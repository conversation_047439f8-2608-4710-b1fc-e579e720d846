from base.config import DatasetConfig, FactorConfig
from dataset.spot import SpotKlineDataset


def spot_kline_config(start_date: str = "", end_date: str = ""):
    return DatasetConfig(
        data_type="kline",
        csv_dir_path="/home/<USER>/projects/crypto_quant/downloads/spot/monthly/klines",
        pqt_file_path="/home/<USER>/projects/crypto_quant/data/spot/klines.parquet",
        start_date=start_date,
        end_date=end_date,
    )


def alpha101_config(start_date: str = "", end_date: str = ""):
    return FactorConfig(
        file_path="/home/<USER>/projects/crypto_quant/data/factor/alpha101.parquet",
        so_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha101/alpha101.so",
        so_stream_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so",
        dataset=SpotKlineDataset(
            spot_kline_config(start_date=start_date, end_date=end_date)
        ),
        window=128,
        start_date=start_date,
        end_date=end_date,
    )


def alpha158_config(start_date: str = "", end_date: str = ""):
    return FactorConfig(
        file_path="/home/<USER>/projects/crypto_quant/data/factor/alpha158.parquet",
        so_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha158/alpha158.so",
        so_stream_path="/home/<USER>/projects/crypto_quant/compiled_factor/alpha158_stream/alpha158_stream.so",
        dataset=SpotKlineDataset(
            spot_kline_config(start_date=start_date, end_date=end_date)
        ),
        window=128,
        start_date=start_date,
        end_date=end_date,
    )
