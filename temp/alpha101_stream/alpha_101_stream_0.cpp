#include <Kun/Context.hpp>
#include <Kun/Module.hpp>
#include <Kun/Ops.hpp>
#include <Kun/Rank.hpp>
#include <Kun/Scale.hpp>
#include <Kun/Ops/Quantile.hpp>


using namespace kun;
using namespace kun::ops;
























static void stage_alpha_101_stream__18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 21> buf_1f0f01f1c83584d6f{__ctx->buffers[88].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_high{__ctx->buffers[1].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_18fdad286b28e4b7b{__ctx->buffers[91].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha023{__ctx->buffers[28].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d5bb7cf49a1cb4a0{__ctx->buffers[93].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_10c16d9a20e0387b3{__ctx->buffers[95].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha006{__ctx->buffers[11].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 250> buf_82b65763b5b2c7e4{__ctx->buffers[105].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_156e62fbc370dca38{__ctx->buffers[99].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14969b303c48d510f{__ctx->buffers[101].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_10156c5755f433df0{__ctx->buffers[103].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16e18489215dc7531{__ctx->buffers[106].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f4055f7b15338dfe{__ctx->buffers[107].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_160b80f2567e1055c{__ctx->buffers[108].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_161e738cf1eba2c95{__ctx->buffers[109].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e2c0a8e1c6ba083a{__ctx->buffers[110].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 50> buf_11883856b1555ef59{__ctx->buffers[111].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1eda8e378b6c06d1a{__ctx->buffers[112].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14a4506c73ffa04a3{__ctx->buffers[113].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a81d3f6084e29607{__ctx->buffers[114].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_93dc794f9443fc2e{__ctx->buffers[115].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19b7389e0c453d803{__ctx->buffers[116].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2c53c8eaac8635f5{__ctx->buffers[117].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15d5de9a420044862{__ctx->buffers[118].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_00adb6936a973911{__ctx->buffers[119].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19f751d58c7a6b64{__ctx->buffers[120].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d97c2770b5a6165f{__ctx->buffers[121].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1ba3ef6f8151bb423{__ctx->buffers[122].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> temp_152{__ctx->buffers[123].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1f0f01f1c83584d6f.step(i);
        auto v1 = Add(v0, v0);
        auto v2 = buf_high.step(i);
        ReduceAdd<float, 8> v5{};
        for(int iter = 19;iter >= 10;iter--) {
            auto v4 = buf_high.getWindow(i, iter);
            v5.step(v4, iter);
        }
        ReduceAdd<float, 8> v8{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v4 = buf_high.getWindow(i, iter);
            v5.step(v4, iter);
            auto v7 = buf_high.getWindow(i, iter);
            v8.step(v7, iter);
        }
        auto v9 = buf_low.step(i);
        auto v10 = Add(v2, v9);
        buf_18fdad286b28e4b7b.store(i, v10);
        auto v11 = v10;
        auto v12 = Mul(v2, v9);
        auto v13 = Sqrt(v12);
        auto v14 = Sub(v9, v2);
        auto v15 = constVec<8>(0.f);
        auto v16 = Equals(v14, v15);
        auto v17 = constVec<8>(0.0001f);
        auto v18 = Select(v16, v17, v14);
        auto v19 = windowedRef<float, 8, 2>(buf_high, i);
        auto v20 = Sub(v2, v19);
        auto v21 = SetInfOrNanToValue(v20, 0.0f);
        auto v22 = Sub(0.f, v21);
        auto v23 = Div(v5, 20.f);
        auto v24 = LessThan(v23, v2);
        auto v25 = Select(v24, v22, v15);
        buf_alpha023.store(i, v25);
        auto v26 = v25;
        auto v27 = windowedRef<float, 8, 1>(buf_high, i);
        auto v28 = buf_open.step(i);
        ReduceAdd<float, 8> v31{};
        for(int iter = 9;iter >= 5;iter--) {
            auto v30 = buf_open.getWindow(i, iter);
            v31.step(v30, iter);
        }
        ReduceAdd<float, 8> v34{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v30 = buf_open.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_open.getWindow(i, iter);
            v34.step(v33, iter);
        }
        auto v35 = Sub(v28, v27);
        buf_1d5bb7cf49a1cb4a0.store(i, v35);
        auto v36 = v35;
        auto v37 = Div(v8, 10.f);
        ReduceAdd<float, 8> v42{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v39 = buf_high.getWindow(i, iter);
            auto v40 = Sub(v39, v37);
            auto v41 = Mul(v40, v40);
            v42.step(v41, iter);
        }
        buf_10c16d9a20e0387b3.store(i, v42);
        auto v43 = v42;
        auto v44 = Sqrt(v42);
        auto v45 = buf_volume.step(i);
        ReduceAdd<float, 8> v48{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v47 = buf_volume.getWindow(i, iter);
            v48.step(v47, iter);
        }
        auto v49 = Div(v48, 10.f);
        ReduceAdd<float, 8> v56{};
        ReduceAdd<float, 8> v58{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v51 = buf_high.getWindow(i, iter);
            auto v52 = buf_volume.getWindow(i, iter);
            auto v53 = Sub(v51, v37);
            auto v54 = Sub(v52, v49);
            auto v55 = Mul(v54, v54);
            v56.step(v55, iter);
            auto v57 = Mul(v53, v54);
            v58.step(v57, iter);
        }
        auto v59 = Sqrt(v56);
        auto v60 = Mul(v44, v59);
        auto v61 = Div(v31, 10.f);
        ReduceAdd<float, 8> v67{};
        ReduceAdd<float, 8> v70{};
        ReduceAdd<float, 8> v72{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v63 = buf_volume.getWindow(i, iter);
            auto v64 = Sub(v63, v49);
            auto v65 = buf_open.getWindow(i, iter);
            auto v66 = Mul(v64, v64);
            v67.step(v66, iter);
            auto v68 = Sub(v65, v61);
            auto v69 = Mul(v68, v64);
            v70.step(v69, iter);
            auto v71 = Mul(v68, v68);
            v72.step(v71, iter);
        }
        auto v73 = Sqrt(v72);
        auto v74 = Sqrt(v67);
        auto v75 = Mul(v73, v74);
        auto v76 = Add(v45, 1e-07f);
        auto v77 = Div(v70, v75);
        auto v78 = Sub(0.f, v77);
        auto v79 = SetInfOrNanToValue(v78, 0.0f);
        buf_alpha006.store(i, v79);
        auto v80 = v79;
        auto v81 = SetInfOrNanToValue(v77, 0.0f);
        auto v82 = buf_close.step(i);
        ReduceAdd<float, 8> v85{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v84 = buf_close.getWindow(i, iter);
            v85.step(v84, iter);
        }
        auto v86 = Div(v85, 10.f);
        ReduceAdd<float, 8> v92{};
        ReduceAdd<float, 8> v95{};
        ReduceAdd<float, 8> v97{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v88 = buf_open.getWindow(i, iter);
            auto v89 = Sub(v88, v61);
            auto v90 = buf_close.getWindow(i, iter);
            auto v91 = Mul(v89, v89);
            v92.step(v91, iter);
            auto v93 = Sub(v90, v86);
            auto v94 = Mul(v93, v89);
            v95.step(v94, iter);
            auto v96 = Mul(v93, v93);
            v97.step(v96, iter);
        }
        auto v98 = Sqrt(v97);
        auto v99 = Sqrt(v92);
        auto v100 = Mul(v98, v99);
        auto v101 = Mul(v82, v82);
        auto v102 = Mul(v101, v101);
        auto v103 = Mul(v82, v102);
        auto v104 = windowedRef<float, 8, 1>(buf_close, i);
        auto v105 = Div(v82, v104);
        auto v106 = Sub(v105, 1.0f);
        auto v107 = LessThan(v106, 0.0f);
        buf_82b65763b5b2c7e4.store(i, v106);
        auto v108 = v106;
        ReduceAdd<float, 8> v111{};
        for(int iter = 249;iter >= 240;iter--) {
            auto v110 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v111.step(v110, iter);
        }
        ReduceAdd<float, 8> v114{};
        for(int iter = 239;iter >= 20;iter--) {
            auto v110 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v111.step(v110, iter);
            auto v113 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v114.step(v113, iter);
        }
        ReduceAdd<float, 8> v117{};
        for(int iter = 19;iter >= 5;iter--) {
            auto v110 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v111.step(v110, iter);
            auto v113 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v114.step(v113, iter);
            auto v116 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v117.step(v116, iter);
        }
        ReduceAdd<float, 8> v120{};
        for(int iter = 4;iter >= 2;iter--) {
            auto v110 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v111.step(v110, iter);
            auto v113 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v114.step(v113, iter);
            auto v116 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v117.step(v116, iter);
            auto v119 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v120.step(v119, iter);
        }
        ReduceAdd<float, 8> v123{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v110 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v111.step(v110, iter);
            auto v113 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v114.step(v113, iter);
            auto v116 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v117.step(v116, iter);
            auto v119 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v120.step(v119, iter);
            auto v122 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v123.step(v122, iter);
        }
        auto v124 = Div(v117, 20.f);
        ReduceAdd<float, 8> v129{};
        for(int iter = 19;iter >= 0;iter--) {
            auto v126 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            auto v127 = Sub(v126, v124);
            auto v128 = Mul(v127, v127);
            v129.step(v128, iter);
        }
        auto v130 = Div(v129, 19.f);
        auto v131 = Sqrt(v130);
        auto v132 = Select(v107, v131, v82);
        ReduceRank<float, 8> v135{v106};
        for(int iter = 31;iter >= 0;iter--) {
            auto v134 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            v135.step(v134, iter);
        }
        auto v136 = windowedRef<float, 8, 3>(buf_82b65763b5b2c7e4, i);
        auto v137 = Sub(v106, v136);
        buf_156e62fbc370dca38.store(i, v137);
        auto v138 = v137;
        auto v139 = Div(v120, 5.f);
        ReduceAdd<float, 8> v144{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v141 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            auto v142 = Sub(v141, v139);
            auto v143 = Mul(v142, v142);
            v144.step(v143, iter);
        }
        auto v145 = Div(v123, 2.f);
        ReduceAdd<float, 8> v150{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v147 = buf_82b65763b5b2c7e4.getWindow(i, iter);
            auto v148 = Sub(v147, v145);
            auto v149 = Mul(v148, v148);
            v150.step(v149, iter);
        }
        auto v151 = Mul(v34, v120);
        temp_152.store(i, v151);
        auto v152 = v151;
        auto v153 = windowedRef<float, 8, 10>(temp_152, i);
        auto v154 = Sub(v151, v153);
        buf_14969b303c48d510f.store(i, v154);
        auto v155 = v154;
        auto v156 = Div(v150, 1.f);
        auto v157 = Sqrt(v156);
        auto v158 = Div(v144, 4.f);
        auto v159 = Sqrt(v158);
        auto v160 = Div(v157, v159);
        auto v161 = SetInfOrNanToValue(v160, 1.0f);
        buf_10156c5755f433df0.store(i, v161);
        auto v162 = v161;
        buf_16e18489215dc7531.store(i, v111);
        auto v163 = v111;
        buf_f4055f7b15338dfe.store(i, v104);
        auto v164 = v104;
        buf_160b80f2567e1055c.store(i, v76);
        auto v165 = v76;
        buf_161e738cf1eba2c95.store(i, v114);
        auto v166 = v114;
        buf_e2c0a8e1c6ba083a.store(i, v117);
        auto v167 = v117;
        buf_11883856b1555ef59.store(i, v49);
        auto v168 = v49;
        buf_1eda8e378b6c06d1a.store(i, v13);
        auto v169 = v13;
        buf_14a4506c73ffa04a3.store(i, v95);
        auto v170 = v95;
        buf_1a81d3f6084e29607.store(i, v100);
        auto v171 = v100;
        buf_93dc794f9443fc2e.store(i, v81);
        auto v172 = v81;
        buf_19b7389e0c453d803.store(i, v135);
        auto v173 = v135;
        buf_2c53c8eaac8635f5.store(i, v132);
        auto v174 = v132;
        buf_15d5de9a420044862.store(i, v18);
        auto v175 = v18;
        buf_00adb6936a973911.store(i, v103);
        auto v176 = v103;
        buf_19f751d58c7a6b64.store(i, v58);
        auto v177 = v58;
        buf_1d97c2770b5a6165f.store(i, v60);
        auto v178 = v60;
        buf_1ba3ef6f8151bb423.store(i, v1);
        auto v179 = v1;
    }
}















static void stage_alpha_101_stream__alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_2edba89328237aeb{__ctx->buffers[124].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f4055f7b15338dfe{__ctx->buffers[107].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha019{__ctx->buffers[24].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> buf_78d7937a0020cd3f{__ctx->buffers[137].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_be958f2e6e459f60{__ctx->buffers[125].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14ab5e930c67d541b{__ctx->buffers[127].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_9d962035ba5f1ea4{__ctx->buffers[129].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_077b734a024fd813{__ctx->buffers[131].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha012{__ctx->buffers[17].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_a16e007555996c6e{__ctx->buffers[138].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> buf_106499afd0abdce0c{__ctx->buffers[133].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16fd514be19f48a38{__ctx->buffers[135].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16aad41bb0d085fc8{__ctx->buffers[139].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha007{__ctx->buffers[12].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha021{__ctx->buffers[26].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha010{__ctx->buffers[15].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha046{__ctx->buffers[51].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha049{__ctx->buffers[53].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha051{__ctx->buffers[55].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha009{__ctx->buffers[14].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_eca4f6b3acf43b21{__ctx->buffers[140].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_118ca723e22053d82{__ctx->buffers[141].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 22> buf_bf8995d8792205e7{__ctx->buffers[142].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f4bf06972d9be22e{__ctx->buffers[143].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_b1ef8918ee2f42d9{__ctx->buffers[144].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2b4119829ffbe8ca{__ctx->buffers[145].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_68d8962d72605382{__ctx->buffers[146].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1da3294008ba11157{__ctx->buffers[147].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b968497d4dea7e45{__ctx->buffers[148].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1821f4158341eaadd{__ctx->buffers[149].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1dcccc562c26e18cc{__ctx->buffers[150].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e2149fed4461a684{__ctx->buffers[151].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 60> temp_21{__ctx->buffers[152].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> temp_135{__ctx->buffers[153].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_162{__ctx->buffers[154].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> temp_180{__ctx->buffers[155].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_2edba89328237aeb.step(i);
        auto v1 = Add(v0, 1.f);
        auto v2 = buf_close.step(i);
        ReduceAdd<float, 8> v5{};
        for(int iter = 199;iter >= 8;iter--) {
            auto v4 = buf_close.getWindow(i, iter);
            v5.step(v4, iter);
        }
        ReduceAdd<float, 8> v8{};
        for(int iter = 7;iter >= 2;iter--) {
            auto v4 = buf_close.getWindow(i, iter);
            v5.step(v4, iter);
            auto v7 = buf_close.getWindow(i, iter);
            v8.step(v7, iter);
        }
        ReduceAdd<float, 8> v11{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v4 = buf_close.getWindow(i, iter);
            v5.step(v4, iter);
            auto v7 = buf_close.getWindow(i, iter);
            v8.step(v7, iter);
            auto v10 = buf_close.getWindow(i, iter);
            v11.step(v10, iter);
        }
        auto v12 = windowedRef<float, 8, 7>(buf_close, i);
        auto v13 = Sub(v2, v12);
        auto v14 = Add(v13, v13);
        auto v15 = Sign(v14);
        auto v16 = Sub(0.f, v15);
        auto v17 = Mul(v16, v1);
        buf_alpha019.store(i, v17);
        auto v18 = v17;
        auto v19 = Sign(v13);
        auto v20 = Abs(v13);
        temp_21.store(i, v20);
        auto v21 = v20;
        ReduceRank<float, 8> v24{v20};
        for(int iter = 59;iter >= 0;iter--) {
            auto v23 = temp_21.getWindow(i, iter);
            v24.step(v23, iter);
        }
        auto v25 = Mul(v24, v19);
        auto v26 = Sub(0.f, v25);
        auto v27 = buf_volume.step(i);
        ReduceAdd<float, 8> v30{};
        for(int iter = 19;iter >= 5;iter--) {
            auto v29 = buf_volume.getWindow(i, iter);
            v30.step(v29, iter);
        }
        ReduceAdd<float, 8> v33{};
        for(int iter = 4;iter >= 2;iter--) {
            auto v29 = buf_volume.getWindow(i, iter);
            v30.step(v29, iter);
            auto v32 = buf_volume.getWindow(i, iter);
            v33.step(v32, iter);
        }
        ReduceAdd<float, 8> v36{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v29 = buf_volume.getWindow(i, iter);
            v30.step(v29, iter);
            auto v32 = buf_volume.getWindow(i, iter);
            v33.step(v32, iter);
            auto v35 = buf_volume.getWindow(i, iter);
            v36.step(v35, iter);
        }
        auto v37 = Div(v30, 20.f);
        auto v38 = GreaterEqual(v37, v27);
        auto v39 = constVec<8>(-1.f);
        auto v40 = Select(v38, v39, v26);
        auto v41 = GreaterEqual(v27, v37);
        auto v42 = Div(v8, 8.f);
        ReduceAdd<float, 8> v47{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v44 = buf_close.getWindow(i, iter);
            auto v45 = Sub(v44, v42);
            auto v46 = Mul(v45, v45);
            v47.step(v46, iter);
        }
        auto v48 = Div(v47, 7.f);
        auto v49 = Sqrt(v48);
        auto v50 = Sub(v42, v49);
        auto v51 = Div(v11, 2.f);
        auto v52 = LessThan(v51, v50);
        auto v53 = Or(v52, v41);
        auto v54 = Add(v42, v49);
        auto v55 = LessThan(v54, v51);
        auto v56 = Not(v55);
        auto v57 = And(v56, v53);
        auto v58 = constVec<8>(1.f);
        auto v59 = Select(v57, v58, v39);
        auto v60 = Div(v27, v37);
        buf_78d7937a0020cd3f.store(i, v60);
        auto v61 = v60;
        ReduceDecayLinear<float, 8, 9> v64{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v63 = buf_78d7937a0020cd3f.getWindow(i, iter);
            v64.step(v63, iter);
        }
        buf_be958f2e6e459f60.store(i, v64);
        auto v65 = v64;
        auto v66 = Div(1.f, v2);
        buf_14ab5e930c67d541b.store(i, v66);
        auto v67 = v66;
        auto v68 = Div(v36, 2.f);
        ReduceAdd<float, 8> v74{};
        ReduceAdd<float, 8> v77{};
        ReduceAdd<float, 8> v79{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v70 = buf_close.getWindow(i, iter);
            auto v71 = Sub(v70, v51);
            auto v72 = buf_volume.getWindow(i, iter);
            auto v73 = Mul(v71, v71);
            v74.step(v73, iter);
            auto v75 = Sub(v72, v68);
            auto v76 = Mul(v71, v75);
            v77.step(v76, iter);
            auto v78 = Mul(v75, v75);
            v79.step(v78, iter);
        }
        auto v80 = Sqrt(v79);
        auto v81 = Sqrt(v74);
        auto v82 = Mul(v81, v80);
        auto v83 = Div(v33, v30);
        auto v84 = SetInfOrNanToValue(v83, 1.f);
        auto v85 = buf_f4055f7b15338dfe.step(i);
        auto v86 = Sub(v2, v85);
        buf_9d962035ba5f1ea4.store(i, v86);
        auto v87 = v86;
        ReduceMax<float, 8> v90{};
        ReduceMin<float, 8> v91{};
        for(int iter = 4;iter >= 4;iter--) {
            auto v89 = buf_9d962035ba5f1ea4.getWindow(i, iter);
            v90.step(v89, iter);
            v91.step(v89, iter);
        }
        ReduceMax<float, 8> v94{};
        ReduceMin<float, 8> v95{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v89 = buf_9d962035ba5f1ea4.getWindow(i, iter);
            v90.step(v89, iter);
            v91.step(v89, iter);
            auto v93 = buf_9d962035ba5f1ea4.getWindow(i, iter);
            v94.step(v93, iter);
            v95.step(v93, iter);
        }
        auto v96 = constVec<8>(0.f);
        auto v97 = LessThan(v94, v96);
        auto v98 = GreaterThan(v95, v96);
        auto v99 = Or(v98, v97);
        auto v100 = Sub(0.f, v86);
        auto v101 = Select(v99, v86, v100);
        auto v102 = windowedRef<float, 8, 20>(buf_close, i);
        auto v103 = windowedRef<float, 8, 10>(buf_close, i);
        auto v104 = Sub(v103, v2);
        auto v105 = Div(v104, 10.f);
        auto v106 = Sub(v102, v103);
        auto v107 = Div(v106, 10.f);
        auto v108 = Sub(v107, v105);
        auto v109 = LessThan(v108, -0.05f);
        auto v110 = LessThan(v108, -0.1f);
        auto v111 = LessThan(v108, 0.f);
        auto v112 = Select(v111, v58, v100);
        auto v113 = constVec<8>(0.25f);
        auto v114 = GreaterThan(v108, v113);
        auto v115 = Select(v114, v39, v112);
        auto v116 = Select(v110, v58, v100);
        auto v117 = Select(v109, v58, v100);
        auto v118 = LessThan(v90, v96);
        auto v119 = GreaterThan(v91, v96);
        auto v120 = Or(v119, v118);
        auto v121 = Select(v120, v86, v100);
        auto v122 = windowedRef<float, 8, 1>(buf_9d962035ba5f1ea4, i);
        auto v123 = Sign(v122);
        auto v124 = Sign(v86);
        auto v125 = Add(v124, v123);
        auto v126 = windowedRef<float, 8, 2>(buf_9d962035ba5f1ea4, i);
        auto v127 = Sign(v126);
        auto v128 = Add(v125, v127);
        buf_077b734a024fd813.store(i, v128);
        auto v129 = v128;
        auto v130 = windowedRef<float, 8, 1>(buf_volume, i);
        auto v131 = Sub(v27, v130);
        auto v132 = Sign(v131);
        auto v133 = Mul(v132, v100);
        buf_alpha012.store(i, v133);
        auto v134 = v133;
        temp_135.store(i, v130);
        auto v135 = v130;
        auto v136 = buf_open.step(i);
        auto v137 = Sub(v2, v136);
        buf_a16e007555996c6e.store(i, v137);
        auto v138 = v137;
        ReduceAdd<float, 8> v141{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v140 = buf_a16e007555996c6e.getWindow(i, iter);
            v141.step(v140, iter);
        }
        auto v142 = Div(v141, 15.f);
        ReduceAdd<float, 8> v145{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v144 = temp_135.getWindow(i, iter);
            v145.step(v144, iter);
        }
        auto v146 = Div(v145, 15.f);
        ReduceAdd<float, 8> v152{};
        ReduceAdd<float, 8> v155{};
        ReduceAdd<float, 8> v157{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v148 = temp_135.getWindow(i, iter);
            auto v149 = buf_a16e007555996c6e.getWindow(i, iter);
            auto v150 = Sub(v148, v146);
            auto v151 = Mul(v150, v150);
            v152.step(v151, iter);
            auto v153 = Sub(v149, v142);
            auto v154 = Mul(v153, v150);
            v155.step(v154, iter);
            auto v156 = Mul(v153, v153);
            v157.step(v156, iter);
        }
        auto v158 = Sqrt(v157);
        auto v159 = Sqrt(v152);
        auto v160 = Mul(v158, v159);
        auto v161 = Abs(v137);
        temp_162.store(i, v161);
        auto v162 = v161;
        ReduceAdd<float, 8> v165{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v164 = temp_162.getWindow(i, iter);
            v165.step(v164, iter);
        }
        auto v166 = Div(v165, 5.f);
        ReduceAdd<float, 8> v171{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v168 = temp_162.getWindow(i, iter);
            auto v169 = Sub(v168, v166);
            auto v170 = Mul(v169, v169);
            v171.step(v170, iter);
        }
        auto v172 = Div(v171, 4.f);
        auto v173 = Sqrt(v172);
        auto v174 = Div(v5, 200.f);
        auto v175 = Div(v174, 200.f);
        auto v176 = Sub(v175, v136);
        auto v177 = Sub(v136, v2);
        buf_106499afd0abdce0c.store(i, v177);
        auto v178 = v177;
        auto v179 = windowedRef<float, 8, 1>(buf_106499afd0abdce0c, i);
        temp_180.store(i, v179);
        auto v180 = v179;
        ReduceAdd<float, 8> v183{};
        for(int iter = 199;iter >= 0;iter--) {
            auto v182 = temp_180.getWindow(i, iter);
            v183.step(v182, iter);
        }
        auto v184 = Div(v183, 200.f);
        ReduceAdd<float, 8> v190{};
        ReduceAdd<float, 8> v193{};
        ReduceAdd<float, 8> v195{};
        for(int iter = 199;iter >= 0;iter--) {
            auto v186 = buf_close.getWindow(i, iter);
            auto v187 = Sub(v186, v174);
            auto v188 = temp_180.getWindow(i, iter);
            auto v189 = Mul(v187, v187);
            v190.step(v189, iter);
            auto v191 = Sub(v188, v184);
            auto v192 = Mul(v191, v187);
            v193.step(v192, iter);
            auto v194 = Mul(v191, v191);
            v195.step(v194, iter);
        }
        auto v196 = Sqrt(v195);
        auto v197 = Sqrt(v190);
        auto v198 = Mul(v196, v197);
        auto v199 = Div(v193, v198);
        buf_16fd514be19f48a38.store(i, v199);
        auto v200 = v199;
        auto v201 = Mul(v176, v137);
        buf_16aad41bb0d085fc8.store(i, v201);
        auto v202 = v201;
        buf_alpha007.store(i, v40);
        auto v203 = v40;
        buf_alpha021.store(i, v59);
        auto v204 = v59;
        buf_alpha010.store(i, v101);
        auto v205 = v101;
        buf_alpha046.store(i, v115);
        auto v206 = v115;
        buf_alpha049.store(i, v116);
        auto v207 = v116;
        buf_alpha051.store(i, v117);
        auto v208 = v117;
        buf_alpha009.store(i, v121);
        auto v209 = v121;
        buf_eca4f6b3acf43b21.store(i, v155);
        auto v210 = v155;
        buf_118ca723e22053d82.store(i, v160);
        auto v211 = v160;
        buf_bf8995d8792205e7.store(i, v37);
        auto v212 = v37;
        buf_f4bf06972d9be22e.store(i, v103);
        auto v213 = v103;
        buf_b1ef8918ee2f42d9.store(i, v33);
        auto v214 = v33;
        buf_2b4119829ffbe8ca.store(i, v173);
        auto v215 = v173;
        buf_68d8962d72605382.store(i, v84);
        auto v216 = v84;
        buf_1da3294008ba11157.store(i, v122);
        auto v217 = v122;
        buf_1b968497d4dea7e45.store(i, v77);
        auto v218 = v77;
        buf_1821f4158341eaadd.store(i, v82);
        auto v219 = v82;
        buf_1dcccc562c26e18cc.store(i, v13);
        auto v220 = v13;
        buf_e2149fed4461a684.store(i, v1);
        auto v221 = v1;
    }
}

















static void stage_alpha_101_stream__493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_26a1c5dfd27db7bb{__ctx->buffers[156].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_54a7c757d1b2deb5{__ctx->buffers[134].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_eca4f6b3acf43b21{__ctx->buffers[140].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_118ca723e22053d82{__ctx->buffers[141].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 22> buf_bf8995d8792205e7{__ctx->buffers[142].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_amount{__ctx->buffers[4].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_160b80f2567e1055c{__ctx->buffers[108].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f4bf06972d9be22e{__ctx->buffers[143].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_493c5c7c2e79a37f{__ctx->buffers[157].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_10bf2e96da60b4371{__ctx->buffers[161].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f3d21de30288a87{__ctx->buffers[163].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14aa873133ad7c9f4{__ctx->buffers[165].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_fa408c94db08823f{__ctx->buffers[167].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha084{__ctx->buffers[77].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ae449937b099b289{__ctx->buffers[169].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_54c72aeffc3e3508{__ctx->buffers[171].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_7bba8c2207236de9{__ctx->buffers[172].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_3eda254c3f952022{__ctx->buffers[173].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b087cce3aa0b937a{__ctx->buffers[174].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_1dc772caca00e1584{__ctx->buffers[175].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cd0c872219e5d6d0{__ctx->buffers[176].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_89deb5a8070c6bf4{__ctx->buffers[177].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_82dce2338e7ee89f{__ctx->buffers[178].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_1b38965bcd2746ceb{__ctx->buffers[179].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c062f9bb82def253{__ctx->buffers[180].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_4f68c121edd4b69a{__ctx->buffers[181].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_18644381e8646210a{__ctx->buffers[182].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12db32b8edf3abd9c{__ctx->buffers[183].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_114be7640278a0027{__ctx->buffers[184].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_a998c0027e40f4c6{__ctx->buffers[185].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_662df1dc40577e1e{__ctx->buffers[186].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_90a31d273592c40c{__ctx->buffers[187].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e8ef8c4a12962781{__ctx->buffers[188].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_75e672355a4f049c{__ctx->buffers[189].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1ac920ea885d1ed2b{__ctx->buffers[190].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16599f4652129dcaa{__ctx->buffers[191].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_6722cd923f64335c{__ctx->buffers[192].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_196f4b404382c7357{__ctx->buffers[193].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 10> temp_72{__ctx->buffers[194].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> temp_127{__ctx->buffers[195].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_197{__ctx->buffers[196].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> temp_219{__ctx->buffers[197].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> temp_246{__ctx->buffers[198].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 21> temp_260{__ctx->buffers[199].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_26a1c5dfd27db7bb.step(i);
        auto v1 = Mul(0.6f, v0);
        auto v2 = buf_54a7c757d1b2deb5.step(i);
        auto v3 = Mul(0.7f, v2);
        auto v4 = buf_eca4f6b3acf43b21.step(i);
        auto v5 = buf_118ca723e22053d82.step(i);
        auto v6 = Div(v4, v5);
        buf_493c5c7c2e79a37f.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_bf8995d8792205e7.step(i);
        ReduceAdd<float, 8> v11{};
        for(int iter = 21;iter >= 15;iter--) {
            auto v10 = buf_bf8995d8792205e7.getWindow(i, iter);
            v11.step(v10, iter);
        }
        ReduceAdd<float, 8> v14{};
        for(int iter = 14;iter >= 12;iter--) {
            auto v10 = buf_bf8995d8792205e7.getWindow(i, iter);
            v11.step(v10, iter);
            auto v13 = buf_bf8995d8792205e7.getWindow(i, iter);
            v14.step(v13, iter);
        }
        ReduceAdd<float, 8> v17{};
        for(int iter = 11;iter >= 6;iter--) {
            auto v10 = buf_bf8995d8792205e7.getWindow(i, iter);
            v11.step(v10, iter);
            auto v13 = buf_bf8995d8792205e7.getWindow(i, iter);
            v14.step(v13, iter);
            auto v16 = buf_bf8995d8792205e7.getWindow(i, iter);
            v17.step(v16, iter);
        }
        ReduceAdd<float, 8> v20{};
        for(int iter = 5;iter >= 5;iter--) {
            auto v10 = buf_bf8995d8792205e7.getWindow(i, iter);
            v11.step(v10, iter);
            auto v13 = buf_bf8995d8792205e7.getWindow(i, iter);
            v14.step(v13, iter);
            auto v16 = buf_bf8995d8792205e7.getWindow(i, iter);
            v17.step(v16, iter);
            auto v19 = buf_bf8995d8792205e7.getWindow(i, iter);
            v20.step(v19, iter);
        }
        ReduceAdd<float, 8> v23{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v10 = buf_bf8995d8792205e7.getWindow(i, iter);
            v11.step(v10, iter);
            auto v13 = buf_bf8995d8792205e7.getWindow(i, iter);
            v14.step(v13, iter);
            auto v16 = buf_bf8995d8792205e7.getWindow(i, iter);
            v17.step(v16, iter);
            auto v19 = buf_bf8995d8792205e7.getWindow(i, iter);
            v20.step(v19, iter);
            auto v22 = buf_bf8995d8792205e7.getWindow(i, iter);
            v23.step(v22, iter);
        }
        auto v24 = Div(v20, 6.f);
        auto v25 = buf_amount.step(i);
        auto v26 = buf_160b80f2567e1055c.step(i);
        auto v27 = Div(v25, v26);
        buf_e0e3079c4a901fa8.store(i, v27);
        auto v28 = v27;
        ReduceAdd<float, 8> v31{};
        for(int iter = 229;iter >= 18;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
        }
        ReduceAdd<float, 8> v34{};
        for(int iter = 17;iter >= 16;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
        }
        ReduceMin<float, 8> v37{};
        for(int iter = 15;iter >= 15;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
        }
        ReduceMax<float, 8> v40{};
        for(int iter = 14;iter >= 12;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
        }
        ReduceMin<float, 8> v43{};
        for(int iter = 11;iter >= 10;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
        }
        ReduceAdd<float, 8> v46{};
        for(int iter = 9;iter >= 8;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v46.step(v45, iter);
        }
        ReduceAdd<float, 8> v49{};
        for(int iter = 7;iter >= 6;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v46.step(v45, iter);
            auto v48 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v49.step(v48, iter);
        }
        ReduceAdd<float, 8> v52{};
        for(int iter = 5;iter >= 5;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v46.step(v45, iter);
            auto v48 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v49.step(v48, iter);
            auto v51 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v52.step(v51, iter);
        }
        ReduceAdd<float, 8> v55{};
        for(int iter = 4;iter >= 4;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v46.step(v45, iter);
            auto v48 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v49.step(v48, iter);
            auto v51 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v52.step(v51, iter);
            auto v54 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v55.step(v54, iter);
        }
        ReduceAdd<float, 8> v58{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v30 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v31.step(v30, iter);
            auto v33 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v34.step(v33, iter);
            auto v36 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v37.step(v36, iter);
            auto v39 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v40.step(v39, iter);
            auto v42 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v46.step(v45, iter);
            auto v48 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v49.step(v48, iter);
            auto v51 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v52.step(v51, iter);
            auto v54 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v55.step(v54, iter);
            auto v57 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v58.step(v57, iter);
        }
        auto v59 = Div(v52, 6.f);
        ReduceAdd<float, 8> v65{};
        ReduceAdd<float, 8> v68{};
        ReduceAdd<float, 8> v70{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v61 = buf_bf8995d8792205e7.getWindow(i, iter);
            auto v62 = Sub(v61, v24);
            auto v63 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v64 = Mul(v62, v62);
            v65.step(v64, iter);
            auto v66 = Sub(v63, v59);
            auto v67 = Mul(v66, v62);
            v68.step(v67, iter);
            auto v69 = Mul(v66, v66);
            v70.step(v69, iter);
        }
        auto v71 = Div(v11, 22.f);
        temp_72.store(i, v71);
        auto v72 = v71;
        ReduceAdd<float, 8> v75{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v74 = temp_72.getWindow(i, iter);
            v75.step(v74, iter);
        }
        auto v76 = Div(v75, 10.f);
        auto v77 = Div(v46, 10.f);
        ReduceAdd<float, 8> v83{};
        ReduceAdd<float, 8> v86{};
        ReduceAdd<float, 8> v88{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v79 = temp_72.getWindow(i, iter);
            auto v80 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v81 = Sub(v80, v77);
            auto v82 = Mul(v81, v81);
            v83.step(v82, iter);
            auto v84 = Sub(v79, v76);
            auto v85 = Mul(v81, v84);
            v86.step(v85, iter);
            auto v87 = Mul(v84, v84);
            v88.step(v87, iter);
        }
        auto v89 = Sqrt(v70);
        auto v90 = Sqrt(v65);
        auto v91 = Mul(v89, v90);
        ReduceRank<float, 8> v94{v27};
        for(int iter = 19;iter >= 0;iter--) {
            auto v93 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v94.step(v93, iter);
        }
        ReduceRank<float, 8> v97{v27};
        for(int iter = 3;iter >= 0;iter--) {
            auto v96 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            v97.step(v96, iter);
        }
        auto v98 = Div(v68, v91);
        auto v99 = Abs(v98);
        buf_10bf2e96da60b4371.store(i, v99);
        auto v100 = v99;
        auto v101 = Sqrt(v88);
        auto v102 = Sqrt(v83);
        auto v103 = Mul(v102, v101);
        auto v104 = windowedRef<float, 8, 4>(buf_e0e3079c4a901fa8, i);
        auto v105 = windowedRef<float, 8, 5>(buf_e0e3079c4a901fa8, i);
        auto v106 = buf_volume.step(i);
        ReduceAdd<float, 8> v109{};
        for(int iter = 179;iter >= 4;iter--) {
            auto v108 = buf_volume.getWindow(i, iter);
            v109.step(v108, iter);
        }
        ReduceAdd<float, 8> v112{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v108 = buf_volume.getWindow(i, iter);
            v109.step(v108, iter);
            auto v111 = buf_volume.getWindow(i, iter);
            v112.step(v111, iter);
        }
        auto v113 = Div(v112, 4.f);
        auto v114 = Div(v58, 4.f);
        ReduceAdd<float, 8> v120{};
        ReduceAdd<float, 8> v123{};
        ReduceAdd<float, 8> v125{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v116 = buf_volume.getWindow(i, iter);
            auto v117 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v118 = Sub(v117, v114);
            auto v119 = Mul(v118, v118);
            v120.step(v119, iter);
            auto v121 = Sub(v116, v113);
            auto v122 = Mul(v118, v121);
            v123.step(v122, iter);
            auto v124 = Mul(v121, v121);
            v125.step(v124, iter);
        }
        auto v126 = Div(v109, 180.f);
        temp_127.store(i, v126);
        auto v127 = v126;
        ReduceAdd<float, 8> v130{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v129 = temp_127.getWindow(i, iter);
            v130.step(v129, iter);
        }
        auto v131 = Div(v130, 18.f);
        auto v132 = Div(v34, 18.f);
        ReduceAdd<float, 8> v138{};
        ReduceAdd<float, 8> v141{};
        ReduceAdd<float, 8> v143{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v134 = temp_127.getWindow(i, iter);
            auto v135 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v136 = Sub(v134, v131);
            auto v137 = Mul(v136, v136);
            v138.step(v137, iter);
            auto v139 = Sub(v135, v132);
            auto v140 = Mul(v139, v136);
            v141.step(v140, iter);
            auto v142 = Mul(v139, v139);
            v143.step(v142, iter);
        }
        ReduceRank<float, 8> v146{v126};
        for(int iter = 11;iter >= 0;iter--) {
            auto v145 = temp_127.getWindow(i, iter);
            v146.step(v145, iter);
        }
        auto v147 = Sqrt(v143);
        auto v148 = Sqrt(v138);
        auto v149 = Mul(v147, v148);
        auto v150 = Sqrt(v125);
        auto v151 = Sqrt(v120);
        auto v152 = Mul(v151, v150);
        auto v153 = buf_low.step(i);
        ReduceAdd<float, 8> v156{};
        for(int iter = 11;iter >= 5;iter--) {
            auto v155 = buf_low.getWindow(i, iter);
            v156.step(v155, iter);
        }
        ReduceAdd<float, 8> v159{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v155 = buf_low.getWindow(i, iter);
            v156.step(v155, iter);
            auto v158 = buf_low.getWindow(i, iter);
            v159.step(v158, iter);
        }
        auto v160 = Div(v156, 12.f);
        auto v161 = Div(v17, 12.f);
        ReduceAdd<float, 8> v167{};
        ReduceAdd<float, 8> v170{};
        ReduceAdd<float, 8> v172{};
        for(int iter = 11;iter >= 0;iter--) {
            auto v163 = buf_low.getWindow(i, iter);
            auto v164 = buf_bf8995d8792205e7.getWindow(i, iter);
            auto v165 = Sub(v164, v161);
            auto v166 = Mul(v165, v165);
            v167.step(v166, iter);
            auto v168 = Sub(v163, v160);
            auto v169 = Mul(v165, v168);
            v170.step(v169, iter);
            auto v171 = Mul(v168, v168);
            v172.step(v171, iter);
        }
        auto v173 = Sqrt(v172);
        auto v174 = Sqrt(v167);
        auto v175 = Mul(v174, v173);
        auto v176 = Div(v159, 5.f);
        auto v177 = Div(v23, 5.f);
        ReduceAdd<float, 8> v183{};
        ReduceAdd<float, 8> v186{};
        ReduceAdd<float, 8> v188{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v179 = buf_low.getWindow(i, iter);
            auto v180 = buf_bf8995d8792205e7.getWindow(i, iter);
            auto v181 = Sub(v180, v177);
            auto v182 = Mul(v181, v181);
            v183.step(v182, iter);
            auto v184 = Sub(v179, v176);
            auto v185 = Mul(v181, v184);
            v186.step(v185, iter);
            auto v187 = Mul(v184, v184);
            v188.step(v187, iter);
        }
        auto v189 = Sqrt(v188);
        auto v190 = Sqrt(v183);
        auto v191 = Mul(v190, v189);
        auto v192 = Mul(v153, 0.03366999999999998f);
        auto v193 = Mul(v153, 0.96633f);
        auto v194 = Add(v193, v192);
        auto v195 = Sub(v194, v27);
        auto v196 = Div(v14, 15.f);
        temp_197.store(i, v196);
        auto v197 = v196;
        ReduceAdd<float, 8> v200{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v199 = temp_197.getWindow(i, iter);
            v200.step(v199, iter);
        }
        auto v201 = buf_close.step(i);
        ReduceAdd<float, 8> v204{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v203 = buf_close.getWindow(i, iter);
            v204.step(v203, iter);
        }
        auto v205 = Div(v204, 6.f);
        auto v206 = Div(v200, 6.f);
        ReduceAdd<float, 8> v212{};
        ReduceAdd<float, 8> v215{};
        ReduceAdd<float, 8> v217{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v208 = temp_197.getWindow(i, iter);
            auto v209 = buf_close.getWindow(i, iter);
            auto v210 = Sub(v208, v206);
            auto v211 = Mul(v210, v210);
            v212.step(v211, iter);
            auto v213 = Sub(v209, v205);
            auto v214 = Mul(v213, v210);
            v215.step(v214, iter);
            auto v216 = Mul(v213, v213);
            v217.step(v216, iter);
        }
        auto v218 = windowedRef<float, 8, 5>(buf_close, i);
        temp_219.store(i, v218);
        auto v219 = v218;
        ReduceAdd<float, 8> v222{};
        for(int iter = 229;iter >= 20;iter--) {
            auto v221 = temp_219.getWindow(i, iter);
            v222.step(v221, iter);
        }
        ReduceAdd<float, 8> v225{};
        for(int iter = 19;iter >= 0;iter--) {
            auto v221 = temp_219.getWindow(i, iter);
            v222.step(v221, iter);
            auto v224 = temp_219.getWindow(i, iter);
            v225.step(v224, iter);
        }
        auto v226 = Div(v31, 230.f);
        auto v227 = Div(v222, 230.f);
        ReduceAdd<float, 8> v233{};
        ReduceAdd<float, 8> v236{};
        ReduceAdd<float, 8> v238{};
        for(int iter = 229;iter >= 0;iter--) {
            auto v229 = temp_219.getWindow(i, iter);
            auto v230 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v231 = Sub(v229, v227);
            auto v232 = Mul(v231, v231);
            v233.step(v232, iter);
            auto v234 = Sub(v230, v226);
            auto v235 = Mul(v234, v231);
            v236.step(v235, iter);
            auto v237 = Mul(v234, v234);
            v238.step(v237, iter);
        }
        auto v239 = Sqrt(v238);
        auto v240 = Sqrt(v233);
        auto v241 = Mul(v239, v240);
        auto v242 = Sqrt(v217);
        auto v243 = Sqrt(v212);
        auto v244 = Mul(v242, v243);
        auto v245 = Div(v215, v244);
        temp_246.store(i, v245);
        auto v246 = v245;
        ReduceRank<float, 8> v249{v245};
        for(int iter = 19;iter >= 0;iter--) {
            auto v248 = temp_246.getWindow(i, iter);
            v249.step(v248, iter);
        }
        auto v250 = buf_open.step(i);
        auto v251 = Add(v27, v250);
        auto v252 = Add(v250, v201);
        auto v253 = Sub(v252, v251);
        buf_1f3d21de30288a87.store(i, v253);
        auto v254 = v253;
        buf_14aa873133ad7c9f4.store(i, v225);
        auto v255 = v225;
        auto v256 = Div(v236, v241);
        buf_fa408c94db08823f.store(i, v256);
        auto v257 = v256;
        auto v258 = Sub(v201, v218);
        auto v259 = Sub(v27, v40);
        temp_260.store(i, v259);
        auto v260 = v259;
        ReduceRank<float, 8> v263{v259};
        for(int iter = 20;iter >= 0;iter--) {
            auto v262 = temp_260.getWindow(i, iter);
            v263.step(v262, iter);
        }
        auto v264 = LogFast(v263);
        auto v265 = Mul(v258, v264);
        auto v266 = Exp(v265);
        buf_alpha084.store(i, v266);
        auto v267 = v266;
        auto v268 = Div(v186, v191);
        auto v269 = SetInfOrNanToValue(v268, 0.0f);
        auto v270 = Div(v170, v175);
        auto v271 = SetInfOrNanToValue(v270, 0.0f);
        auto v272 = Sign(v271);
        auto v273 = buf_f4bf06972d9be22e.step(i);
        auto v274 = Sub(v201, v273);
        buf_ae449937b099b289.store(i, v274);
        auto v275 = v274;
        auto v276 = windowedRef<float, 8, 3>(buf_close, i);
        auto v277 = Sub(v201, v276);
        auto v278 = Sub(0.f, v277);
        buf_54c72aeffc3e3508.store(i, v278);
        auto v279 = v278;
        buf_7bba8c2207236de9.store(i, v249);
        auto v280 = v249;
        buf_3eda254c3f952022.store(i, v77);
        auto v281 = v77;
        buf_1b087cce3aa0b937a.store(i, v55);
        auto v282 = v55;
        buf_1dc772caca00e1584.store(i, v146);
        auto v283 = v146;
        buf_cd0c872219e5d6d0.store(i, v195);
        auto v284 = v195;
        buf_89deb5a8070c6bf4.store(i, v104);
        auto v285 = v104;
        buf_82dce2338e7ee89f.store(i, v97);
        auto v286 = v97;
        buf_1b38965bcd2746ceb.store(i, v94);
        auto v287 = v94;
        buf_c062f9bb82def253.store(i, v43);
        auto v288 = v43;
        buf_4f68c121edd4b69a.store(i, v269);
        auto v289 = v269;
        buf_18644381e8646210a.store(i, v272);
        auto v290 = v272;
        buf_12db32b8edf3abd9c.store(i, v49);
        auto v291 = v49;
        buf_114be7640278a0027.store(i, v123);
        auto v292 = v123;
        buf_a998c0027e40f4c6.store(i, v152);
        auto v293 = v152;
        buf_662df1dc40577e1e.store(i, v141);
        auto v294 = v141;
        buf_90a31d273592c40c.store(i, v149);
        auto v295 = v149;
        buf_e8ef8c4a12962781.store(i, v105);
        auto v296 = v105;
        buf_75e672355a4f049c.store(i, v3);
        auto v297 = v3;
        buf_1ac920ea885d1ed2b.store(i, v1);
        auto v298 = v1;
        buf_16599f4652129dcaa.store(i, v86);
        auto v299 = v86;
        buf_6722cd923f64335c.store(i, v103);
        auto v300 = v103;
        buf_196f4b404382c7357.store(i, v37);
        auto v301 = v37;
    }
}

















static void stage_alpha_101_stream__alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_7bba8c2207236de9{__ctx->buffers[172].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12e33a12fe6cb8a9{__ctx->buffers[164].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16f2a300b18e43c49{__ctx->buffers[170].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_84f75b98293a92f1{__ctx->buffers[168].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha086{__ctx->buffers[79].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_4599ae1c53681419{__ctx->buffers[201].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d6c2cd9d0a42eb53{__ctx->buffers[203].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_122a8870451b838e7{__ctx->buffers[205].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19bcab12c0b751190{__ctx->buffers[207].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e5741f7564c392ac{__ctx->buffers[209].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_350c605085b2027e{__ctx->buffers[211].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f2d443115bbe19b8{__ctx->buffers[213].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> buf_122a8a80286874cc8{__ctx->buffers[215].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1209b15d3b8ee8a84{__ctx->buffers[216].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_9dd1ca3b1e0306d5{__ctx->buffers[217].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 10> temp_10{__ctx->buffers[218].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> temp_43{__ctx->buffers[219].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> temp_44{__ctx->buffers[220].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_7bba8c2207236de9.step(i);
        auto v1 = buf_12e33a12fe6cb8a9.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = Sub(0.f, v5);
        buf_alpha086.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_16f2a300b18e43c49.step(i);
        auto v9 = Sub(0.f, v8);
        temp_10.store(i, v9);
        auto v10 = v9;
        ReduceDecayLinear<float, 8, 10> v13{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v12 = temp_10.getWindow(i, iter);
            v13.step(v12, iter);
        }
        buf_4599ae1c53681419.store(i, v13);
        auto v14 = v13;
        auto v15 = buf_84f75b98293a92f1.step(i);
        auto v16 = Mul(20.f, v15);
        auto v17 = buf_close.step(i);
        ReduceArgMax<float, 8> v20{};
        for(int iter = 29;iter >= 20;iter--) {
            auto v19 = buf_close.getWindow(i, iter);
            v20.step(v19, iter);
        }
        ReduceAdd<float, 8> v23{};
        for(int iter = 19;iter >= 7;iter--) {
            auto v19 = buf_close.getWindow(i, iter);
            v20.step(v19, iter);
            auto v22 = buf_close.getWindow(i, iter);
            v23.step(v22, iter);
        }
        ReduceAdd<float, 8> v26{};
        for(int iter = 6;iter >= 5;iter--) {
            auto v19 = buf_close.getWindow(i, iter);
            v20.step(v19, iter);
            auto v22 = buf_close.getWindow(i, iter);
            v23.step(v22, iter);
            auto v25 = buf_close.getWindow(i, iter);
            v26.step(v25, iter);
        }
        ReduceAdd<float, 8> v29{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v19 = buf_close.getWindow(i, iter);
            v20.step(v19, iter);
            auto v22 = buf_close.getWindow(i, iter);
            v23.step(v22, iter);
            auto v25 = buf_close.getWindow(i, iter);
            v26.step(v25, iter);
            auto v28 = buf_close.getWindow(i, iter);
            v29.step(v28, iter);
        }
        auto v30 = Div(v26, 7.f);
        auto v31 = Div(v30, 7.f);
        auto v32 = Sub(v31, v17);
        buf_d6c2cd9d0a42eb53.store(i, v32);
        auto v33 = v32;
        auto v34 = buf_e0e3079c4a901fa8.step(i);
        auto v35 = Add(v34, v17);
        buf_122a8870451b838e7.store(i, v35);
        auto v36 = v35;
        auto v37 = Sub(v17, v34);
        auto v38 = Sub(30.f, v20);
        buf_19bcab12c0b751190.store(i, v38);
        auto v39 = v38;
        buf_e5741f7564c392ac.store(i, v37);
        auto v40 = v37;
        auto v41 = Sub(v34, v17);
        auto v42 = Div(v29, 5.f);
        temp_43.store(i, v29);
        auto v43 = v29;
        temp_44.store(i, v23);
        auto v44 = v23;
        ReduceAdd<float, 8> v47{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v46 = temp_43.getWindow(i, iter);
            v47.step(v46, iter);
        }
        auto v48 = Div(v47, 2.f);
        ReduceAdd<float, 8> v51{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v50 = temp_44.getWindow(i, iter);
            v51.step(v50, iter);
        }
        auto v52 = Div(v51, 2.f);
        ReduceAdd<float, 8> v58{};
        ReduceAdd<float, 8> v61{};
        ReduceAdd<float, 8> v63{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v54 = temp_44.getWindow(i, iter);
            auto v55 = temp_43.getWindow(i, iter);
            auto v56 = Sub(v55, v48);
            auto v57 = Mul(v56, v56);
            v58.step(v57, iter);
            auto v59 = Sub(v54, v52);
            auto v60 = Mul(v56, v59);
            v61.step(v60, iter);
            auto v62 = Mul(v59, v59);
            v63.step(v62, iter);
        }
        auto v64 = Sqrt(v63);
        auto v65 = Sqrt(v58);
        auto v66 = Mul(v65, v64);
        auto v67 = Div(v61, v66);
        auto v68 = SetInfOrNanToValue(v67, 1.f);
        buf_350c605085b2027e.store(i, v68);
        auto v69 = v68;
        auto v70 = Div(v23, 20.f);
        ReduceAdd<float, 8> v75{};
        for(int iter = 19;iter >= 0;iter--) {
            auto v72 = buf_close.getWindow(i, iter);
            auto v73 = Sub(v72, v70);
            auto v74 = Mul(v73, v73);
            v75.step(v74, iter);
        }
        buf_1f2d443115bbe19b8.store(i, v75);
        auto v76 = v75;
        buf_122a8a80286874cc8.store(i, v41);
        auto v77 = v41;
        buf_1209b15d3b8ee8a84.store(i, v42);
        auto v78 = v42;
        buf_9dd1ca3b1e0306d5.store(i, v16);
        auto v79 = v16;
    }
}















static void stage_alpha_101_stream__alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1a9308070e053bb70{__ctx->buffers[221].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_152b4b6c739dec9b7{__ctx->buffers[206].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> buf_122a8a80286874cc8{__ctx->buffers[215].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1eae54ac4d54608f9{__ctx->buffers[210].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_3eda254c3f952022{__ctx->buffers[173].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_a16e007555996c6e{__ctx->buffers[138].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f4055f7b15338dfe{__ctx->buffers[107].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_54c72aeffc3e3508{__ctx->buffers[171].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha042{__ctx->buffers[47].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_a9c228e0b9dafee8{__ctx->buffers[222].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_a9c228c71b191482{__ctx->buffers[224].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e28b6a66388aca96{__ctx->buffers[226].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_18e6ff5693a16658f{__ctx->buffers[228].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_92a82f927e12be36{__ctx->buffers[230].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_106498830afb12b2f{__ctx->buffers[232].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha024{__ctx->buffers[29].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_db1ec42f638754c4{__ctx->buffers[233].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19448648e73a0e59a{__ctx->buffers[234].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_46fde329d2fadda4{__ctx->buffers[235].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 101> temp_42{__ctx->buffers[236].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1a9308070e053bb70.step(i);
        auto v1 = buf_152b4b6c739dec9b7.step(i);
        auto v2 = Div(v0, v1);
        buf_alpha042.store(i, v2);
        auto v3 = v2;
        auto v4 = buf_122a8a80286874cc8.step(i);
        ReduceMin<float, 8> v7{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v6 = buf_122a8a80286874cc8.getWindow(i, iter);
            v7.step(v6, iter);
        }
        buf_a9c228e0b9dafee8.store(i, v7);
        auto v8 = v7;
        ReduceMax<float, 8> v11{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v10 = buf_122a8a80286874cc8.getWindow(i, iter);
            v11.step(v10, iter);
        }
        buf_a9c228c71b191482.store(i, v11);
        auto v12 = v11;
        auto v13 = buf_1eae54ac4d54608f9.step(i);
        auto v14 = Sub(0.f, v13);
        auto v15 = buf_open.step(i);
        auto v16 = buf_3eda254c3f952022.step(i);
        auto v17 = Sub(v15, v16);
        buf_e28b6a66388aca96.store(i, v17);
        auto v18 = v17;
        auto v19 = buf_a16e007555996c6e.step(i);
        auto v20 = Div(v19, v15);
        buf_18e6ff5693a16658f.store(i, v20);
        auto v21 = v20;
        auto v22 = buf_f4055f7b15338dfe.step(i);
        auto v23 = Sub(v15, v22);
        buf_92a82f927e12be36.store(i, v23);
        auto v24 = v23;
        auto v25 = buf_close.step(i);
        auto v26 = Div(v15, v25);
        buf_106498830afb12b2f.store(i, v26);
        auto v27 = v26;
        auto v28 = buf_low.step(i);
        auto v29 = Sub(v28, v25);
        auto v30 = Sub(0.f, v29);
        auto v31 = Mul(v15, v15);
        auto v32 = Mul(v31, v31);
        auto v33 = Mul(v15, v32);
        auto v34 = Mul(v30, v33);
        auto v35 = windowedRef<float, 8, 100>(buf_close, i);
        auto v36 = constVec<8>(0.05f);
        auto v37 = Mul(v35, v36);
        ReduceAdd<float, 8> v40{};
        for(int iter = 99;iter >= 0;iter--) {
            auto v39 = buf_close.getWindow(i, iter);
            v40.step(v39, iter);
        }
        auto v41 = Div(v40, 100.f);
        temp_42.store(i, v41);
        auto v42 = v41;
        auto v43 = windowedRef<float, 8, 100>(temp_42, i);
        auto v44 = Sub(v41, v43);
        auto v45 = LessEqual(v44, v37);
        ReduceMin<float, 8> v48{};
        for(int iter = 99;iter >= 0;iter--) {
            auto v47 = buf_close.getWindow(i, iter);
            v48.step(v47, iter);
        }
        auto v49 = Sub(v25, v48);
        auto v50 = Sub(0.f, v49);
        auto v51 = buf_54c72aeffc3e3508.step(i);
        auto v52 = Select(v45, v50, v51);
        buf_alpha024.store(i, v52);
        auto v53 = v52;
        auto v54 = Div(v25, v15);
        auto v55 = SetInfOrNanToValue(v54, 1.0f);
        buf_db1ec42f638754c4.store(i, v55);
        auto v56 = v55;
        buf_19448648e73a0e59a.store(i, v14);
        auto v57 = v14;
        buf_46fde329d2fadda4.store(i, v34);
        auto v58 = v34;
    }
}















static void stage_alpha_101_stream__f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1f769b559feed1b47{__ctx->buffers[94].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_62f1b56c1f91b803{__ctx->buffers[231].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_high{__ctx->buffers[1].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_a16e007555996c6e{__ctx->buffers[138].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1209b15d3b8ee8a84{__ctx->buffers[216].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> buf_122a8a80286874cc8{__ctx->buffers[215].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_156ece8c9e5c0c4da{__ctx->buffers[128].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 22> buf_bf8995d8792205e7{__ctx->buffers[142].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_b1ef8918ee2f42d9{__ctx->buffers[144].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d2731cd6670fbbb9{__ctx->buffers[214].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b087cce3aa0b937a{__ctx->buffers[174].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_f6f97214486a2cc1{__ctx->buffers[238].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha053{__ctx->buffers[57].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17feca80f99bb159b{__ctx->buffers[240].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha101{__ctx->buffers[87].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c484102cd55ad1b1{__ctx->buffers[243].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_53d006c904f6f37f{__ctx->buffers[245].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha022{__ctx->buffers[27].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_817836889c535887{__ctx->buffers[247].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_a7019f9a5c963187{__ctx->buffers[249].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d46a09aff428b211{__ctx->buffers[250].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1af234ead49ceac5a{__ctx->buffers[251].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1163db3251b923ec7{__ctx->buffers[252].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1015175dbdfa9818a{__ctx->buffers[253].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c125ece16202104b{__ctx->buffers[254].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_34585a48fb808f83{__ctx->buffers[255].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ab239b5b61f82d07{__ctx->buffers[256].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 10> temp_18{__ctx->buffers[257].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_38{__ctx->buffers[258].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_71{__ctx->buffers[259].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 26> temp_78{__ctx->buffers[260].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_83{__ctx->buffers[261].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_105{__ctx->buffers[262].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1f769b559feed1b47.step(i);
        auto v1 = buf_62f1b56c1f91b803.step(i);
        auto v2 = Mul(v0, v1);
        auto v3 = buf_low.step(i);
        auto v4 = windowedRef<float, 8, 1>(buf_low, i);
        auto v5 = buf_open.step(i);
        auto v6 = Sub(v5, v4);
        buf_f6f97214486a2cc1.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_close.step(i);
        auto v9 = Sub(v8, v3);
        auto v10 = constVec<8>(0.f);
        auto v11 = Equals(v9, v10);
        auto v12 = constVec<8>(0.0001f);
        auto v13 = Select(v11, v12, v9);
        auto v14 = buf_high.step(i);
        auto v15 = Sub(v14, v8);
        auto v16 = Sub(v9, v15);
        auto v17 = Div(v16, v13);
        temp_18.store(i, v17);
        auto v18 = v17;
        auto v19 = windowedRef<float, 8, 9>(temp_18, i);
        auto v20 = Sub(v17, v19);
        auto v21 = Sub(0.f, v20);
        buf_alpha053.store(i, v21);
        auto v22 = v21;
        auto v23 = buf_volume.step(i);
        auto v24 = Mul(v16, v23);
        auto v25 = Sub(v14, v3);
        auto v26 = Equals(v25, v10);
        auto v27 = Select(v26, v12, v25);
        auto v28 = Div(v24, v27);
        buf_17feca80f99bb159b.store(i, v28);
        auto v29 = v28;
        auto v30 = Add(v25, 0.001f);
        auto v31 = buf_a16e007555996c6e.step(i);
        auto v32 = Div(v31, v30);
        buf_alpha101.store(i, v32);
        auto v33 = v32;
        auto v34 = buf_1209b15d3b8ee8a84.step(i);
        auto v35 = Div(v25, v34);
        auto v36 = buf_122a8a80286874cc8.step(i);
        auto v37 = Div(v35, v36);
        temp_38.store(i, v35);
        auto v38 = v35;
        auto v39 = windowedRef<float, 8, 2>(temp_38, i);
        buf_c484102cd55ad1b1.store(i, v39);
        auto v40 = v39;
        buf_53d006c904f6f37f.store(i, v15);
        auto v41 = v15;
        auto v42 = buf_e0e3079c4a901fa8.step(i);
        auto v43 = Add(v42, v14);
        auto v44 = buf_156ece8c9e5c0c4da.step(i);
        auto v45 = Mul(v44, v23);
        auto v46 = buf_bf8995d8792205e7.step(i);
        auto v47 = Div(v45, v46);
        ReduceAdd<float, 8> v50{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v49 = buf_high.getWindow(i, iter);
            v50.step(v49, iter);
        }
        auto v51 = Div(v50, 5.f);
        auto v52 = Div(v51, 5.f);
        auto v53 = buf_b1ef8918ee2f42d9.step(i);
        auto v54 = Div(v53, 5.f);
        ReduceAdd<float, 8> v60{};
        ReduceAdd<float, 8> v63{};
        ReduceAdd<float, 8> v65{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v56 = buf_high.getWindow(i, iter);
            auto v57 = Sub(v56, v51);
            auto v58 = buf_volume.getWindow(i, iter);
            auto v59 = Mul(v57, v57);
            v60.step(v59, iter);
            auto v61 = Sub(v58, v54);
            auto v62 = Mul(v57, v61);
            v63.step(v62, iter);
            auto v64 = Mul(v61, v61);
            v65.step(v64, iter);
        }
        auto v66 = Sqrt(v65);
        auto v67 = Sqrt(v60);
        auto v68 = Mul(v67, v66);
        auto v69 = Div(v63, v68);
        auto v70 = SetInfOrNanToValue(v69, 0.0f);
        temp_71.store(i, v70);
        auto v71 = v70;
        auto v72 = windowedRef<float, 8, 5>(temp_71, i);
        auto v73 = Sub(v70, v72);
        auto v74 = Sub(0.f, v73);
        auto v75 = buf_1d2731cd6670fbbb9.step(i);
        auto v76 = Mul(v74, v75);
        buf_alpha022.store(i, v76);
        auto v77 = v76;
        temp_78.store(i, v54);
        auto v78 = v54;
        ReduceAdd<float, 8> v81{};
        for(int iter = 25;iter >= 0;iter--) {
            auto v80 = temp_78.getWindow(i, iter);
            v81.step(v80, iter);
        }
        auto v82 = Div(v81, 26.f);
        temp_83.store(i, v82);
        auto v83 = v82;
        ReduceAdd<float, 8> v86{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v85 = temp_83.getWindow(i, iter);
            v86.step(v85, iter);
        }
        auto v87 = Div(v86, 5.f);
        auto v88 = buf_1b087cce3aa0b937a.step(i);
        auto v89 = Div(v88, 5.f);
        ReduceAdd<float, 8> v95{};
        ReduceAdd<float, 8> v98{};
        ReduceAdd<float, 8> v100{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v91 = temp_83.getWindow(i, iter);
            auto v92 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v93 = Sub(v91, v87);
            auto v94 = Mul(v93, v93);
            v95.step(v94, iter);
            auto v96 = Sub(v92, v89);
            auto v97 = Mul(v96, v93);
            v98.step(v97, iter);
            auto v99 = Mul(v96, v96);
            v100.step(v99, iter);
        }
        auto v101 = Sqrt(v100);
        auto v102 = Sqrt(v95);
        auto v103 = Mul(v101, v102);
        auto v104 = Div(v98, v103);
        temp_105.store(i, v104);
        auto v105 = v104;
        ReduceDecayLinear<float, 8, 7> v108{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v107 = temp_105.getWindow(i, iter);
            v108.step(v107, iter);
        }
        buf_817836889c535887.store(i, v108);
        auto v109 = v108;
        ReduceRank<float, 8> v112{v8};
        for(int iter = 9;iter >= 0;iter--) {
            auto v111 = buf_close.getWindow(i, iter);
            v112.step(v111, iter);
        }
        buf_a7019f9a5c963187.store(i, v112);
        auto v113 = v112;
        buf_d46a09aff428b211.store(i, v52);
        auto v114 = v52;
        buf_1af234ead49ceac5a.store(i, v47);
        auto v115 = v47;
        buf_1163db3251b923ec7.store(i, v43);
        auto v116 = v43;
        buf_1015175dbdfa9818a.store(i, v51);
        auto v117 = v51;
        buf_1c125ece16202104b.store(i, v67);
        auto v118 = v67;
        buf_34585a48fb808f83.store(i, v37);
        auto v119 = v37;
        buf_ab239b5b61f82d07.store(i, v2);
        auto v120 = v2;
    }
}





















static void stage_alpha_101_stream__1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 20> buf_high{__ctx->buffers[1].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c8a42a3708a91898{__ctx->buffers[246].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d46a09aff428b211{__ctx->buffers[250].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1af234ead49ceac5a{__ctx->buffers[251].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_1dc772caca00e1584{__ctx->buffers[175].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_18fdad286b28e4b7b{__ctx->buffers[91].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1fb795dcf1c9ec054{__ctx->buffers[264].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_07afa02613eebb6d{__ctx->buffers[266].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_197046de60901aa41{__ctx->buffers[269].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a12cbc7ebe63cfcc{__ctx->buffers[271].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1457ce2c155a61fd4{__ctx->buffers[273].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_76fe21605013c9a7{__ctx->buffers[275].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_20e24891e9d62d6b{__ctx->buffers[277].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e88f9cd0d1d87daa{__ctx->buffers[279].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_149416df8d2c4dac{__ctx->buffers[281].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_718b6e819250274f{__ctx->buffers[282].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_1d423b99fe89ce6c8{__ctx->buffers[283].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_ac8b438bbe1cf240{__ctx->buffers[284].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ca86c5db4dae4c75{__ctx->buffers[285].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d4580520b32d9d74{__ctx->buffers[286].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_c7f03cfe4a9a0496{__ctx->buffers[287].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_11ba1c0582ed7958d{__ctx->buffers[288].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_7e2c3205f536c762{__ctx->buffers[289].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e083a5768021835f{__ctx->buffers[290].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_168fddb21a03fc027{__ctx->buffers[291].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15c36dacf75e527a0{__ctx->buffers[292].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f724efa04733d837{__ctx->buffers[293].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16ba41787258d0ac2{__ctx->buffers[294].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_711b2797a20851f9{__ctx->buffers[295].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d0d5bd22659e1cc3{__ctx->buffers[296].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_13990b65aba0b7628{__ctx->buffers[297].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 16> temp_17{__ctx->buffers[298].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> temp_25{__ctx->buffers[299].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_33{__ctx->buffers[300].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 10> temp_40{__ctx->buffers[301].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 37> temp_55{__ctx->buffers[302].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> temp_76{__ctx->buffers[303].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> temp_111{__ctx->buffers[304].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 4> temp_136{__ctx->buffers[305].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 16> temp_140{__ctx->buffers[306].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_153{__ctx->buffers[307].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 21> temp_155{__ctx->buffers[308].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_165{__ctx->buffers[309].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_192{__ctx->buffers[310].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_199{__ctx->buffers[311].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> temp_221{__ctx->buffers[312].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> temp_228{__ctx->buffers[313].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> temp_254{__ctx->buffers[314].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_315{__ctx->buffers[315].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_317{__ctx->buffers[316].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_high.step(i);
        auto v1 = buf_c8a42a3708a91898.step(i);
        auto v2 = Mul(v0, v1);
        auto v3 = buf_d46a09aff428b211.step(i);
        auto v4 = Div(v2, v3);
        auto v5 = buf_1af234ead49ceac5a.step(i);
        auto v6 = Mul(v5, v4);
        auto v7 = buf_close.step(i);
        ReduceAdd<float, 8> v10{};
        for(int iter = 14;iter >= 10;iter--) {
            auto v9 = buf_close.getWindow(i, iter);
            v10.step(v9, iter);
        }
        ReduceArgMax<float, 8> v13{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v9 = buf_close.getWindow(i, iter);
            v10.step(v9, iter);
            auto v12 = buf_close.getWindow(i, iter);
            v13.step(v12, iter);
        }
        auto v14 = Add(v7, v0);
        auto v15 = buf_low.step(i);
        auto v16 = Sub(v14, v15);
        temp_17.store(i, v16);
        auto v17 = v16;
        ReduceRank<float, 8> v20{v16};
        for(int iter = 15;iter >= 0;iter--) {
            auto v19 = temp_17.getWindow(i, iter);
            v20.step(v19, iter);
        }
        auto v21 = Sub(1.f, v20);
        auto v22 = Mul(v7, 0.518371f);
        auto v23 = Mul(v15, 0.481629f);
        auto v24 = Add(v22, v23);
        temp_25.store(i, v24);
        auto v25 = v24;
        auto v26 = windowedRef<float, 8, 1>(temp_25, i);
        auto v27 = Sub(v24, v26);
        buf_1fb795dcf1c9ec054.store(i, v27);
        auto v28 = v27;
        auto v29 = Sub(10.f, v13);
        buf_07afa02613eebb6d.store(i, v29);
        auto v30 = v29;
        auto v31 = Div(v10, 15.f);
        auto v32 = Sub(v7, 1.f);
        temp_33.store(i, v32);
        auto v33 = v32;
        auto v34 = windowedRef<float, 8, 5>(temp_33, i);
        auto v35 = Sub(v32, v34);
        buf_197046de60901aa41.store(i, v35);
        auto v36 = v35;
        auto v37 = Mul(v7, 0.12329699999999999f);
        auto v38 = Mul(v0, 0.876703f);
        auto v39 = Add(v38, v37);
        temp_40.store(i, v39);
        auto v40 = v39;
        ReduceAdd<float, 8> v43{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v42 = temp_40.getWindow(i, iter);
            v43.step(v42, iter);
        }
        auto v44 = buf_volume.step(i);
        ReduceAdd<float, 8> v47{};
        for(int iter = 59;iter >= 40;iter--) {
            auto v46 = buf_volume.getWindow(i, iter);
            v47.step(v46, iter);
        }
        ReduceAdd<float, 8> v50{};
        for(int iter = 39;iter >= 30;iter--) {
            auto v46 = buf_volume.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_volume.getWindow(i, iter);
            v50.step(v49, iter);
        }
        ReduceAdd<float, 8> v53{};
        for(int iter = 29;iter >= 0;iter--) {
            auto v46 = buf_volume.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_volume.getWindow(i, iter);
            v50.step(v49, iter);
            auto v52 = buf_volume.getWindow(i, iter);
            v53.step(v52, iter);
        }
        auto v54 = Div(v53, 30.f);
        temp_55.store(i, v54);
        auto v55 = v54;
        ReduceAdd<float, 8> v58{};
        for(int iter = 36;iter >= 10;iter--) {
            auto v57 = temp_55.getWindow(i, iter);
            v58.step(v57, iter);
        }
        ReduceAdd<float, 8> v61{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v57 = temp_55.getWindow(i, iter);
            v58.step(v57, iter);
            auto v60 = temp_55.getWindow(i, iter);
            v61.step(v60, iter);
        }
        auto v62 = Div(v43, 10.f);
        auto v63 = Div(v61, 10.f);
        ReduceAdd<float, 8> v69{};
        ReduceAdd<float, 8> v72{};
        ReduceAdd<float, 8> v74{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v65 = temp_55.getWindow(i, iter);
            auto v66 = temp_40.getWindow(i, iter);
            auto v67 = Sub(v65, v63);
            auto v68 = Mul(v67, v67);
            v69.step(v68, iter);
            auto v70 = Sub(v66, v62);
            auto v71 = Mul(v70, v67);
            v72.step(v71, iter);
            auto v73 = Mul(v70, v70);
            v74.step(v73, iter);
        }
        auto v75 = Div(v58, 37.f);
        temp_76.store(i, v75);
        auto v76 = v75;
        ReduceAdd<float, 8> v79{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v78 = temp_76.getWindow(i, iter);
            v79.step(v78, iter);
        }
        auto v80 = Div(v79, 15.f);
        ReduceAdd<float, 8> v86{};
        ReduceAdd<float, 8> v89{};
        ReduceAdd<float, 8> v91{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v82 = buf_close.getWindow(i, iter);
            auto v83 = Sub(v82, v31);
            auto v84 = temp_76.getWindow(i, iter);
            auto v85 = Mul(v83, v83);
            v86.step(v85, iter);
            auto v87 = Sub(v84, v80);
            auto v88 = Mul(v83, v87);
            v89.step(v88, iter);
            auto v90 = Mul(v87, v87);
            v91.step(v90, iter);
        }
        auto v92 = Sqrt(v91);
        auto v93 = Sqrt(v86);
        auto v94 = Mul(v93, v92);
        auto v95 = Sqrt(v74);
        auto v96 = Sqrt(v69);
        auto v97 = Mul(v95, v96);
        ReduceRank<float, 8> v100{v44};
        for(int iter = 31;iter >= 0;iter--) {
            auto v99 = buf_volume.getWindow(i, iter);
            v100.step(v99, iter);
        }
        auto v101 = Mul(v100, v21);
        auto v102 = Div(v72, v97);
        auto v103 = SetInfOrNanToValue(v102, 1.f);
        buf_1a12cbc7ebe63cfcc.store(i, v103);
        auto v104 = v103;
        auto v105 = Div(v89, v94);
        buf_1457ce2c155a61fd4.store(i, v105);
        auto v106 = v105;
        buf_76fe21605013c9a7.store(i, v53);
        auto v107 = v53;
        ReduceRank<float, 8> v110{v7};
        for(int iter = 2;iter >= 0;iter--) {
            auto v109 = buf_close.getWindow(i, iter);
            v110.step(v109, iter);
        }
        temp_111.store(i, v110);
        auto v111 = v110;
        ReduceAdd<float, 8> v114{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v113 = temp_111.getWindow(i, iter);
            v114.step(v113, iter);
        }
        auto v115 = Div(v114, 18.f);
        auto v116 = buf_1dc772caca00e1584.step(i);
        ReduceAdd<float, 8> v119{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v118 = buf_1dc772caca00e1584.getWindow(i, iter);
            v119.step(v118, iter);
        }
        auto v120 = Div(v119, 18.f);
        ReduceAdd<float, 8> v126{};
        ReduceAdd<float, 8> v129{};
        ReduceAdd<float, 8> v131{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v122 = temp_111.getWindow(i, iter);
            auto v123 = Sub(v122, v115);
            auto v124 = buf_1dc772caca00e1584.getWindow(i, iter);
            auto v125 = Mul(v123, v123);
            v126.step(v125, iter);
            auto v127 = Sub(v124, v120);
            auto v128 = Mul(v123, v127);
            v129.step(v128, iter);
            auto v130 = Mul(v127, v127);
            v131.step(v130, iter);
        }
        auto v132 = Sqrt(v131);
        auto v133 = Sqrt(v126);
        auto v134 = Mul(v133, v132);
        auto v135 = Div(v129, v134);
        temp_136.store(i, v135);
        auto v136 = v135;
        ReduceDecayLinear<float, 8, 4> v139{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v138 = temp_136.getWindow(i, iter);
            v139.step(v138, iter);
        }
        temp_140.store(i, v139);
        auto v140 = v139;
        ReduceRank<float, 8> v143{v139};
        for(int iter = 15;iter >= 0;iter--) {
            auto v142 = temp_140.getWindow(i, iter);
            v143.step(v142, iter);
        }
        auto v144 = buf_open.step(i);
        auto v145 = Add(v15, v144);
        auto v146 = buf_e0e3079c4a901fa8.step(i);
        auto v147 = Add(v146, v146);
        auto v148 = Sub(v145, v147);
        buf_20e24891e9d62d6b.store(i, v148);
        auto v149 = v148;
        ReduceRank<float, 8> v152{v7};
        for(int iter = 7;iter >= 0;iter--) {
            auto v151 = buf_close.getWindow(i, iter);
            v152.step(v151, iter);
        }
        temp_153.store(i, v152);
        auto v153 = v152;
        auto v154 = Div(v47, 60.f);
        temp_155.store(i, v154);
        auto v155 = v154;
        ReduceAdd<float, 8> v158{};
        for(int iter = 19;iter >= 9;iter--) {
            auto v157 = temp_155.getWindow(i, iter);
            v158.step(v157, iter);
        }
        ReduceAdd<float, 8> v161{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v157 = temp_155.getWindow(i, iter);
            v158.step(v157, iter);
            auto v160 = temp_155.getWindow(i, iter);
            v161.step(v160, iter);
        }
        ReduceRank<float, 8> v164{v154};
        for(int iter = 20;iter >= 0;iter--) {
            auto v163 = temp_155.getWindow(i, iter);
            v164.step(v163, iter);
        }
        temp_165.store(i, v164);
        auto v165 = v164;
        ReduceAdd<float, 8> v168{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v167 = temp_165.getWindow(i, iter);
            v168.step(v167, iter);
        }
        auto v169 = Div(v168, 8.f);
        ReduceAdd<float, 8> v172{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v171 = temp_153.getWindow(i, iter);
            v172.step(v171, iter);
        }
        auto v173 = Div(v172, 8.f);
        ReduceAdd<float, 8> v179{};
        ReduceAdd<float, 8> v182{};
        ReduceAdd<float, 8> v184{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v175 = temp_165.getWindow(i, iter);
            auto v176 = temp_153.getWindow(i, iter);
            auto v177 = Sub(v176, v173);
            auto v178 = Mul(v177, v177);
            v179.step(v178, iter);
            auto v180 = Sub(v175, v169);
            auto v181 = Mul(v177, v180);
            v182.step(v181, iter);
            auto v183 = Mul(v180, v180);
            v184.step(v183, iter);
        }
        ReduceRank<float, 8> v187{v154};
        for(int iter = 3;iter >= 0;iter--) {
            auto v186 = temp_155.getWindow(i, iter);
            v187.step(v186, iter);
        }
        auto v188 = Sqrt(v184);
        auto v189 = Sqrt(v179);
        auto v190 = Mul(v189, v188);
        auto v191 = Div(v161, 9.f);
        temp_192.store(i, v191);
        auto v192 = v191;
        auto v193 = Mul(v144, 0.00817205f);
        ReduceAdd<float, 8> v196{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v195 = temp_192.getWindow(i, iter);
            v196.step(v195, iter);
        }
        auto v197 = Mul(v146, 0.99182795f);
        auto v198 = Add(v193, v197);
        temp_199.store(i, v198);
        auto v199 = v198;
        auto v200 = Div(v196, 6.f);
        ReduceAdd<float, 8> v203{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v202 = temp_199.getWindow(i, iter);
            v203.step(v202, iter);
        }
        auto v204 = Div(v203, 6.f);
        ReduceAdd<float, 8> v210{};
        ReduceAdd<float, 8> v213{};
        ReduceAdd<float, 8> v215{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v206 = temp_192.getWindow(i, iter);
            auto v207 = temp_199.getWindow(i, iter);
            auto v208 = Sub(v207, v204);
            auto v209 = Mul(v208, v208);
            v210.step(v209, iter);
            auto v211 = Sub(v206, v200);
            auto v212 = Mul(v208, v211);
            v213.step(v212, iter);
            auto v214 = Mul(v211, v211);
            v215.step(v214, iter);
        }
        auto v216 = Sqrt(v215);
        auto v217 = Sqrt(v210);
        auto v218 = Mul(v217, v216);
        auto v219 = Div(v213, v218);
        buf_e88f9cd0d1d87daa.store(i, v219);
        auto v220 = v219;
        temp_221.store(i, v158);
        auto v221 = v158;
        ReduceAdd<float, 8> v224{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v223 = temp_221.getWindow(i, iter);
            v224.step(v223, iter);
        }
        auto v225 = buf_18fdad286b28e4b7b.step(i);
        auto v226 = Div(v225, 2.f);
        auto v227 = Div(v50, 40.f);
        temp_228.store(i, v227);
        auto v228 = v227;
        ReduceAdd<float, 8> v231{};
        for(int iter = 19;iter >= 19;iter--) {
            auto v230 = temp_228.getWindow(i, iter);
            v231.step(v230, iter);
        }
        ReduceAdd<float, 8> v234{};
        for(int iter = 18;iter >= 9;iter--) {
            auto v230 = temp_228.getWindow(i, iter);
            v231.step(v230, iter);
            auto v233 = temp_228.getWindow(i, iter);
            v234.step(v233, iter);
        }
        ReduceAdd<float, 8> v237{};
        for(int iter = 8;iter >= 3;iter--) {
            auto v230 = temp_228.getWindow(i, iter);
            v231.step(v230, iter);
            auto v233 = temp_228.getWindow(i, iter);
            v234.step(v233, iter);
            auto v236 = temp_228.getWindow(i, iter);
            v237.step(v236, iter);
        }
        ReduceAdd<float, 8> v240{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v230 = temp_228.getWindow(i, iter);
            v231.step(v230, iter);
            auto v233 = temp_228.getWindow(i, iter);
            v234.step(v233, iter);
            auto v236 = temp_228.getWindow(i, iter);
            v237.step(v236, iter);
            auto v239 = temp_228.getWindow(i, iter);
            v240.step(v239, iter);
        }
        buf_149416df8d2c4dac.store(i, v226);
        auto v241 = v226;
        ReduceAdd<float, 8> v244{};
        for(int iter = 19;iter >= 19;iter--) {
            auto v243 = buf_149416df8d2c4dac.getWindow(i, iter);
            v244.step(v243, iter);
        }
        ReduceAdd<float, 8> v247{};
        for(int iter = 18;iter >= 9;iter--) {
            auto v243 = buf_149416df8d2c4dac.getWindow(i, iter);
            v244.step(v243, iter);
            auto v246 = buf_149416df8d2c4dac.getWindow(i, iter);
            v247.step(v246, iter);
        }
        ReduceAdd<float, 8> v250{};
        for(int iter = 8;iter >= 3;iter--) {
            auto v243 = buf_149416df8d2c4dac.getWindow(i, iter);
            v244.step(v243, iter);
            auto v246 = buf_149416df8d2c4dac.getWindow(i, iter);
            v247.step(v246, iter);
            auto v249 = buf_149416df8d2c4dac.getWindow(i, iter);
            v250.step(v249, iter);
        }
        ReduceAdd<float, 8> v253{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v243 = buf_149416df8d2c4dac.getWindow(i, iter);
            v244.step(v243, iter);
            auto v246 = buf_149416df8d2c4dac.getWindow(i, iter);
            v247.step(v246, iter);
            auto v249 = buf_149416df8d2c4dac.getWindow(i, iter);
            v250.step(v249, iter);
            auto v252 = buf_149416df8d2c4dac.getWindow(i, iter);
            v253.step(v252, iter);
        }
        temp_254.store(i, v244);
        auto v254 = v244;
        auto v255 = Div(v224, 9.f);
        ReduceAdd<float, 8> v258{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v257 = temp_254.getWindow(i, iter);
            v258.step(v257, iter);
        }
        auto v259 = Div(v258, 9.f);
        ReduceAdd<float, 8> v265{};
        ReduceAdd<float, 8> v268{};
        ReduceAdd<float, 8> v270{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v261 = temp_221.getWindow(i, iter);
            auto v262 = temp_254.getWindow(i, iter);
            auto v263 = Sub(v262, v259);
            auto v264 = Mul(v263, v263);
            v265.step(v264, iter);
            auto v266 = Sub(v261, v255);
            auto v267 = Mul(v263, v266);
            v268.step(v267, iter);
            auto v269 = Mul(v266, v266);
            v270.step(v269, iter);
        }
        auto v271 = Div(v253, 3.f);
        auto v272 = Div(v240, 3.f);
        ReduceAdd<float, 8> v278{};
        ReduceAdd<float, 8> v281{};
        ReduceAdd<float, 8> v283{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v274 = temp_228.getWindow(i, iter);
            auto v275 = buf_149416df8d2c4dac.getWindow(i, iter);
            auto v276 = Sub(v275, v271);
            auto v277 = Mul(v276, v276);
            v278.step(v277, iter);
            auto v279 = Sub(v274, v272);
            auto v280 = Mul(v276, v279);
            v281.step(v280, iter);
            auto v282 = Mul(v279, v279);
            v283.step(v282, iter);
        }
        auto v284 = Div(v237, 9.f);
        auto v285 = Div(v250, 9.f);
        ReduceAdd<float, 8> v291{};
        ReduceAdd<float, 8> v294{};
        ReduceAdd<float, 8> v296{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v287 = temp_228.getWindow(i, iter);
            auto v288 = buf_149416df8d2c4dac.getWindow(i, iter);
            auto v289 = Sub(v288, v285);
            auto v290 = Mul(v289, v289);
            v291.step(v290, iter);
            auto v292 = Sub(v287, v284);
            auto v293 = Mul(v289, v292);
            v294.step(v293, iter);
            auto v295 = Mul(v292, v292);
            v296.step(v295, iter);
        }
        ReduceRank<float, 8> v299{v226};
        for(int iter = 3;iter >= 0;iter--) {
            auto v298 = buf_149416df8d2c4dac.getWindow(i, iter);
            v299.step(v298, iter);
        }
        auto v300 = Sqrt(v296);
        auto v301 = Sqrt(v291);
        auto v302 = Mul(v301, v300);
        auto v303 = Sqrt(v283);
        auto v304 = Sqrt(v278);
        auto v305 = Mul(v304, v303);
        auto v306 = Sqrt(v270);
        auto v307 = Sqrt(v265);
        auto v308 = Mul(v307, v306);
        auto v309 = Add(v226, v7);
        auto v310 = LessThan(v309, v145);
        auto v311 = constVec<8>(1.f);
        auto v312 = constVec<8>(0.f);
        auto v313 = Select(v310, v311, v312);
        auto v314 = Div(v247, 19.f);
        temp_315.store(i, v314);
        auto v315 = v314;
        auto v316 = Div(v234, 19.f);
        temp_317.store(i, v316);
        auto v317 = v316;
        ReduceAdd<float, 8> v320{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v319 = temp_317.getWindow(i, iter);
            v320.step(v319, iter);
        }
        auto v321 = Div(v320, 13.f);
        ReduceAdd<float, 8> v324{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v323 = temp_315.getWindow(i, iter);
            v324.step(v323, iter);
        }
        auto v325 = Div(v324, 13.f);
        ReduceAdd<float, 8> v331{};
        ReduceAdd<float, 8> v334{};
        ReduceAdd<float, 8> v336{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v327 = temp_317.getWindow(i, iter);
            auto v328 = temp_315.getWindow(i, iter);
            auto v329 = Sub(v328, v325);
            auto v330 = Mul(v329, v329);
            v331.step(v330, iter);
            auto v332 = Sub(v327, v321);
            auto v333 = Mul(v329, v332);
            v334.step(v333, iter);
            auto v335 = Mul(v332, v332);
            v336.step(v335, iter);
        }
        auto v337 = Sqrt(v336);
        auto v338 = Sqrt(v331);
        auto v339 = Mul(v338, v337);
        auto v340 = Div(v334, v339);
        buf_718b6e819250274f.store(i, v340);
        auto v341 = v340;
        buf_1d423b99fe89ce6c8.store(i, v299);
        auto v342 = v299;
        buf_ac8b438bbe1cf240.store(i, v313);
        auto v343 = v313;
        buf_ca86c5db4dae4c75.store(i, v294);
        auto v344 = v294;
        buf_1d4580520b32d9d74.store(i, v302);
        auto v345 = v302;
        buf_c7f03cfe4a9a0496.store(i, v231);
        auto v346 = v231;
        buf_11ba1c0582ed7958d.store(i, v187);
        auto v347 = v187;
        buf_7e2c3205f536c762.store(i, v268);
        auto v348 = v268;
        buf_e083a5768021835f.store(i, v308);
        auto v349 = v308;
        buf_168fddb21a03fc027.store(i, v281);
        auto v350 = v281;
        buf_15c36dacf75e527a0.store(i, v305);
        auto v351 = v305;
        buf_1f724efa04733d837.store(i, v182);
        auto v352 = v182;
        buf_16ba41787258d0ac2.store(i, v190);
        auto v353 = v190;
        buf_711b2797a20851f9.store(i, v143);
        auto v354 = v143;
        buf_1d0d5bd22659e1cc3.store(i, v6);
        auto v355 = v6;
        buf_13990b65aba0b7628.store(i, v101);
        auto v356 = v101;
    }
}



















static void stage_alpha_101_stream__49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1d4b7681a80713990{__ctx->buffers[317].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> buf_open{__ctx->buffers[3].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_149416df8d2c4dac{__ctx->buffers[281].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cd0c872219e5d6d0{__ctx->buffers[176].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_89deb5a8070c6bf4{__ctx->buffers[177].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_1d423b99fe89ce6c8{__ctx->buffers[283].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_49be5d9c39d10d72{__ctx->buffers[318].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_351f7bdaf8ced412{__ctx->buffers[320].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_0189b1479ac96a49{__ctx->buffers[322].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d55ca7c2bb592f6b{__ctx->buffers[324].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1bf81dfeb065cab7e{__ctx->buffers[326].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d782b67ea7f219d8{__ctx->buffers[328].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_13ceb33893cac5614{__ctx->buffers[330].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1637895cfc77c386d{__ctx->buffers[332].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1cf1f6a8d90ea2e78{__ctx->buffers[334].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14c03c015491e6f07{__ctx->buffers[335].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1859886e85061c544{__ctx->buffers[336].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_123e2c4ec6491301c{__ctx->buffers[337].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> temp_4{__ctx->buffers[338].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> temp_22{__ctx->buffers[339].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_26{__ctx->buffers[340].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_41{__ctx->buffers[341].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_50{__ctx->buffers[342].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_55{__ctx->buffers[343].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 17> temp_59{__ctx->buffers[344].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_64{__ctx->buffers[345].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 17> temp_69{__ctx->buffers[346].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_82{__ctx->buffers[347].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 17> temp_87{__ctx->buffers[348].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_138{__ctx->buffers[349].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1d4b7681a80713990.step(i);
        auto v1 = Mul(v0, v0);
        auto v2 = Mul(v1, v1);
        auto v3 = Mul(v0, v2);
        temp_4.store(i, v3);
        auto v4 = v3;
        ReduceRank<float, 8> v7{v3};
        for(int iter = 11;iter >= 0;iter--) {
            auto v6 = temp_4.getWindow(i, iter);
            v7.step(v6, iter);
        }
        auto v8 = buf_open.step(i);
        ReduceMin<float, 8> v11{};
        for(int iter = 13;iter >= 12;iter--) {
            auto v10 = buf_open.getWindow(i, iter);
            v11.step(v10, iter);
        }
        ReduceMin<float, 8> v14{};
        for(int iter = 11;iter >= 0;iter--) {
            auto v10 = buf_open.getWindow(i, iter);
            v11.step(v10, iter);
            auto v13 = buf_open.getWindow(i, iter);
            v14.step(v13, iter);
        }
        auto v15 = Sub(v8, v14);
        buf_49be5d9c39d10d72.store(i, v15);
        auto v16 = v15;
        auto v17 = buf_149416df8d2c4dac.step(i);
        auto v18 = Sub(v8, v17);
        auto v19 = buf_cd0c872219e5d6d0.step(i);
        auto v20 = Div(v19, v18);
        auto v21 = SetInfOrNanToValue(v20, 0.0f);
        temp_22.store(i, v21);
        auto v22 = v21;
        ReduceDecayLinear<float, 8, 11> v25{};
        for(int iter = 10;iter >= 0;iter--) {
            auto v24 = temp_22.getWindow(i, iter);
            v25.step(v24, iter);
        }
        temp_26.store(i, v25);
        auto v26 = v25;
        ReduceRank<float, 8> v29{v25};
        for(int iter = 6;iter >= 0;iter--) {
            auto v28 = temp_26.getWindow(i, iter);
            v29.step(v28, iter);
        }
        ReduceRank<float, 8> v32{v8};
        for(int iter = 9;iter >= 0;iter--) {
            auto v31 = buf_open.getWindow(i, iter);
            v32.step(v31, iter);
        }
        buf_351f7bdaf8ced412.store(i, v32);
        auto v33 = v32;
        auto v34 = Sub(v8, v11);
        buf_0189b1479ac96a49.store(i, v34);
        auto v35 = v34;
        auto v36 = Mul(v8, 0.147155f);
        auto v37 = Mul(v8, 0.178404f);
        auto v38 = buf_e0e3079c4a901fa8.step(i);
        auto v39 = buf_89deb5a8070c6bf4.step(i);
        auto v40 = Sub(v38, v39);
        temp_41.store(i, v40);
        auto v41 = v40;
        ReduceDecayLinear<float, 8, 7> v44{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v43 = temp_41.getWindow(i, iter);
            v44.step(v43, iter);
        }
        buf_1d55ca7c2bb592f6b.store(i, v44);
        auto v45 = v44;
        auto v46 = buf_low.step(i);
        auto v47 = Mul(v46, 0.852845f);
        auto v48 = Mul(v46, 0.821596f);
        auto v49 = Add(v36, v47);
        temp_50.store(i, v49);
        auto v50 = v49;
        auto v51 = windowedRef<float, 8, 2>(temp_50, i);
        auto v52 = Sub(v49, v51);
        auto v53 = Div(v52, v49);
        auto v54 = Sub(0.f, v53);
        temp_55.store(i, v54);
        auto v55 = v54;
        ReduceDecayLinear<float, 8, 3> v58{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v57 = temp_55.getWindow(i, iter);
            v58.step(v57, iter);
        }
        temp_59.store(i, v58);
        auto v59 = v58;
        ReduceRank<float, 8> v62{v58};
        for(int iter = 16;iter >= 0;iter--) {
            auto v61 = temp_59.getWindow(i, iter);
            v62.step(v61, iter);
        }
        auto v63 = Add(v37, v48);
        temp_64.store(i, v63);
        auto v64 = v63;
        ReduceAdd<float, 8> v67{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v66 = temp_64.getWindow(i, iter);
            v67.step(v66, iter);
        }
        auto v68 = Div(v67, 13.f);
        temp_69.store(i, v68);
        auto v69 = v68;
        ReduceAdd<float, 8> v72{};
        for(int iter = 16;iter >= 0;iter--) {
            auto v71 = temp_69.getWindow(i, iter);
            v72.step(v71, iter);
        }
        auto v73 = Div(v72, 17.f);
        auto v74 = buf_volume.step(i);
        ReduceAdd<float, 8> v77{};
        for(int iter = 119;iter >= 6;iter--) {
            auto v76 = buf_volume.getWindow(i, iter);
            v77.step(v76, iter);
        }
        ReduceAdd<float, 8> v80{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v76 = buf_volume.getWindow(i, iter);
            v77.step(v76, iter);
            auto v79 = buf_volume.getWindow(i, iter);
            v80.step(v79, iter);
        }
        auto v81 = Div(v77, 120.f);
        temp_82.store(i, v81);
        auto v82 = v81;
        ReduceAdd<float, 8> v85{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v84 = temp_82.getWindow(i, iter);
            v85.step(v84, iter);
        }
        auto v86 = Div(v85, 13.f);
        temp_87.store(i, v86);
        auto v87 = v86;
        ReduceAdd<float, 8> v90{};
        for(int iter = 16;iter >= 0;iter--) {
            auto v89 = temp_87.getWindow(i, iter);
            v90.step(v89, iter);
        }
        auto v91 = Div(v90, 17.f);
        ReduceAdd<float, 8> v97{};
        ReduceAdd<float, 8> v100{};
        ReduceAdd<float, 8> v102{};
        for(int iter = 16;iter >= 0;iter--) {
            auto v93 = temp_69.getWindow(i, iter);
            auto v94 = Sub(v93, v73);
            auto v95 = temp_87.getWindow(i, iter);
            auto v96 = Mul(v94, v94);
            v97.step(v96, iter);
            auto v98 = Sub(v95, v91);
            auto v99 = Mul(v94, v98);
            v100.step(v99, iter);
            auto v101 = Mul(v98, v98);
            v102.step(v101, iter);
        }
        auto v103 = Sqrt(v102);
        auto v104 = Sqrt(v97);
        auto v105 = Mul(v104, v103);
        auto v106 = Div(v100, v105);
        buf_1bf81dfeb065cab7e.store(i, v106);
        auto v107 = v106;
        auto v108 = windowedRef<float, 8, 3>(buf_volume, i);
        auto v109 = Sub(v74, v108);
        buf_d782b67ea7f219d8.store(i, v109);
        auto v110 = v109;
        auto v111 = Div(v80, 6.f);
        ReduceAdd<float, 8> v114{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v113 = buf_low.getWindow(i, iter);
            v114.step(v113, iter);
        }
        auto v115 = Div(v114, 6.f);
        ReduceAdd<float, 8> v121{};
        ReduceAdd<float, 8> v124{};
        ReduceAdd<float, 8> v126{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v117 = buf_volume.getWindow(i, iter);
            auto v118 = Sub(v117, v111);
            auto v119 = buf_low.getWindow(i, iter);
            auto v120 = Mul(v118, v118);
            v121.step(v120, iter);
            auto v122 = Sub(v119, v115);
            auto v123 = Mul(v122, v118);
            v124.step(v123, iter);
            auto v125 = Mul(v122, v122);
            v126.step(v125, iter);
        }
        auto v127 = Sqrt(v126);
        auto v128 = Sqrt(v121);
        auto v129 = Mul(v127, v128);
        auto v130 = Div(v124, v129);
        buf_13ceb33893cac5614.store(i, v130);
        auto v131 = v130;
        auto v132 = windowedRef<float, 8, 2>(buf_volume, i);
        auto v133 = Div(v74, v132);
        buf_1637895cfc77c386d.store(i, v133);
        auto v134 = v133;
        ReduceRank<float, 8> v137{v74};
        for(int iter = 9;iter >= 0;iter--) {
            auto v136 = buf_volume.getWindow(i, iter);
            v137.step(v136, iter);
        }
        temp_138.store(i, v137);
        auto v138 = v137;
        ReduceAdd<float, 8> v141{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v140 = temp_138.getWindow(i, iter);
            v141.step(v140, iter);
        }
        auto v142 = Div(v141, 7.f);
        auto v143 = buf_1d423b99fe89ce6c8.step(i);
        ReduceAdd<float, 8> v146{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v145 = buf_1d423b99fe89ce6c8.getWindow(i, iter);
            v146.step(v145, iter);
        }
        auto v147 = Div(v146, 7.f);
        ReduceAdd<float, 8> v153{};
        ReduceAdd<float, 8> v156{};
        ReduceAdd<float, 8> v158{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v149 = temp_138.getWindow(i, iter);
            auto v150 = Sub(v149, v142);
            auto v151 = buf_1d423b99fe89ce6c8.getWindow(i, iter);
            auto v152 = Mul(v150, v150);
            v153.step(v152, iter);
            auto v154 = Sub(v151, v147);
            auto v155 = Mul(v154, v150);
            v156.step(v155, iter);
            auto v157 = Mul(v154, v154);
            v158.step(v157, iter);
        }
        auto v159 = Sqrt(v158);
        auto v160 = Sqrt(v153);
        auto v161 = Mul(v159, v160);
        auto v162 = Div(v156, v161);
        auto v163 = SetInfOrNanToValue(v162, 1.f);
        buf_1cf1f6a8d90ea2e78.store(i, v163);
        auto v164 = v163;
        buf_14c03c015491e6f07.store(i, v7);
        auto v165 = v7;
        buf_1859886e85061c544.store(i, v29);
        auto v166 = v29;
        buf_123e2c4ec6491301c.store(i, v62);
        auto v167 = v62;
    }
}





static void stage_alpha_101_stream__alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_a88237a38c9d018f{__ctx->buffers[280].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_4b88f8befc88e458{__ctx->buffers[323].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a231b2e05e8601d3{__ctx->buffers[319].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14c03c015491e6f07{__ctx->buffers[335].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> buf_13a8169589968006c{__ctx->buffers[333].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> buf_b096a6b338e59bd1{__ctx->buffers[229].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_562cec0cb33847c8{__ctx->buffers[325].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1859886e85061c544{__ctx->buffers[336].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_db7aef550751a1cd{__ctx->buffers[321].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1ad82bd2893556b95{__ctx->buffers[237].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_ac8b438bbe1cf240{__ctx->buffers[284].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> buf_381bba73519aac77{__ctx->buffers[276].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_19cc09703572f7b58{__ctx->buffers[89].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_82dce2338e7ee89f{__ctx->buffers[178].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha095{__ctx->buffers[83].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha065{__ctx->buffers[65].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha002{__ctx->buffers[7].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha066{__ctx->buffers[66].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha038{__ctx->buffers[43].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ad74e3f90fab1c5b{__ctx->buffers[351].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha004{__ctx->buffers[9].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha092{__ctx->buffers[81].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cd7e62b0652a2be4{__ctx->buffers[353].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_1f45d88fcb5643470{__ctx->buffers[354].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b87bba46739d9a98{__ctx->buffers[355].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 19> temp_55{__ctx->buffers[356].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_88{__ctx->buffers[357].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_92{__ctx->buffers[358].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_112{__ctx->buffers[359].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_137{__ctx->buffers[360].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_a88237a38c9d018f.step(i);
        auto v1 = buf_4b88f8befc88e458.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = buf_1a231b2e05e8601d3.step(i);
        auto v7 = buf_14c03c015491e6f07.step(i);
        auto v8 = LessThan(v6, v7);
        auto v9 = Select(v8, v3, v4);
        buf_alpha095.store(i, v9);
        auto v10 = v9;
        auto v11 = Sub(0.f, v5);
        buf_alpha065.store(i, v11);
        auto v12 = v11;
        auto v13 = buf_13a8169589968006c.step(i);
        ReduceAdd<float, 8> v16{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v15 = buf_13a8169589968006c.getWindow(i, iter);
            v16.step(v15, iter);
        }
        auto v17 = Div(v16, 6.f);
        auto v18 = buf_b096a6b338e59bd1.step(i);
        ReduceAdd<float, 8> v21{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v20 = buf_b096a6b338e59bd1.getWindow(i, iter);
            v21.step(v20, iter);
        }
        auto v22 = Div(v21, 6.f);
        ReduceAdd<float, 8> v28{};
        ReduceAdd<float, 8> v31{};
        ReduceAdd<float, 8> v33{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v24 = buf_13a8169589968006c.getWindow(i, iter);
            auto v25 = Sub(v24, v17);
            auto v26 = buf_b096a6b338e59bd1.getWindow(i, iter);
            auto v27 = Mul(v25, v25);
            v28.step(v27, iter);
            auto v29 = Sub(v26, v22);
            auto v30 = Mul(v25, v29);
            v31.step(v30, iter);
            auto v32 = Mul(v29, v29);
            v33.step(v32, iter);
        }
        auto v34 = Sqrt(v33);
        auto v35 = Sqrt(v28);
        auto v36 = Mul(v35, v34);
        auto v37 = Div(v31, v36);
        auto v38 = Sub(0.f, v37);
        auto v39 = SetInfOrNanToValue(v38, 0.0f);
        buf_alpha002.store(i, v39);
        auto v40 = v39;
        auto v41 = buf_562cec0cb33847c8.step(i);
        auto v42 = buf_1859886e85061c544.step(i);
        auto v43 = Add(v41, v42);
        auto v44 = Sub(0.f, v43);
        buf_alpha066.store(i, v44);
        auto v45 = v44;
        auto v46 = buf_db7aef550751a1cd.step(i);
        auto v47 = Sub(0.f, v46);
        auto v48 = buf_1ad82bd2893556b95.step(i);
        auto v49 = Mul(v47, v48);
        buf_alpha038.store(i, v49);
        auto v50 = v49;
        auto v51 = buf_ac8b438bbe1cf240.step(i);
        ReduceDecayLinear<float, 8, 15> v54{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v53 = buf_ac8b438bbe1cf240.getWindow(i, iter);
            v54.step(v53, iter);
        }
        temp_55.store(i, v54);
        auto v55 = v54;
        ReduceRank<float, 8> v58{v54};
        for(int iter = 18;iter >= 0;iter--) {
            auto v57 = temp_55.getWindow(i, iter);
            v58.step(v57, iter);
        }
        auto v59 = buf_381bba73519aac77.step(i);
        ReduceAdd<float, 8> v62{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v61 = buf_381bba73519aac77.getWindow(i, iter);
            v62.step(v61, iter);
        }
        auto v63 = Div(v62, 8.f);
        auto v64 = buf_19cc09703572f7b58.step(i);
        ReduceAdd<float, 8> v67{};
        for(int iter = 11;iter >= 8;iter--) {
            auto v66 = buf_19cc09703572f7b58.getWindow(i, iter);
            v67.step(v66, iter);
        }
        ReduceAdd<float, 8> v70{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v66 = buf_19cc09703572f7b58.getWindow(i, iter);
            v67.step(v66, iter);
            auto v69 = buf_19cc09703572f7b58.getWindow(i, iter);
            v70.step(v69, iter);
        }
        auto v71 = Div(v70, 8.f);
        ReduceAdd<float, 8> v77{};
        ReduceAdd<float, 8> v80{};
        ReduceAdd<float, 8> v82{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v73 = buf_381bba73519aac77.getWindow(i, iter);
            auto v74 = Sub(v73, v63);
            auto v75 = buf_19cc09703572f7b58.getWindow(i, iter);
            auto v76 = Mul(v74, v74);
            v77.step(v76, iter);
            auto v78 = Sub(v75, v71);
            auto v79 = Mul(v78, v74);
            v80.step(v79, iter);
            auto v81 = Mul(v78, v78);
            v82.step(v81, iter);
        }
        auto v83 = Sqrt(v82);
        auto v84 = Sqrt(v77);
        auto v85 = Mul(v83, v84);
        auto v86 = Div(v80, v85);
        auto v87 = SetInfOrNanToValue(v86, 0.0f);
        temp_88.store(i, v87);
        auto v88 = v87;
        ReduceDecayLinear<float, 8, 7> v91{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v90 = temp_88.getWindow(i, iter);
            v91.step(v90, iter);
        }
        temp_92.store(i, v91);
        auto v92 = v91;
        ReduceRank<float, 8> v95{v91};
        for(int iter = 6;iter >= 0;iter--) {
            auto v94 = temp_92.getWindow(i, iter);
            v95.step(v94, iter);
        }
        ReduceRank<float, 8> v98{v64};
        for(int iter = 8;iter >= 0;iter--) {
            auto v97 = buf_19cc09703572f7b58.getWindow(i, iter);
            v98.step(v97, iter);
        }
        auto v99 = Div(v67, 12.f);
        auto v100 = buf_volume.step(i);
        ReduceAdd<float, 8> v103{};
        for(int iter = 49;iter >= 0;iter--) {
            auto v102 = buf_volume.getWindow(i, iter);
            v103.step(v102, iter);
        }
        buf_ad74e3f90fab1c5b.store(i, v103);
        auto v104 = v103;
        auto v105 = Sub(0.f, v98);
        buf_alpha004.store(i, v105);
        auto v106 = v105;
        auto v107 = Min(v95, v58);
        buf_alpha092.store(i, v107);
        auto v108 = v107;
        ReduceRank<float, 8> v111{v100};
        for(int iter = 18;iter >= 0;iter--) {
            auto v110 = buf_volume.getWindow(i, iter);
            v111.step(v110, iter);
        }
        temp_112.store(i, v111);
        auto v112 = v111;
        ReduceAdd<float, 8> v115{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v114 = temp_112.getWindow(i, iter);
            v115.step(v114, iter);
        }
        auto v116 = Div(v115, 7.f);
        auto v117 = buf_82dce2338e7ee89f.step(i);
        ReduceAdd<float, 8> v120{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v119 = buf_82dce2338e7ee89f.getWindow(i, iter);
            v120.step(v119, iter);
        }
        auto v121 = Div(v120, 7.f);
        ReduceAdd<float, 8> v127{};
        ReduceAdd<float, 8> v130{};
        ReduceAdd<float, 8> v132{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v123 = temp_112.getWindow(i, iter);
            auto v124 = Sub(v123, v116);
            auto v125 = buf_82dce2338e7ee89f.getWindow(i, iter);
            auto v126 = Mul(v124, v124);
            v127.step(v126, iter);
            auto v128 = Sub(v125, v121);
            auto v129 = Mul(v128, v124);
            v130.step(v129, iter);
            auto v131 = Mul(v128, v128);
            v132.step(v131, iter);
        }
        auto v133 = Sqrt(v132);
        auto v134 = Sqrt(v127);
        auto v135 = Mul(v133, v134);
        auto v136 = Div(v130, v135);
        temp_137.store(i, v136);
        auto v137 = v136;
        ReduceDecayLinear<float, 8, 3> v140{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v139 = temp_137.getWindow(i, iter);
            v140.step(v139, iter);
        }
        buf_cd7e62b0652a2be4.store(i, v140);
        auto v141 = v140;
        buf_1f45d88fcb5643470.store(i, v64);
        auto v142 = v64;
        buf_1b87bba46739d9a98.store(i, v99);
        auto v143 = v99;
    }
}

















static void stage_alpha_101_stream__5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_52b6ff208174d149{__ctx->buffers[361].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ca86c5db4dae4c75{__ctx->buffers[285].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d4580520b32d9d74{__ctx->buffers[286].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_507411a9986abb68{__ctx->buffers[352].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b87bba46739d9a98{__ctx->buffers[355].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_1f45d88fcb5643470{__ctx->buffers[354].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> buf_c7f03cfe4a9a0496{__ctx->buffers[287].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_low{__ctx->buffers[0].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_11ba1c0582ed7958d{__ctx->buffers[288].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 18> buf_1b38965bcd2746ceb{__ctx->buffers[179].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_high{__ctx->buffers[1].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_149416df8d2c4dac{__ctx->buffers[281].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1163db3251b923ec7{__ctx->buffers[252].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 180> buf_volume{__ctx->buffers[5].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_161e738cf1eba2c95{__ctx->buffers[109].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e2c0a8e1c6ba083a{__ctx->buffers[110].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c062f9bb82def253{__ctx->buffers[180].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_5bb02c19c3dd6729{__ctx->buffers[362].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1107e617187eb74dc{__ctx->buffers[364].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a07b0431ada8801a{__ctx->buffers[366].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17428b32875256d19{__ctx->buffers[368].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_115ae56c1acc5da93{__ctx->buffers[370].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_16ebd20a45d3e92fe{__ctx->buffers[376].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha026{__ctx->buffers[31].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c6a5b1cc4b64300a{__ctx->buffers[372].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15264260244c74405{__ctx->buffers[374].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cbfb1d0ffb904289{__ctx->buffers[377].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12c2c23e763e94135{__ctx->buffers[378].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_fc74c311397bb045{__ctx->buffers[379].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_165fc323207ecacad{__ctx->buffers[380].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_64394211e6651f69{__ctx->buffers[381].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_3af437316f85598d{__ctx->buffers[382].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 10> temp_5{__ctx->buffers[383].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> temp_49{__ctx->buffers[384].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_53{__ctx->buffers[385].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_74{__ctx->buffers[386].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 4> temp_83{__ctx->buffers[387].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_132{__ctx->buffers[388].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_138{__ctx->buffers[389].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> temp_143{__ctx->buffers[390].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 13> temp_147{__ctx->buffers[391].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> temp_166{__ctx->buffers[392].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_175{__ctx->buffers[393].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_205{__ctx->buffers[394].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_52b6ff208174d149.step(i);
        auto v1 = Add(v0, 0.0001f);
        auto v2 = buf_ca86c5db4dae4c75.step(i);
        auto v3 = buf_1d4580520b32d9d74.step(i);
        auto v4 = Div(v2, v3);
        temp_5.store(i, v4);
        auto v5 = v4;
        ReduceDecayLinear<float, 8, 10> v8{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v7 = temp_5.getWindow(i, iter);
            v8.step(v7, iter);
        }
        buf_5bb02c19c3dd6729.store(i, v8);
        auto v9 = v8;
        auto v10 = buf_507411a9986abb68.step(i);
        ReduceAdd<float, 8> v13{};
        for(int iter = 11;iter >= 0;iter--) {
            auto v12 = buf_507411a9986abb68.getWindow(i, iter);
            v13.step(v12, iter);
        }
        auto v14 = buf_1b87bba46739d9a98.step(i);
        auto v15 = buf_1f45d88fcb5643470.step(i);
        auto v16 = Div(v13, 12.f);
        ReduceAdd<float, 8> v22{};
        ReduceAdd<float, 8> v25{};
        ReduceAdd<float, 8> v27{};
        for(int iter = 11;iter >= 0;iter--) {
            auto v18 = buf_507411a9986abb68.getWindow(i, iter);
            auto v19 = buf_1f45d88fcb5643470.getWindow(i, iter);
            auto v20 = Sub(v19, v14);
            auto v21 = Mul(v20, v20);
            v22.step(v21, iter);
            auto v23 = Sub(v18, v16);
            auto v24 = Mul(v20, v23);
            v25.step(v24, iter);
            auto v26 = Mul(v23, v23);
            v27.step(v26, iter);
        }
        auto v28 = Sqrt(v27);
        auto v29 = Sqrt(v22);
        auto v30 = Mul(v29, v28);
        auto v31 = Div(v25, v30);
        buf_1107e617187eb74dc.store(i, v31);
        auto v32 = v31;
        auto v33 = buf_c7f03cfe4a9a0496.step(i);
        ReduceAdd<float, 8> v36{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v35 = buf_c7f03cfe4a9a0496.getWindow(i, iter);
            v36.step(v35, iter);
        }
        auto v37 = Div(v36, 7.f);
        auto v38 = buf_e0e3079c4a901fa8.step(i);
        auto v39 = Mul(v38, 0.647767f);
        auto v40 = buf_low.step(i);
        ReduceMin<float, 8> v43{};
        for(int iter = 11;iter >= 5;iter--) {
            auto v42 = buf_low.getWindow(i, iter);
            v43.step(v42, iter);
        }
        ReduceMin<float, 8> v46{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v42 = buf_low.getWindow(i, iter);
            v43.step(v42, iter);
            auto v45 = buf_low.getWindow(i, iter);
            v46.step(v45, iter);
        }
        auto v47 = Mul(v40, 0.352233f);
        auto v48 = Add(v47, v39);
        temp_49.store(i, v48);
        auto v49 = v48;
        ReduceAdd<float, 8> v52{};
        for(int iter = 19;iter >= 0;iter--) {
            auto v51 = temp_49.getWindow(i, iter);
            v52.step(v51, iter);
        }
        temp_53.store(i, v52);
        auto v53 = v52;
        ReduceAdd<float, 8> v56{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v55 = temp_53.getWindow(i, iter);
            v56.step(v55, iter);
        }
        auto v57 = Div(v56, 7.f);
        ReduceAdd<float, 8> v63{};
        ReduceAdd<float, 8> v66{};
        ReduceAdd<float, 8> v68{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v59 = buf_c7f03cfe4a9a0496.getWindow(i, iter);
            auto v60 = Sub(v59, v37);
            auto v61 = temp_53.getWindow(i, iter);
            auto v62 = Mul(v60, v60);
            v63.step(v62, iter);
            auto v64 = Sub(v61, v57);
            auto v65 = Mul(v64, v60);
            v66.step(v65, iter);
            auto v67 = Mul(v64, v64);
            v68.step(v67, iter);
        }
        auto v69 = Sqrt(v68);
        auto v70 = Sqrt(v63);
        auto v71 = Mul(v69, v70);
        auto v72 = Div(v66, v71);
        buf_1a07b0431ada8801a.store(i, v72);
        auto v73 = v72;
        temp_74.store(i, v46);
        auto v74 = v46;
        auto v75 = windowedRef<float, 8, 5>(temp_74, i);
        auto v76 = Sub(v46, v75);
        auto v77 = Sub(0.f, v76);
        auto v78 = buf_close.step(i);
        auto v79 = Sub(v78, v43);
        ReduceRank<float, 8> v82{v78};
        for(int iter = 6;iter >= 0;iter--) {
            auto v81 = buf_close.getWindow(i, iter);
            v82.step(v81, iter);
        }
        temp_83.store(i, v82);
        auto v83 = v82;
        ReduceAdd<float, 8> v86{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v85 = temp_83.getWindow(i, iter);
            v86.step(v85, iter);
        }
        auto v87 = Div(v86, 4.f);
        auto v88 = buf_11ba1c0582ed7958d.step(i);
        ReduceAdd<float, 8> v91{};
        for(int iter = 17;iter >= 4;iter--) {
            auto v90 = buf_11ba1c0582ed7958d.getWindow(i, iter);
            v91.step(v90, iter);
        }
        ReduceAdd<float, 8> v94{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v90 = buf_11ba1c0582ed7958d.getWindow(i, iter);
            v91.step(v90, iter);
            auto v93 = buf_11ba1c0582ed7958d.getWindow(i, iter);
            v94.step(v93, iter);
        }
        auto v95 = Div(v94, 4.f);
        ReduceAdd<float, 8> v101{};
        ReduceAdd<float, 8> v104{};
        ReduceAdd<float, 8> v106{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v97 = temp_83.getWindow(i, iter);
            auto v98 = Sub(v97, v87);
            auto v99 = buf_11ba1c0582ed7958d.getWindow(i, iter);
            auto v100 = Mul(v98, v98);
            v101.step(v100, iter);
            auto v102 = Sub(v99, v95);
            auto v103 = Mul(v98, v102);
            v104.step(v103, iter);
            auto v105 = Mul(v102, v102);
            v106.step(v105, iter);
        }
        auto v107 = Sqrt(v106);
        auto v108 = Sqrt(v101);
        auto v109 = Mul(v108, v107);
        auto v110 = Div(v91, 18.f);
        auto v111 = buf_1b38965bcd2746ceb.step(i);
        ReduceAdd<float, 8> v114{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v113 = buf_1b38965bcd2746ceb.getWindow(i, iter);
            v114.step(v113, iter);
        }
        auto v115 = Div(v114, 18.f);
        ReduceAdd<float, 8> v121{};
        ReduceAdd<float, 8> v124{};
        ReduceAdd<float, 8> v126{};
        for(int iter = 17;iter >= 0;iter--) {
            auto v117 = buf_11ba1c0582ed7958d.getWindow(i, iter);
            auto v118 = Sub(v117, v110);
            auto v119 = buf_1b38965bcd2746ceb.getWindow(i, iter);
            auto v120 = Mul(v118, v118);
            v121.step(v120, iter);
            auto v122 = Sub(v119, v115);
            auto v123 = Mul(v122, v118);
            v124.step(v123, iter);
            auto v125 = Mul(v122, v122);
            v126.step(v125, iter);
        }
        auto v127 = Sqrt(v126);
        auto v128 = Sqrt(v121);
        auto v129 = Mul(v127, v128);
        auto v130 = Div(v124, v129);
        auto v131 = SetInfOrNanToValue(v130, 0.0f);
        temp_132.store(i, v131);
        auto v132 = v131;
        ReduceRank<float, 8> v135{v131};
        for(int iter = 2;iter >= 0;iter--) {
            auto v134 = temp_132.getWindow(i, iter);
            v135.step(v134, iter);
        }
        auto v136 = Div(v104, v109);
        auto v137 = SetInfOrNanToValue(v136, 0.0f);
        temp_138.store(i, v137);
        auto v138 = v137;
        ReduceArgMax<float, 8> v141{};
        for(int iter = 12;iter >= 0;iter--) {
            auto v140 = temp_138.getWindow(i, iter);
            v141.step(v140, iter);
        }
        auto v142 = Sub(13.f, v141);
        temp_143.store(i, v142);
        auto v143 = v142;
        ReduceDecayLinear<float, 8, 14> v146{};
        for(int iter = 13;iter >= 0;iter--) {
            auto v145 = temp_143.getWindow(i, iter);
            v146.step(v145, iter);
        }
        temp_147.store(i, v146);
        auto v147 = v146;
        ReduceRank<float, 8> v150{v146};
        for(int iter = 12;iter >= 0;iter--) {
            auto v149 = temp_147.getWindow(i, iter);
            v150.step(v149, iter);
        }
        auto v151 = buf_high.step(i);
        ReduceMax<float, 8> v154{};
        for(int iter = 11;iter >= 0;iter--) {
            auto v153 = buf_high.getWindow(i, iter);
            v154.step(v153, iter);
        }
        auto v155 = Sub(v154, v43);
        auto v156 = constVec<8>(0.f);
        auto v157 = Equals(v155, v156);
        auto v158 = constVec<8>(0.0001f);
        auto v159 = Select(v157, v158, v155);
        auto v160 = Div(v79, v159);
        buf_17428b32875256d19.store(i, v160);
        auto v161 = v160;
        auto v162 = buf_149416df8d2c4dac.step(i);
        auto v163 = Add(v162, v151);
        auto v164 = buf_1163db3251b923ec7.step(i);
        auto v165 = Sub(v163, v164);
        temp_166.store(i, v165);
        auto v166 = v165;
        ReduceDecayLinear<float, 8, 20> v169{};
        for(int iter = 19;iter >= 0;iter--) {
            auto v168 = temp_166.getWindow(i, iter);
            v169.step(v168, iter);
        }
        buf_115ae56c1acc5da93.store(i, v169);
        auto v170 = v169;
        auto v171 = Mul(v151, 0.0261661f);
        ReduceRank<float, 8> v174{v151};
        for(int iter = 4;iter >= 0;iter--) {
            auto v173 = buf_high.getWindow(i, iter);
            v174.step(v173, iter);
        }
        temp_175.store(i, v174);
        auto v175 = v174;
        ReduceAdd<float, 8> v178{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v177 = temp_175.getWindow(i, iter);
            v178.step(v177, iter);
        }
        auto v179 = Div(v178, 5.f);
        auto v180 = buf_volume.step(i);
        ReduceRank<float, 8> v183{v180};
        for(int iter = 4;iter >= 0;iter--) {
            auto v182 = buf_volume.getWindow(i, iter);
            v183.step(v182, iter);
        }
        buf_16ebd20a45d3e92fe.store(i, v183);
        auto v184 = v183;
        ReduceAdd<float, 8> v187{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v186 = buf_16ebd20a45d3e92fe.getWindow(i, iter);
            v187.step(v186, iter);
        }
        auto v188 = Div(v187, 5.f);
        ReduceAdd<float, 8> v194{};
        ReduceAdd<float, 8> v197{};
        ReduceAdd<float, 8> v199{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v190 = temp_175.getWindow(i, iter);
            auto v191 = Sub(v190, v179);
            auto v192 = buf_16ebd20a45d3e92fe.getWindow(i, iter);
            auto v193 = Mul(v191, v191);
            v194.step(v193, iter);
            auto v195 = Sub(v192, v188);
            auto v196 = Mul(v195, v191);
            v197.step(v196, iter);
            auto v198 = Mul(v195, v195);
            v199.step(v198, iter);
        }
        auto v200 = Sqrt(v199);
        auto v201 = Sqrt(v194);
        auto v202 = Mul(v200, v201);
        auto v203 = Div(v197, v202);
        auto v204 = SetInfOrNanToValue(v203, 0.0f);
        temp_205.store(i, v204);
        auto v205 = v204;
        ReduceMax<float, 8> v208{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v207 = temp_205.getWindow(i, iter);
            v208.step(v207, iter);
        }
        auto v209 = Sub(0.f, v208);
        buf_alpha026.store(i, v209);
        auto v210 = v209;
        auto v211 = buf_161e738cf1eba2c95.step(i);
        auto v212 = buf_e2c0a8e1c6ba083a.step(i);
        auto v213 = Sub(v211, v212);
        buf_c6a5b1cc4b64300a.store(i, v213);
        auto v214 = v213;
        ReduceAdd<float, 8> v217{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v216 = buf_volume.getWindow(i, iter);
            v217.step(v216, iter);
        }
        buf_15264260244c74405.store(i, v217);
        auto v218 = v217;
        auto v219 = buf_c062f9bb82def253.step(i);
        auto v220 = Sub(v38, v219);
        buf_cbfb1d0ffb904289.store(i, v220);
        auto v221 = v220;
        buf_12c2c23e763e94135.store(i, v135);
        auto v222 = v135;
        buf_fc74c311397bb045.store(i, v77);
        auto v223 = v77;
        buf_165fc323207ecacad.store(i, v171);
        auto v224 = v171;
        buf_64394211e6651f69.store(i, v150);
        auto v225 = v150;
        buf_3af437316f85598d.store(i, v1);
        auto v226 = v1;
    }
}











static void stage_alpha_101_stream__alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_186e7dd3a2080800a{__ctx->buffers[395].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12c2c23e763e94135{__ctx->buffers[378].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_fc74c311397bb045{__ctx->buffers[379].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_226e640ee8e31e3d{__ctx->buffers[373].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_16ebd20a45d3e92fe{__ctx->buffers[376].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 21> buf_e05c149cd44339a3{__ctx->buffers[375].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 21> buf_1f0f01f1c83584d6f{__ctx->buffers[88].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> buf_17237c9420bba7634{__ctx->buffers[97].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> buf_1bcc1d690ec58df7f{__ctx->buffers[160].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> buf_120d49c2693eb0b8f{__ctx->buffers[90].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_high{__ctx->buffers[1].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1015175dbdfa9818a{__ctx->buffers[253].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c125ece16202104b{__ctx->buffers[254].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> buf_eb6ce72b67e4317e{__ctx->buffers[369].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_60b7aa4774c90f7a{__ctx->buffers[98].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_165fc323207ecacad{__ctx->buffers[380].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e714ed386b6c8ec3{__ctx->buffers[265].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha094{__ctx->buffers[82].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha052{__ctx->buffers[56].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e0868dec9669359f{__ctx->buffers[396].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha055{__ctx->buffers[59].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_fc30d8436cd2e6f0{__ctx->buffers[398].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha003{__ctx->buffers[8].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15d7d8ba49746a937{__ctx->buffers[400].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1fa7018abf0f3a453{__ctx->buffers[402].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha068{__ctx->buffers[67].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17bd17f7435f12bee{__ctx->buffers[404].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> buf_1693aa3aaeb129a60{__ctx->buffers[405].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dad364f59a26ac50{__ctx->buffers[406].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dfe1845a1a8fb41d{__ctx->buffers[407].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_164447fd516042841{__ctx->buffers[408].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_10bf852bbded84010{__ctx->buffers[409].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_140f4347a2e0bf204{__ctx->buffers[410].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cfbdcaaf4ede80b1{__ctx->buffers[411].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1406245cbf0a5c46f{__ctx->buffers[412].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_137906673daae4438{__ctx->buffers[413].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17832f3ff2a94f427{__ctx->buffers[414].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d43b462e5d69ba5d{__ctx->buffers[415].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2b4deadd228e75a9{__ctx->buffers[416].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_13f9e1b099bf55621{__ctx->buffers[417].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_95b7f4bc0dee6ab3{__ctx->buffers[418].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 14> temp_280{__ctx->buffers[419].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_186e7dd3a2080800a.step(i);
        auto v1 = LogFast(v0);
        auto v2 = buf_12c2c23e763e94135.step(i);
        auto v3 = Mul(v2, v1);
        auto v4 = Exp(v3);
        auto v5 = Sub(0.f, v4);
        buf_alpha094.store(i, v5);
        auto v6 = v5;
        auto v7 = buf_fc74c311397bb045.step(i);
        auto v8 = buf_226e640ee8e31e3d.step(i);
        auto v9 = Mul(v7, v8);
        auto v10 = buf_16ebd20a45d3e92fe.step(i);
        auto v11 = Mul(v9, v10);
        buf_alpha052.store(i, v11);
        auto v12 = v11;
        auto v13 = buf_e05c149cd44339a3.step(i);
        ReduceAdd<float, 8> v16{};
        for(int iter = 20;iter >= 9;iter--) {
            auto v15 = buf_e05c149cd44339a3.getWindow(i, iter);
            v16.step(v15, iter);
        }
        ReduceAdd<float, 8> v19{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v15 = buf_e05c149cd44339a3.getWindow(i, iter);
            v16.step(v15, iter);
            auto v18 = buf_e05c149cd44339a3.getWindow(i, iter);
            v19.step(v18, iter);
        }
        auto v20 = Div(v16, 21.f);
        auto v21 = buf_1f0f01f1c83584d6f.step(i);
        ReduceAdd<float, 8> v24{};
        for(int iter = 20;iter >= 10;iter--) {
            auto v23 = buf_1f0f01f1c83584d6f.getWindow(i, iter);
            v24.step(v23, iter);
        }
        ReduceAdd<float, 8> v27{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v23 = buf_1f0f01f1c83584d6f.getWindow(i, iter);
            v24.step(v23, iter);
            auto v26 = buf_1f0f01f1c83584d6f.getWindow(i, iter);
            v27.step(v26, iter);
        }
        auto v28 = Div(v24, 21.f);
        ReduceAdd<float, 8> v34{};
        ReduceAdd<float, 8> v37{};
        ReduceAdd<float, 8> v39{};
        for(int iter = 20;iter >= 0;iter--) {
            auto v30 = buf_e05c149cd44339a3.getWindow(i, iter);
            auto v31 = Sub(v30, v20);
            auto v32 = buf_1f0f01f1c83584d6f.getWindow(i, iter);
            auto v33 = Mul(v31, v31);
            v34.step(v33, iter);
            auto v35 = Sub(v32, v28);
            auto v36 = Mul(v35, v31);
            v37.step(v36, iter);
            auto v38 = Mul(v35, v35);
            v39.step(v38, iter);
        }
        auto v40 = Sqrt(v39);
        auto v41 = Sqrt(v34);
        auto v42 = Mul(v40, v41);
        auto v43 = Div(v27, 10.f);
        auto v44 = buf_17237c9420bba7634.step(i);
        ReduceAdd<float, 8> v47{};
        for(int iter = 10;iter >= 10;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
        }
        ReduceAdd<float, 8> v50{};
        for(int iter = 9;iter >= 6;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_17237c9420bba7634.getWindow(i, iter);
            v50.step(v49, iter);
        }
        ReduceAdd<float, 8> v53{};
        for(int iter = 5;iter >= 5;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_17237c9420bba7634.getWindow(i, iter);
            v50.step(v49, iter);
            auto v52 = buf_17237c9420bba7634.getWindow(i, iter);
            v53.step(v52, iter);
        }
        ReduceAdd<float, 8> v56{};
        for(int iter = 4;iter >= 4;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_17237c9420bba7634.getWindow(i, iter);
            v50.step(v49, iter);
            auto v52 = buf_17237c9420bba7634.getWindow(i, iter);
            v53.step(v52, iter);
            auto v55 = buf_17237c9420bba7634.getWindow(i, iter);
            v56.step(v55, iter);
        }
        ReduceAdd<float, 8> v59{};
        for(int iter = 3;iter >= 3;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_17237c9420bba7634.getWindow(i, iter);
            v50.step(v49, iter);
            auto v52 = buf_17237c9420bba7634.getWindow(i, iter);
            v53.step(v52, iter);
            auto v55 = buf_17237c9420bba7634.getWindow(i, iter);
            v56.step(v55, iter);
            auto v58 = buf_17237c9420bba7634.getWindow(i, iter);
            v59.step(v58, iter);
        }
        ReduceAdd<float, 8> v62{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v46 = buf_17237c9420bba7634.getWindow(i, iter);
            v47.step(v46, iter);
            auto v49 = buf_17237c9420bba7634.getWindow(i, iter);
            v50.step(v49, iter);
            auto v52 = buf_17237c9420bba7634.getWindow(i, iter);
            v53.step(v52, iter);
            auto v55 = buf_17237c9420bba7634.getWindow(i, iter);
            v56.step(v55, iter);
            auto v58 = buf_17237c9420bba7634.getWindow(i, iter);
            v59.step(v58, iter);
            auto v61 = buf_17237c9420bba7634.getWindow(i, iter);
            v62.step(v61, iter);
        }
        auto v63 = buf_1bcc1d690ec58df7f.step(i);
        ReduceAdd<float, 8> v66{};
        for(int iter = 5;iter >= 5;iter--) {
            auto v65 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v66.step(v65, iter);
        }
        ReduceAdd<float, 8> v69{};
        for(int iter = 4;iter >= 4;iter--) {
            auto v65 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v66.step(v65, iter);
            auto v68 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v69.step(v68, iter);
        }
        ReduceAdd<float, 8> v72{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v65 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v66.step(v65, iter);
            auto v68 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v69.step(v68, iter);
            auto v71 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            v72.step(v71, iter);
        }
        auto v73 = buf_120d49c2693eb0b8f.step(i);
        ReduceAdd<float, 8> v76{};
        for(int iter = 8;iter >= 5;iter--) {
            auto v75 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v76.step(v75, iter);
        }
        ReduceAdd<float, 8> v79{};
        for(int iter = 4;iter >= 3;iter--) {
            auto v75 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v76.step(v75, iter);
            auto v78 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v79.step(v78, iter);
        }
        ReduceAdd<float, 8> v82{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v75 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v76.step(v75, iter);
            auto v78 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v79.step(v78, iter);
            auto v81 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            v82.step(v81, iter);
        }
        auto v83 = Div(v50, 10.f);
        ReduceAdd<float, 8> v89{};
        ReduceAdd<float, 8> v92{};
        ReduceAdd<float, 8> v94{};
        for(int iter = 9;iter >= 0;iter--) {
            auto v85 = buf_1f0f01f1c83584d6f.getWindow(i, iter);
            auto v86 = Sub(v85, v43);
            auto v87 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v88 = Mul(v86, v86);
            v89.step(v88, iter);
            auto v90 = Sub(v87, v83);
            auto v91 = Mul(v86, v90);
            v92.step(v91, iter);
            auto v93 = Mul(v90, v90);
            v94.step(v93, iter);
        }
        auto v95 = Div(v79, 5.f);
        auto v96 = Div(v56, 5.f);
        ReduceAdd<float, 8> v103{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v98 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v99 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            auto v100 = Sub(v99, v95);
            auto v101 = Sub(v98, v96);
            auto v102 = Mul(v100, v101);
            v103.step(v102, iter);
        }
        auto v104 = Div(v69, 5.f);
        ReduceAdd<float, 8> v110{};
        ReduceAdd<float, 8> v113{};
        ReduceAdd<float, 8> v115{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v106 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v107 = Sub(v106, v96);
            auto v108 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            auto v109 = Mul(v107, v107);
            v110.step(v109, iter);
            auto v111 = Sub(v108, v104);
            auto v112 = Mul(v111, v107);
            v113.step(v112, iter);
            auto v114 = Mul(v111, v111);
            v115.step(v114, iter);
        }
        ReduceAdd<float, 8> v122{};
        ReduceAdd<float, 8> v124{};
        ReduceAdd<float, 8> v126{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v117 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            auto v118 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v119 = Sub(v118, v96);
            auto v120 = Sub(v117, v104);
            auto v121 = Mul(v119, v120);
            v122.step(v121, iter);
            auto v123 = Mul(v120, v120);
            v124.step(v123, iter);
            auto v125 = Mul(v119, v119);
            v126.step(v125, iter);
        }
        auto v127 = Div(v19, 9.f);
        auto v128 = Div(v76, 9.f);
        ReduceAdd<float, 8> v134{};
        ReduceAdd<float, 8> v137{};
        ReduceAdd<float, 8> v139{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v130 = buf_e05c149cd44339a3.getWindow(i, iter);
            auto v131 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            auto v132 = Sub(v131, v128);
            auto v133 = Mul(v132, v132);
            v134.step(v133, iter);
            auto v135 = Sub(v130, v127);
            auto v136 = Mul(v132, v135);
            v137.step(v136, iter);
            auto v138 = Mul(v135, v135);
            v139.step(v138, iter);
        }
        auto v140 = Div(v82, 3.f);
        auto v141 = Div(v62, 3.f);
        ReduceAdd<float, 8> v147{};
        ReduceAdd<float, 8> v150{};
        ReduceAdd<float, 8> v152{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v143 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v144 = buf_120d49c2693eb0b8f.getWindow(i, iter);
            auto v145 = Sub(v143, v141);
            auto v146 = Mul(v145, v145);
            v147.step(v146, iter);
            auto v148 = Sub(v144, v140);
            auto v149 = Mul(v148, v145);
            v150.step(v149, iter);
            auto v151 = Mul(v148, v148);
            v152.step(v151, iter);
        }
        auto v153 = Div(v66, 6.f);
        auto v154 = Div(v53, 6.f);
        ReduceAdd<float, 8> v160{};
        ReduceAdd<float, 8> v163{};
        ReduceAdd<float, 8> v165{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v156 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v157 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            auto v158 = Sub(v156, v154);
            auto v159 = Mul(v158, v158);
            v160.step(v159, iter);
            auto v161 = Sub(v157, v153);
            auto v162 = Mul(v161, v158);
            v163.step(v162, iter);
            auto v164 = Mul(v161, v161);
            v165.step(v164, iter);
        }
        ReduceAdd<float, 8> v172{};
        ReduceAdd<float, 8> v174{};
        ReduceAdd<float, 8> v176{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v167 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            auto v168 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v169 = Sub(v168, v154);
            auto v170 = Sub(v167, v153);
            auto v171 = Mul(v169, v170);
            v172.step(v171, iter);
            auto v173 = Mul(v170, v170);
            v174.step(v173, iter);
            auto v175 = Mul(v169, v169);
            v176.step(v175, iter);
        }
        auto v177 = buf_high.step(i);
        auto v178 = buf_1015175dbdfa9818a.step(i);
        ReduceAdd<float, 8> v185{};
        ReduceAdd<float, 8> v187{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v180 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v181 = Sub(v180, v96);
            auto v182 = buf_high.getWindow(i, iter);
            auto v183 = Sub(v182, v178);
            auto v184 = Mul(v183, v181);
            v185.step(v184, iter);
            auto v186 = Mul(v181, v181);
            v187.step(v186, iter);
        }
        auto v188 = Div(v72, 4.f);
        auto v189 = Div(v59, 4.f);
        ReduceAdd<float, 8> v195{};
        ReduceAdd<float, 8> v198{};
        ReduceAdd<float, 8> v200{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v191 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v192 = buf_1bcc1d690ec58df7f.getWindow(i, iter);
            auto v193 = Sub(v191, v189);
            auto v194 = Mul(v193, v193);
            v195.step(v194, iter);
            auto v196 = Sub(v192, v188);
            auto v197 = Mul(v196, v193);
            v198.step(v197, iter);
            auto v199 = Mul(v196, v196);
            v200.step(v199, iter);
        }
        auto v201 = Sqrt(v200);
        auto v202 = Sqrt(v195);
        auto v203 = Mul(v201, v202);
        auto v204 = Sqrt(v187);
        auto v205 = buf_1c125ece16202104b.step(i);
        auto v206 = Mul(v205, v204);
        auto v207 = Sqrt(v176);
        auto v208 = Sqrt(v174);
        auto v209 = Mul(v207, v208);
        auto v210 = Sqrt(v165);
        auto v211 = Sqrt(v160);
        auto v212 = Mul(v210, v211);
        auto v213 = Sqrt(v152);
        auto v214 = Sqrt(v147);
        auto v215 = Mul(v213, v214);
        auto v216 = Sqrt(v139);
        auto v217 = Sqrt(v134);
        auto v218 = Mul(v217, v216);
        auto v219 = Sqrt(v126);
        auto v220 = Sqrt(v124);
        auto v221 = Mul(v219, v220);
        auto v222 = Sqrt(v115);
        auto v223 = Sqrt(v110);
        auto v224 = Mul(v222, v223);
        auto v225 = Sqrt(v94);
        auto v226 = Sqrt(v89);
        auto v227 = Mul(v226, v225);
        auto v228 = buf_eb6ce72b67e4317e.step(i);
        ReduceAdd<float, 8> v231{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v230 = buf_eb6ce72b67e4317e.getWindow(i, iter);
            v231.step(v230, iter);
        }
        auto v232 = Div(v231, 6.f);
        ReduceAdd<float, 8> v238{};
        ReduceAdd<float, 8> v241{};
        ReduceAdd<float, 8> v243{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v234 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v235 = Sub(v234, v154);
            auto v236 = buf_eb6ce72b67e4317e.getWindow(i, iter);
            auto v237 = Mul(v235, v235);
            v238.step(v237, iter);
            auto v239 = Sub(v236, v232);
            auto v240 = Mul(v239, v235);
            v241.step(v240, iter);
            auto v242 = Mul(v239, v239);
            v243.step(v242, iter);
        }
        auto v244 = Sqrt(v243);
        auto v245 = Sqrt(v238);
        auto v246 = Mul(v244, v245);
        auto v247 = buf_60b7aa4774c90f7a.step(i);
        ReduceAdd<float, 8> v250{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v249 = buf_60b7aa4774c90f7a.getWindow(i, iter);
            v250.step(v249, iter);
        }
        auto v251 = Div(v250, 5.f);
        ReduceAdd<float, 8> v258{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v253 = buf_17237c9420bba7634.getWindow(i, iter);
            auto v254 = Sub(v253, v96);
            auto v255 = buf_60b7aa4774c90f7a.getWindow(i, iter);
            auto v256 = Sub(v255, v251);
            auto v257 = Mul(v256, v254);
            v258.step(v257, iter);
        }
        buf_e0868dec9669359f.store(i, v258);
        auto v259 = v258;
        auto v260 = Div(v241, v246);
        auto v261 = SetInfOrNanToValue(v260, 0.0f);
        auto v262 = Sub(0.f, v261);
        buf_alpha055.store(i, v262);
        auto v263 = v262;
        auto v264 = Div(v47, 11.f);
        auto v265 = buf_e0e3079c4a901fa8.step(i);
        auto v266 = Mul(v265, 0.9738339f);
        auto v267 = buf_165fc323207ecacad.step(i);
        auto v268 = Add(v267, v266);
        buf_fc30d8436cd2e6f0.store(i, v268);
        auto v269 = v268;
        auto v270 = Div(v92, v227);
        auto v271 = Sub(0.f, v270);
        auto v272 = SetInfOrNanToValue(v271, 0.0f);
        buf_alpha003.store(i, v272);
        auto v273 = v272;
        auto v274 = Div(v113, v224);
        buf_15d7d8ba49746a937.store(i, v274);
        auto v275 = v274;
        auto v276 = Div(v122, v221);
        auto v277 = SetInfOrNanToValue(v276, 0.0f);
        buf_1fa7018abf0f3a453.store(i, v277);
        auto v278 = v277;
        auto v279 = Div(v137, v218);
        temp_280.store(i, v279);
        auto v280 = v279;
        ReduceRank<float, 8> v283{v279};
        for(int iter = 13;iter >= 0;iter--) {
            auto v282 = temp_280.getWindow(i, iter);
            v283.step(v282, iter);
        }
        auto v284 = buf_e714ed386b6c8ec3.step(i);
        auto v285 = LessThan(v283, v284);
        auto v286 = constVec<8>(1.f);
        auto v287 = constVec<8>(0.f);
        auto v288 = Select(v285, v286, v287);
        auto v289 = Sub(0.f, v288);
        buf_alpha068.store(i, v289);
        auto v290 = v289;
        buf_17bd17f7435f12bee.store(i, v103);
        auto v291 = v103;
        buf_1693aa3aaeb129a60.store(i, v44);
        auto v292 = v44;
        buf_dad364f59a26ac50.store(i, v264);
        auto v293 = v264;
        buf_dfe1845a1a8fb41d.store(i, v150);
        auto v294 = v150;
        buf_164447fd516042841.store(i, v215);
        auto v295 = v215;
        buf_10bf852bbded84010.store(i, v163);
        auto v296 = v163;
        buf_140f4347a2e0bf204.store(i, v212);
        auto v297 = v212;
        buf_cfbdcaaf4ede80b1.store(i, v172);
        auto v298 = v172;
        buf_1406245cbf0a5c46f.store(i, v209);
        auto v299 = v209;
        buf_137906673daae4438.store(i, v185);
        auto v300 = v185;
        buf_17832f3ff2a94f427.store(i, v206);
        auto v301 = v206;
        buf_1d43b462e5d69ba5d.store(i, v198);
        auto v302 = v198;
        buf_2b4deadd228e75a9.store(i, v203);
        auto v303 = v203;
        buf_13f9e1b099bf55621.store(i, v37);
        auto v304 = v37;
        buf_95b7f4bc0dee6ab3.store(i, v42);
        auto v305 = v42;
    }
}











static void stage_alpha_101_stream__alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1cfbffed3e23f4455{__ctx->buffers[420].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_1acaac361d6d31bdf{__ctx->buffers[403].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> buf_9fda089d0982e762{__ctx->buffers[399].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> buf_1693aa3aaeb129a60{__ctx->buffers[405].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dad364f59a26ac50{__ctx->buffers[406].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_85cedbf1ff9283ca{__ctx->buffers[397].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dfe1845a1a8fb41d{__ctx->buffers[407].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_164447fd516042841{__ctx->buffers[408].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_10bf852bbded84010{__ctx->buffers[409].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_140f4347a2e0bf204{__ctx->buffers[410].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_cfbdcaaf4ede80b1{__ctx->buffers[411].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1406245cbf0a5c46f{__ctx->buffers[412].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_137906673daae4438{__ctx->buffers[413].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17832f3ff2a94f427{__ctx->buffers[414].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d43b462e5d69ba5d{__ctx->buffers[415].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2b4deadd228e75a9{__ctx->buffers[416].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_64394211e6651f69{__ctx->buffers[381].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha016{__ctx->buffers[21].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha050{__ctx->buffers[54].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1494da145191886fa{__ctx->buffers[421].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha013{__ctx->buffers[18].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c983e545d91c9866{__ctx->buffers[423].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_0785974bf2709530{__ctx->buffers[425].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_aaf0b20fc619bbb2{__ctx->buffers[427].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha044{__ctx->buffers[49].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha096{__ctx->buffers[84].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> temp_48{__ctx->buffers[429].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 4> temp_63{__ctx->buffers[430].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_67{__ctx->buffers[431].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1cfbffed3e23f4455.step(i);
        auto v1 = Sub(0.f, v0);
        buf_alpha016.store(i, v1);
        auto v2 = v1;
        auto v3 = buf_1acaac361d6d31bdf.step(i);
        ReduceMax<float, 8> v6{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v5 = buf_1acaac361d6d31bdf.getWindow(i, iter);
            v6.step(v5, iter);
        }
        auto v7 = Sub(0.f, v6);
        buf_alpha050.store(i, v7);
        auto v8 = v7;
        auto v9 = buf_9fda089d0982e762.step(i);
        ReduceAdd<float, 8> v12{};
        for(int iter = 10;iter >= 0;iter--) {
            auto v11 = buf_9fda089d0982e762.getWindow(i, iter);
            v12.step(v11, iter);
        }
        auto v13 = Div(v12, 11.f);
        auto v14 = buf_1693aa3aaeb129a60.step(i);
        auto v15 = buf_dad364f59a26ac50.step(i);
        ReduceAdd<float, 8> v21{};
        ReduceAdd<float, 8> v24{};
        ReduceAdd<float, 8> v26{};
        for(int iter = 10;iter >= 0;iter--) {
            auto v17 = buf_1693aa3aaeb129a60.getWindow(i, iter);
            auto v18 = Sub(v17, v15);
            auto v19 = buf_9fda089d0982e762.getWindow(i, iter);
            auto v20 = Mul(v18, v18);
            v21.step(v20, iter);
            auto v22 = Sub(v19, v13);
            auto v23 = Mul(v22, v18);
            v24.step(v23, iter);
            auto v25 = Mul(v22, v22);
            v26.step(v25, iter);
        }
        auto v27 = Sqrt(v26);
        auto v28 = Sqrt(v21);
        auto v29 = Mul(v27, v28);
        auto v30 = Div(v24, v29);
        buf_1494da145191886fa.store(i, v30);
        auto v31 = v30;
        auto v32 = buf_85cedbf1ff9283ca.step(i);
        auto v33 = Sub(0.f, v32);
        buf_alpha013.store(i, v33);
        auto v34 = v33;
        auto v35 = buf_dfe1845a1a8fb41d.step(i);
        auto v36 = buf_164447fd516042841.step(i);
        auto v37 = Div(v35, v36);
        auto v38 = SetInfOrNanToValue(v37, 0.0f);
        buf_1c983e545d91c9866.store(i, v38);
        auto v39 = v38;
        auto v40 = buf_10bf852bbded84010.step(i);
        auto v41 = buf_140f4347a2e0bf204.step(i);
        auto v42 = Div(v40, v41);
        auto v43 = SetInfOrNanToValue(v42, 0.0f);
        buf_0785974bf2709530.store(i, v43);
        auto v44 = v43;
        auto v45 = buf_cfbdcaaf4ede80b1.step(i);
        auto v46 = buf_1406245cbf0a5c46f.step(i);
        auto v47 = Div(v45, v46);
        temp_48.store(i, v47);
        auto v48 = v47;
        ReduceAdd<float, 8> v51{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v50 = temp_48.getWindow(i, iter);
            v51.step(v50, iter);
        }
        buf_aaf0b20fc619bbb2.store(i, v51);
        auto v52 = v51;
        auto v53 = buf_137906673daae4438.step(i);
        auto v54 = buf_17832f3ff2a94f427.step(i);
        auto v55 = Div(v53, v54);
        auto v56 = SetInfOrNanToValue(v55, 0.0f);
        auto v57 = Sub(0.f, v56);
        buf_alpha044.store(i, v57);
        auto v58 = v57;
        auto v59 = buf_1d43b462e5d69ba5d.step(i);
        auto v60 = buf_2b4deadd228e75a9.step(i);
        auto v61 = Div(v59, v60);
        auto v62 = SetInfOrNanToValue(v61, 0.0f);
        temp_63.store(i, v62);
        auto v63 = v62;
        ReduceDecayLinear<float, 8, 4> v66{};
        for(int iter = 3;iter >= 0;iter--) {
            auto v65 = temp_63.getWindow(i, iter);
            v66.step(v65, iter);
        }
        temp_67.store(i, v66);
        auto v67 = v66;
        ReduceRank<float, 8> v70{v66};
        for(int iter = 7;iter >= 0;iter--) {
            auto v69 = temp_67.getWindow(i, iter);
            v70.step(v69, iter);
        }
        auto v71 = buf_64394211e6651f69.step(i);
        auto v72 = Max(v70, v71);
        auto v73 = Sub(0.f, v72);
        buf_alpha096.store(i, v73);
        auto v74 = v73;
    }
}







static void stage_alpha_101_stream__alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_18c68193caf707703{__ctx->buffers[428].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_78e4f6e7cee909a8{__ctx->buffers[274].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1eb68af50dcd04d42{__ctx->buffers[422].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> buf_1722e93023ecc26d8{__ctx->buffers[424].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_13f9e1b099bf55621{__ctx->buffers[417].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_95b7f4bc0dee6ab3{__ctx->buffers[418].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d08e7dc84e257dab{__ctx->buffers[367].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_593d8be97d887c4e{__ctx->buffers[426].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_11c9b76e737d91c1b{__ctx->buffers[363].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_3af437316f85598d{__ctx->buffers[382].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_7e2c3205f536c762{__ctx->buffers[289].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e083a5768021835f{__ctx->buffers[290].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_168fddb21a03fc027{__ctx->buffers[291].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15c36dacf75e527a0{__ctx->buffers[292].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_4f68c121edd4b69a{__ctx->buffers[181].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_149416df8d2c4dac{__ctx->buffers[281].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 200> buf_close{__ctx->buffers[2].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha074{__ctx->buffers[71].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha027{__ctx->buffers[32].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha015{__ctx->buffers[20].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_577d72745758f4ff{__ctx->buffers[432].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha078{__ctx->buffers[74].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha072{__ctx->buffers[69].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_3c0324e6b050f678{__ctx->buffers[434].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c1e02ad578fee8b1{__ctx->buffers[436].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_9df22aa19187c2f6{__ctx->buffers[438].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> temp_24{__ctx->buffers[439].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_29{__ctx->buffers[440].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_33{__ctx->buffers[441].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 6> temp_57{__ctx->buffers[442].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = constVec<8>(0.5f);
        auto v1 = buf_18c68193caf707703.step(i);
        auto v2 = GreaterThan(v1, v0);
        auto v3 = constVec<8>(-1.f);
        auto v4 = constVec<8>(1.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = buf_78e4f6e7cee909a8.step(i);
        auto v7 = buf_1eb68af50dcd04d42.step(i);
        auto v8 = LessThan(v6, v7);
        auto v9 = constVec<8>(0.f);
        auto v10 = Select(v8, v4, v9);
        auto v11 = Sub(0.f, v10);
        buf_alpha074.store(i, v11);
        auto v12 = v11;
        buf_alpha027.store(i, v5);
        auto v13 = v5;
        auto v14 = buf_1722e93023ecc26d8.step(i);
        ReduceAdd<float, 8> v17{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v16 = buf_1722e93023ecc26d8.getWindow(i, iter);
            v17.step(v16, iter);
        }
        auto v18 = Sub(0.f, v17);
        buf_alpha015.store(i, v18);
        auto v19 = v18;
        auto v20 = buf_13f9e1b099bf55621.step(i);
        auto v21 = buf_95b7f4bc0dee6ab3.step(i);
        auto v22 = Div(v20, v21);
        auto v23 = SetInfOrNanToValue(v22, 0.0f);
        temp_24.store(i, v23);
        auto v24 = v23;
        ReduceArgMin<float, 8> v27{};
        for(int iter = 8;iter >= 0;iter--) {
            auto v26 = temp_24.getWindow(i, iter);
            v27.step(v26, iter);
        }
        auto v28 = Sub(9.f, v27);
        temp_29.store(i, v28);
        auto v29 = v28;
        ReduceRank<float, 8> v32{v28};
        for(int iter = 6;iter >= 0;iter--) {
            auto v31 = temp_29.getWindow(i, iter);
            v32.step(v31, iter);
        }
        temp_33.store(i, v32);
        auto v33 = v32;
        ReduceDecayLinear<float, 8, 8> v36{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v35 = temp_33.getWindow(i, iter);
            v36.step(v35, iter);
        }
        buf_577d72745758f4ff.store(i, v36);
        auto v37 = v36;
        auto v38 = buf_1d08e7dc84e257dab.step(i);
        auto v39 = LogFast(v38);
        auto v40 = buf_593d8be97d887c4e.step(i);
        auto v41 = Mul(v40, v39);
        auto v42 = Exp(v41);
        buf_alpha078.store(i, v42);
        auto v43 = v42;
        auto v44 = buf_11c9b76e737d91c1b.step(i);
        auto v45 = Add(v44, 0.0001f);
        auto v46 = buf_3af437316f85598d.step(i);
        auto v47 = Div(v45, v46);
        buf_alpha072.store(i, v47);
        auto v48 = v47;
        auto v49 = buf_7e2c3205f536c762.step(i);
        auto v50 = buf_e083a5768021835f.step(i);
        auto v51 = Div(v49, v50);
        buf_3c0324e6b050f678.store(i, v51);
        auto v52 = v51;
        auto v53 = buf_168fddb21a03fc027.step(i);
        auto v54 = buf_15c36dacf75e527a0.step(i);
        auto v55 = Div(v53, v54);
        auto v56 = SetInfOrNanToValue(v55, 1.f);
        temp_57.store(i, v56);
        auto v57 = v56;
        ReduceDecayLinear<float, 8, 6> v60{};
        for(int iter = 5;iter >= 0;iter--) {
            auto v59 = temp_57.getWindow(i, iter);
            v60.step(v59, iter);
        }
        buf_1c1e02ad578fee8b1.store(i, v60);
        auto v61 = v60;
        auto v62 = buf_4f68c121edd4b69a.step(i);
        auto v63 = buf_149416df8d2c4dac.step(i);
        auto v64 = Add(v62, v63);
        auto v65 = buf_close.step(i);
        auto v66 = Sub(v64, v65);
        buf_9df22aa19187c2f6.store(i, v66);
        auto v67 = v66;
    }
}









static void stage_alpha_101_stream__alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1502ee23db6b90d7c{__ctx->buffers[435].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d45ec672ff6c56cb{__ctx->buffers[331].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e07e2753279851fe{__ctx->buffers[371].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c760288c6fcebd4{__ctx->buffers[437].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_7d709a995a29d77f{__ctx->buffers[248].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19e3239463a2eeb42{__ctx->buffers[433].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> buf_149416df8d2c4dac{__ctx->buffers[281].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f724efa04733d837{__ctx->buffers[293].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16ba41787258d0ac2{__ctx->buffers[294].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> buf_120d49c2693eb0b8f{__ctx->buffers[90].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_60b7aa4774c90f7a{__ctx->buffers[98].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 21> buf_1f0f01f1c83584d6f{__ctx->buffers[88].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 12> buf_19cc09703572f7b58{__ctx->buffers[89].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1555fc2ac5b1f47bd{__ctx->buffers[278].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_711b2797a20851f9{__ctx->buffers[295].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1e7f2de38b65af18f{__ctx->buffers[272].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_96cdc5fc00ecf5ae{__ctx->buffers[350].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12d3f9faf4df7c584{__ctx->buffers[270].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_148e235db294e7525{__ctx->buffers[244].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 11> buf_17237c9420bba7634{__ctx->buffers[97].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_34585a48fb808f83{__ctx->buffers[255].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha099{__ctx->buffers[86].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha077{__ctx->buffers[73].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha098{__ctx->buffers[85].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1082a9d10e478141d{__ctx->buffers[443].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1e5e0d408d12d9522{__ctx->buffers[445].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha071{__ctx->buffers[68].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha085{__ctx->buffers[78].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_30778bd3fe6f9ee4{__ctx->buffers[447].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha083{__ctx->buffers[76].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dcfa7dc0411b83e2{__ctx->buffers[449].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_21{__ctx->buffers[450].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_29{__ctx->buffers[451].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> temp_33{__ctx->buffers[452].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_44{__ctx->buffers[453].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 16> temp_51{__ctx->buffers[454].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 4> temp_55{__ctx->buffers[455].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1502ee23db6b90d7c.step(i);
        auto v1 = buf_d45ec672ff6c56cb.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = Sub(0.f, v5);
        buf_alpha099.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_e07e2753279851fe.step(i);
        auto v9 = buf_1c760288c6fcebd4.step(i);
        auto v10 = Min(v8, v9);
        buf_alpha077.store(i, v10);
        auto v11 = v10;
        auto v12 = buf_7d709a995a29d77f.step(i);
        auto v13 = buf_19e3239463a2eeb42.step(i);
        auto v14 = Sub(v12, v13);
        buf_alpha098.store(i, v14);
        auto v15 = v14;
        auto v16 = buf_149416df8d2c4dac.step(i);
        auto v17 = Mul(v16, 0.178404f);
        auto v18 = buf_e0e3079c4a901fa8.step(i);
        auto v19 = Mul(v18, 0.821596f);
        auto v20 = Add(v17, v19);
        temp_21.store(i, v20);
        auto v21 = v20;
        auto v22 = windowedRef<float, 8, 4>(temp_21, i);
        auto v23 = Sub(v20, v22);
        buf_1082a9d10e478141d.store(i, v23);
        auto v24 = v23;
        auto v25 = buf_1f724efa04733d837.step(i);
        auto v26 = buf_16ba41787258d0ac2.step(i);
        auto v27 = Div(v25, v26);
        auto v28 = SetInfOrNanToValue(v27, 0.0f);
        temp_29.store(i, v28);
        auto v29 = v28;
        ReduceDecayLinear<float, 8, 7> v32{};
        for(int iter = 6;iter >= 0;iter--) {
            auto v31 = temp_29.getWindow(i, iter);
            v32.step(v31, iter);
        }
        temp_33.store(i, v32);
        auto v33 = v32;
        ReduceRank<float, 8> v36{v32};
        for(int iter = 2;iter >= 0;iter--) {
            auto v35 = temp_33.getWindow(i, iter);
            v36.step(v35, iter);
        }
        auto v37 = buf_120d49c2693eb0b8f.step(i);
        auto v38 = buf_60b7aa4774c90f7a.step(i);
        auto v39 = Add(v37, v38);
        auto v40 = buf_1f0f01f1c83584d6f.step(i);
        auto v41 = buf_19cc09703572f7b58.step(i);
        auto v42 = Add(v40, v41);
        auto v43 = Sub(v42, v39);
        temp_44.store(i, v43);
        auto v44 = v43;
        ReduceDecayLinear<float, 8, 8> v47{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v46 = temp_44.getWindow(i, iter);
            v47.step(v46, iter);
        }
        buf_1e5e0d408d12d9522.store(i, v47);
        auto v48 = v47;
        auto v49 = buf_1555fc2ac5b1f47bd.step(i);
        auto v50 = Mul(v49, v49);
        temp_51.store(i, v50);
        auto v51 = v50;
        ReduceDecayLinear<float, 8, 16> v54{};
        for(int iter = 15;iter >= 0;iter--) {
            auto v53 = temp_51.getWindow(i, iter);
            v54.step(v53, iter);
        }
        temp_55.store(i, v54);
        auto v55 = v54;
        ReduceRank<float, 8> v58{v54};
        for(int iter = 3;iter >= 0;iter--) {
            auto v57 = temp_55.getWindow(i, iter);
            v58.step(v57, iter);
        }
        auto v59 = buf_711b2797a20851f9.step(i);
        auto v60 = Max(v59, v58);
        buf_alpha071.store(i, v60);
        auto v61 = v60;
        auto v62 = buf_1e7f2de38b65af18f.step(i);
        auto v63 = LogFast(v62);
        auto v64 = buf_96cdc5fc00ecf5ae.step(i);
        auto v65 = Mul(v64, v63);
        auto v66 = Exp(v65);
        buf_alpha085.store(i, v66);
        auto v67 = v66;
        auto v68 = buf_12d3f9faf4df7c584.step(i);
        auto v69 = Sub(0.f, v68);
        buf_30778bd3fe6f9ee4.store(i, v69);
        auto v70 = v69;
        auto v71 = buf_148e235db294e7525.step(i);
        auto v72 = buf_17237c9420bba7634.step(i);
        auto v73 = Mul(v71, v72);
        auto v74 = buf_34585a48fb808f83.step(i);
        auto v75 = Div(v73, v74);
        buf_alpha083.store(i, v75);
        auto v76 = v75;
        buf_dcfa7dc0411b83e2.store(i, v36);
        auto v77 = v36;
    }
}



static void stage_alpha_101_stream__alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_15b2fff9c11057958{__ctx->buffers[327].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_180ac7118f5030ca1{__ctx->buffers[444].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> buf_132137a7499bdd6c5{__ctx->buffers[448].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_dcfa7dc0411b83e2{__ctx->buffers[449].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c13f2073feaded25{__ctx->buffers[446].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_93385477fefd8c17{__ctx->buffers[242].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17230fa8d70cb536c{__ctx->buffers[268].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ab239b5b61f82d07{__ctx->buffers[256].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_400531d71d423d49{__ctx->buffers[239].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14ce42d338ddcd7bc{__ctx->buffers[227].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19448648e73a0e59a{__ctx->buffers[234].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_b80797652a77217e{__ctx->buffers[225].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_869f38882be45286{__ctx->buffers[223].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1500a66d04da7a0f4{__ctx->buffers[329].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 2> buf_8f66f09e9d504a8c{__ctx->buffers[208].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e5741f7564c392ac{__ctx->buffers[209].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17186aeb7dd951aaf{__ctx->buffers[204].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_9dd1ca3b1e0306d5{__ctx->buffers[217].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha064{__ctx->buffers[64].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1856762a1003c5303{__ctx->buffers[456].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha088{__ctx->buffers[80].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha060{__ctx->buffers[61].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha020{__ctx->buffers[25].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha005{__ctx->buffers[10].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha011{__ctx->buffers[16].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha057{__ctx->buffers[60].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha032{__ctx->buffers[37].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_15b2fff9c11057958.step(i);
        auto v1 = buf_180ac7118f5030ca1.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = Sub(0.f, v5);
        buf_alpha064.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_132137a7499bdd6c5.step(i);
        ReduceAdd<float, 8> v11{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v10 = buf_132137a7499bdd6c5.getWindow(i, iter);
            v11.step(v10, iter);
        }
        buf_1856762a1003c5303.store(i, v11);
        auto v12 = v11;
        auto v13 = buf_dcfa7dc0411b83e2.step(i);
        auto v14 = buf_c13f2073feaded25.step(i);
        auto v15 = Min(v13, v14);
        buf_alpha088.store(i, v15);
        auto v16 = v15;
        auto v17 = buf_93385477fefd8c17.step(i);
        auto v18 = Mul(2.f, v17);
        auto v19 = buf_17230fa8d70cb536c.step(i);
        auto v20 = Sub(v18, v19);
        auto v21 = Sub(0.f, v20);
        buf_alpha060.store(i, v21);
        auto v22 = v21;
        auto v23 = buf_ab239b5b61f82d07.step(i);
        auto v24 = buf_400531d71d423d49.step(i);
        auto v25 = Mul(v23, v24);
        auto v26 = Sub(0.f, v25);
        buf_alpha020.store(i, v26);
        auto v27 = v26;
        auto v28 = buf_14ce42d338ddcd7bc.step(i);
        auto v29 = buf_19448648e73a0e59a.step(i);
        auto v30 = Mul(v28, v29);
        buf_alpha005.store(i, v30);
        auto v31 = v30;
        auto v32 = buf_b80797652a77217e.step(i);
        auto v33 = buf_869f38882be45286.step(i);
        auto v34 = Add(v32, v33);
        auto v35 = buf_1500a66d04da7a0f4.step(i);
        auto v36 = Mul(v34, v35);
        buf_alpha011.store(i, v36);
        auto v37 = v36;
        auto v38 = buf_8f66f09e9d504a8c.step(i);
        ReduceDecayLinear<float, 8, 2> v41{};
        for(int iter = 1;iter >= 0;iter--) {
            auto v40 = buf_8f66f09e9d504a8c.getWindow(i, iter);
            v41.step(v40, iter);
        }
        auto v42 = buf_e5741f7564c392ac.step(i);
        auto v43 = Div(v42, v41);
        auto v44 = Sub(0.f, v43);
        buf_alpha057.store(i, v44);
        auto v45 = v44;
        auto v46 = buf_17186aeb7dd951aaf.step(i);
        auto v47 = buf_9dd1ca3b1e0306d5.step(i);
        auto v48 = Add(v46, v47);
        buf_alpha032.store(i, v48);
        auto v49 = v48;
    }
}













static void stage_alpha_101_stream__alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 5> buf_17852424e0c297f0e{__ctx->buffers[457].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 250> buf_82b65763b5b2c7e4{__ctx->buffers[105].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 22> buf_bf8995d8792205e7{__ctx->buffers[142].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_53d006c904f6f37f{__ctx->buffers[245].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_42fef27a0c3f65f3{__ctx->buffers[202].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_b8d3ad670e02e188{__ctx->buffers[200].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_18644381e8646210a{__ctx->buffers[182].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_12db32b8edf3abd9c{__ctx->buffers[183].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 50> buf_11883856b1555ef59{__ctx->buffers[111].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_114be7640278a0027{__ctx->buffers[184].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_a998c0027e40f4c6{__ctx->buffers[185].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_662df1dc40577e1e{__ctx->buffers[186].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_90a31d273592c40c{__ctx->buffers[187].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e8ef8c4a12962781{__ctx->buffers[188].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha029{__ctx->buffers[34].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_bc280680bb3448f5{__ctx->buffers[458].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a409bd1b3fb41908{__ctx->buffers[460].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha031{__ctx->buffers[36].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e5cadef0dfe4e951{__ctx->buffers[461].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1e2fe07181b3a06db{__ctx->buffers[463].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f1a2321d133ec98c{__ctx->buffers[465].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 3> buf_3cef9bf07276a7bd{__ctx->buffers[467].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ac3647e8d5b400bf{__ctx->buffers[469].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 7> temp_6{__ctx->buffers[470].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_8{__ctx->buffers[471].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_34{__ctx->buffers[472].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_17852424e0c297f0e.step(i);
        ReduceMin<float, 8> v3{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v2 = buf_17852424e0c297f0e.getWindow(i, iter);
            v3.step(v2, iter);
        }
        auto v4 = buf_82b65763b5b2c7e4.step(i);
        auto v5 = Sub(0.f, v4);
        temp_6.store(i, v5);
        auto v6 = v5;
        auto v7 = windowedRef<float, 8, 6>(temp_6, i);
        temp_8.store(i, v7);
        auto v8 = v7;
        ReduceRank<float, 8> v11{v7};
        for(int iter = 4;iter >= 0;iter--) {
            auto v10 = temp_8.getWindow(i, iter);
            v11.step(v10, iter);
        }
        auto v12 = Add(v3, v11);
        buf_alpha029.store(i, v12);
        auto v13 = v12;
        buf_bc280680bb3448f5.store(i, v11);
        auto v14 = v11;
        auto v15 = buf_bf8995d8792205e7.step(i);
        auto v16 = Mul(v5, v15);
        auto v17 = buf_e0e3079c4a901fa8.step(i);
        auto v18 = Mul(v16, v17);
        auto v19 = buf_53d006c904f6f37f.step(i);
        auto v20 = Mul(v18, v19);
        buf_1a409bd1b3fb41908.store(i, v20);
        auto v21 = v20;
        auto v22 = buf_42fef27a0c3f65f3.step(i);
        auto v23 = buf_b8d3ad670e02e188.step(i);
        auto v24 = Add(v22, v23);
        auto v25 = buf_18644381e8646210a.step(i);
        auto v26 = Add(v24, v25);
        buf_alpha031.store(i, v26);
        auto v27 = v26;
        auto v28 = buf_12db32b8edf3abd9c.step(i);
        auto v29 = Div(v28, 8.f);
        auto v30 = buf_11883856b1555ef59.step(i);
        ReduceAdd<float, 8> v33{};
        for(int iter = 49;iter >= 0;iter--) {
            auto v32 = buf_11883856b1555ef59.getWindow(i, iter);
            v33.step(v32, iter);
        }
        temp_34.store(i, v33);
        auto v34 = v33;
        ReduceAdd<float, 8> v37{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v36 = temp_34.getWindow(i, iter);
            v37.step(v36, iter);
        }
        auto v38 = Div(v37, 8.f);
        ReduceAdd<float, 8> v44{};
        ReduceAdd<float, 8> v47{};
        ReduceAdd<float, 8> v49{};
        for(int iter = 7;iter >= 0;iter--) {
            auto v40 = buf_e0e3079c4a901fa8.getWindow(i, iter);
            auto v41 = Sub(v40, v29);
            auto v42 = temp_34.getWindow(i, iter);
            auto v43 = Mul(v41, v41);
            v44.step(v43, iter);
            auto v45 = Sub(v42, v38);
            auto v46 = Mul(v41, v45);
            v47.step(v46, iter);
            auto v48 = Mul(v45, v45);
            v49.step(v48, iter);
        }
        auto v50 = Sqrt(v49);
        auto v51 = Sqrt(v44);
        auto v52 = Mul(v51, v50);
        auto v53 = Div(v47, v52);
        buf_e5cadef0dfe4e951.store(i, v53);
        auto v54 = v53;
        auto v55 = buf_114be7640278a0027.step(i);
        auto v56 = buf_a998c0027e40f4c6.step(i);
        auto v57 = Div(v55, v56);
        buf_1e2fe07181b3a06db.store(i, v57);
        auto v58 = v57;
        auto v59 = buf_662df1dc40577e1e.step(i);
        auto v60 = buf_90a31d273592c40c.step(i);
        auto v61 = Div(v59, v60);
        buf_1f1a2321d133ec98c.store(i, v61);
        auto v62 = v61;
        auto v63 = buf_e8ef8c4a12962781.step(i);
        auto v64 = Sub(v17, v63);
        buf_3cef9bf07276a7bd.store(i, v64);
        auto v65 = v64;
        ReduceDecayLinear<float, 8, 3> v68{};
        for(int iter = 2;iter >= 0;iter--) {
            auto v67 = buf_3cef9bf07276a7bd.getWindow(i, iter);
            v68.step(v67, iter);
        }
        buf_ac3647e8d5b400bf.store(i, v68);
        auto v69 = v68;
    }
}









static void stage_alpha_101_stream__alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_13197e06412cf906c{__ctx->buffers[464].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1e5ad191dd30293be{__ctx->buffers[365].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_0c3587e4cc9fb312{__ctx->buffers[473].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_123e2c4ec6491301c{__ctx->buffers[337].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d0d5bd22659e1cc3{__ctx->buffers[296].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_9361c0593542cca8{__ctx->buffers[468].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_d7e8c3e6f7f6a559{__ctx->buffers[462].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1134198af3a9d5546{__ctx->buffers[459].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1bec543c83b13b08c{__ctx->buffers[158].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_75e672355a4f049c{__ctx->buffers[189].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_835a27cc9bdeb830{__ctx->buffers[162].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1ac920ea885d1ed2b{__ctx->buffers[190].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16599f4652129dcaa{__ctx->buffers[191].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_6722cd923f64335c{__ctx->buffers[192].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 230> buf_e0e3079c4a901fa8{__ctx->buffers[159].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_196f4b404382c7357{__ctx->buffers[193].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1eda8e378b6c06d1a{__ctx->buffers[112].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_0f32950657c256a9{__ctx->buffers[136].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_54a7c757d1b2deb5{__ctx->buffers[134].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha075{__ctx->buffers[72].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha073{__ctx->buffers[70].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha047{__ctx->buffers[52].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ad098f0979977549{__ctx->buffers[474].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha036{__ctx->buffers[41].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_4ee2dea1b7e3190e{__ctx->buffers[476].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_11eb8919f2723bf62{__ctx->buffers[478].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha041{__ctx->buffers[46].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha037{__ctx->buffers[42].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_13197e06412cf906c.step(i);
        auto v1 = buf_1e5ad191dd30293be.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        buf_alpha075.store(i, v5);
        auto v6 = v5;
        auto v7 = buf_0c3587e4cc9fb312.step(i);
        auto v8 = buf_123e2c4ec6491301c.step(i);
        auto v9 = Max(v7, v8);
        auto v10 = Sub(0.f, v9);
        buf_alpha073.store(i, v10);
        auto v11 = v10;
        auto v12 = buf_1d0d5bd22659e1cc3.step(i);
        auto v13 = buf_9361c0593542cca8.step(i);
        auto v14 = Sub(v12, v13);
        auto v15 = SetInfOrNanToValue(v14, 0.0f);
        buf_alpha047.store(i, v15);
        auto v16 = v15;
        auto v17 = buf_d7e8c3e6f7f6a559.step(i);
        ReduceMul<float, 8> v20{};
        for(int iter = 14;iter >= 0;iter--) {
            auto v19 = buf_d7e8c3e6f7f6a559.getWindow(i, iter);
            v20.step(v19, iter);
        }
        buf_ad098f0979977549.store(i, v20);
        auto v21 = v20;
        auto v22 = buf_1134198af3a9d5546.step(i);
        auto v23 = Mul(0.73f, v22);
        auto v24 = buf_1bec543c83b13b08c.step(i);
        auto v25 = Mul(2.21f, v24);
        auto v26 = buf_75e672355a4f049c.step(i);
        auto v27 = Add(v25, v26);
        auto v28 = Add(v27, v23);
        auto v29 = buf_835a27cc9bdeb830.step(i);
        auto v30 = Add(v28, v29);
        auto v31 = buf_1ac920ea885d1ed2b.step(i);
        auto v32 = Add(v30, v31);
        buf_alpha036.store(i, v32);
        auto v33 = v32;
        auto v34 = buf_16599f4652129dcaa.step(i);
        auto v35 = buf_6722cd923f64335c.step(i);
        auto v36 = Div(v34, v35);
        buf_4ee2dea1b7e3190e.store(i, v36);
        auto v37 = v36;
        auto v38 = buf_e0e3079c4a901fa8.step(i);
        auto v39 = buf_196f4b404382c7357.step(i);
        auto v40 = Sub(v38, v39);
        buf_11eb8919f2723bf62.store(i, v40);
        auto v41 = v40;
        auto v42 = buf_1eda8e378b6c06d1a.step(i);
        auto v43 = Sub(v42, v38);
        buf_alpha041.store(i, v43);
        auto v44 = v43;
        auto v45 = buf_0f32950657c256a9.step(i);
        auto v46 = buf_54a7c757d1b2deb5.step(i);
        auto v47 = Add(v45, v46);
        buf_alpha037.store(i, v47);
        auto v48 = v47;
    }
}









static void stage_alpha_101_stream__alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_6df3b3f2c4401862{__ctx->buffers[479].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_c223205aa1dfceba{__ctx->buffers[466].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_11edbf4250d427421{__ctx->buffers[475].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1cbce9dc8fd160ab0{__ctx->buffers[401].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2b4119829ffbe8ca{__ctx->buffers[145].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 15> buf_a16e007555996c6e{__ctx->buffers[138].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_14a4506c73ffa04a3{__ctx->buffers[113].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1a81d3f6084e29607{__ctx->buffers[114].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_11b9e2ab19c5cf07f{__ctx->buffers[132].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_68d8962d72605382{__ctx->buffers[146].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> buf_9d962035ba5f1ea4{__ctx->buffers[129].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1da3294008ba11157{__ctx->buffers[147].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1b968497d4dea7e45{__ctx->buffers[148].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1821f4158341eaadd{__ctx->buffers[149].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_50636661b8037409{__ctx->buffers[166].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_909092e48dafead6{__ctx->buffers[212].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_ade4363da9807ef1{__ctx->buffers[126].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1dcccc562c26e18cc{__ctx->buffers[150].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> buf_78d7937a0020cd3f{__ctx->buffers[137].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha081{__ctx->buffers[75].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha061{__ctx->buffers[62].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1f6a94fd48767fcd5{__ctx->buffers[480].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha030{__ctx->buffers[35].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_48714155402cad1d{__ctx->buffers[482].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha045{__ctx->buffers[50].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_0723b75820a79324{__ctx->buffers[484].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_009da94a34e00581{__ctx->buffers[486].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha043{__ctx->buffers[48].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 20> temp_48{__ctx->buffers[488].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 8> temp_57{__ctx->buffers[489].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_6df3b3f2c4401862.step(i);
        auto v1 = buf_c223205aa1dfceba.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = buf_11edbf4250d427421.step(i);
        auto v7 = buf_1cbce9dc8fd160ab0.step(i);
        auto v8 = LessThan(v6, v7);
        auto v9 = Select(v8, v3, v4);
        auto v10 = Sub(0.f, v9);
        buf_alpha081.store(i, v10);
        auto v11 = v10;
        buf_alpha061.store(i, v5);
        auto v12 = v5;
        auto v13 = buf_2b4119829ffbe8ca.step(i);
        auto v14 = buf_a16e007555996c6e.step(i);
        auto v15 = Add(v13, v14);
        auto v16 = buf_14a4506c73ffa04a3.step(i);
        auto v17 = buf_1a81d3f6084e29607.step(i);
        auto v18 = Div(v16, v17);
        auto v19 = SetInfOrNanToValue(v18, 0.0f);
        auto v20 = Add(v15, v19);
        buf_1f6a94fd48767fcd5.store(i, v20);
        auto v21 = v20;
        auto v22 = buf_11b9e2ab19c5cf07f.step(i);
        auto v23 = Sub(1.0f, v22);
        auto v24 = buf_68d8962d72605382.step(i);
        auto v25 = Mul(v23, v24);
        buf_alpha030.store(i, v25);
        auto v26 = v25;
        auto v27 = buf_9d962035ba5f1ea4.step(i);
        auto v28 = buf_1da3294008ba11157.step(i);
        auto v29 = Sub(v27, v28);
        buf_48714155402cad1d.store(i, v29);
        auto v30 = v29;
        auto v31 = buf_1b968497d4dea7e45.step(i);
        auto v32 = buf_1821f4158341eaadd.step(i);
        auto v33 = Div(v31, v32);
        auto v34 = SetInfOrNanToValue(v33, 0.0f);
        auto v35 = buf_50636661b8037409.step(i);
        auto v36 = Mul(v35, v34);
        auto v37 = buf_909092e48dafead6.step(i);
        auto v38 = Mul(v36, v37);
        auto v39 = Sub(0.f, v38);
        buf_alpha045.store(i, v39);
        auto v40 = v39;
        auto v41 = buf_ade4363da9807ef1.step(i);
        auto v42 = Sub(1.f, v41);
        auto v43 = buf_1dcccc562c26e18cc.step(i);
        auto v44 = Mul(v43, v42);
        buf_0723b75820a79324.store(i, v44);
        auto v45 = v44;
        auto v46 = buf_78d7937a0020cd3f.step(i);
        auto v47 = SetInfOrNanToValue(v46, 0.0f);
        temp_48.store(i, v47);
        auto v48 = v47;
        ReduceRank<float, 8> v51{v47};
        for(int iter = 19;iter >= 0;iter--) {
            auto v50 = temp_48.getWindow(i, iter);
            v51.step(v50, iter);
        }
        ReduceRank<float, 8> v54{v47};
        for(int iter = 4;iter >= 0;iter--) {
            auto v53 = temp_48.getWindow(i, iter);
            v54.step(v53, iter);
        }
        buf_009da94a34e00581.store(i, v54);
        auto v55 = v54;
        auto v56 = Sub(0.f, v43);
        temp_57.store(i, v56);
        auto v57 = v56;
        ReduceRank<float, 8> v60{v56};
        for(int iter = 7;iter >= 0;iter--) {
            auto v59 = temp_57.getWindow(i, iter);
            v60.step(v59, iter);
        }
        auto v61 = Mul(v51, v60);
        buf_alpha043.store(i, v61);
        auto v62 = v61;
    }
}





static void stage_alpha_101_stream__alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_16c47e4b623012393{__ctx->buffers[485].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_e2149fed4461a684{__ctx->buffers[151].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_6f87169c15697aec{__ctx->buffers[263].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d85621e17bd8a9d6{__ctx->buffers[483].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1fecb90f5ea6b7a42{__ctx->buffers[487].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_d1aea8e58de31174{__ctx->buffers[481].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1cac888714ed9f6a4{__ctx->buffers[104].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1c1ae2fca6456a911{__ctx->buffers[130].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_39cd69e7f6d85328{__ctx->buffers[102].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_16123614810853bf0{__ctx->buffers[100].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_93dc794f9443fc2e{__ctx->buffers[115].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_19b7389e0c453d803{__ctx->buffers[116].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_13990b65aba0b7628{__ctx->buffers[297].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_2c53c8eaac8635f5{__ctx->buffers[117].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_15d5de9a420044862{__ctx->buffers[118].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_00adb6936a973911{__ctx->buffers[119].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_46fde329d2fadda4{__ctx->buffers[235].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha039{__ctx->buffers[44].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha017{__ctx->buffers[22].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha018{__ctx->buffers[23].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_9b92ce11ac49056c{__ctx->buffers[490].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha008{__ctx->buffers[13].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha014{__ctx->buffers[19].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha035{__ctx->buffers[40].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1295929018b7108e8{__ctx->buffers[491].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha054{__ctx->buffers[58].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 5> temp_35{__ctx->buffers[492].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_16c47e4b623012393.step(i);
        auto v1 = Sub(0.f, v0);
        auto v2 = buf_e2149fed4461a684.step(i);
        auto v3 = Mul(v1, v2);
        buf_alpha039.store(i, v3);
        auto v4 = v3;
        auto v5 = buf_6f87169c15697aec.step(i);
        auto v6 = buf_1d85621e17bd8a9d6.step(i);
        auto v7 = Mul(v5, v6);
        auto v8 = buf_1fecb90f5ea6b7a42.step(i);
        auto v9 = Mul(v7, v8);
        auto v10 = Sub(0.f, v9);
        buf_alpha017.store(i, v10);
        auto v11 = v10;
        auto v12 = buf_d1aea8e58de31174.step(i);
        auto v13 = Sub(0.f, v12);
        buf_alpha018.store(i, v13);
        auto v14 = v13;
        auto v15 = buf_1cac888714ed9f6a4.step(i);
        auto v16 = Sub(2.f, v15);
        auto v17 = buf_1c1ae2fca6456a911.step(i);
        auto v18 = Sub(v16, v17);
        buf_9b92ce11ac49056c.store(i, v18);
        auto v19 = v18;
        auto v20 = buf_39cd69e7f6d85328.step(i);
        auto v21 = Sub(0.f, v20);
        buf_alpha008.store(i, v21);
        auto v22 = v21;
        auto v23 = buf_16123614810853bf0.step(i);
        auto v24 = buf_93dc794f9443fc2e.step(i);
        auto v25 = Mul(v23, v24);
        auto v26 = Sub(0.f, v25);
        buf_alpha014.store(i, v26);
        auto v27 = v26;
        auto v28 = buf_19b7389e0c453d803.step(i);
        auto v29 = Sub(1.f, v28);
        auto v30 = buf_13990b65aba0b7628.step(i);
        auto v31 = Mul(v30, v29);
        buf_alpha035.store(i, v31);
        auto v32 = v31;
        auto v33 = buf_2c53c8eaac8635f5.step(i);
        auto v34 = Mul(v33, v33);
        temp_35.store(i, v34);
        auto v35 = v34;
        ReduceArgMax<float, 8> v38{};
        for(int iter = 4;iter >= 0;iter--) {
            auto v37 = temp_35.getWindow(i, iter);
            v38.step(v37, iter);
        }
        auto v39 = Sub(5.f, v38);
        buf_1295929018b7108e8.store(i, v39);
        auto v40 = v39;
        auto v41 = buf_15d5de9a420044862.step(i);
        auto v42 = buf_00adb6936a973911.step(i);
        auto v43 = Mul(v41, v42);
        auto v44 = buf_46fde329d2fadda4.step(i);
        auto v45 = Div(v44, v43);
        buf_alpha054.store(i, v45);
        auto v46 = v45;
    }
}



static void stage_alpha_101_stream__alpha040_1668c43c265a85276(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_19f751d58c7a6b64{__ctx->buffers[120].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1d97c2770b5a6165f{__ctx->buffers[121].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1cb80bd72b1338485{__ctx->buffers[96].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_17c512b9c71d3d940{__ctx->buffers[92].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 9> buf_120d49c2693eb0b8f{__ctx->buffers[90].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1ba3ef6f8151bb423{__ctx->buffers[122].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha040{__ctx->buffers[45].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_1668c43c265a85276{__ctx->buffers[493].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_19f751d58c7a6b64.step(i);
        auto v1 = buf_1d97c2770b5a6165f.step(i);
        auto v2 = Div(v0, v1);
        auto v3 = SetInfOrNanToValue(v2, 1.0f);
        auto v4 = buf_1cb80bd72b1338485.step(i);
        auto v5 = Sub(0.f, v4);
        auto v6 = Mul(v5, v3);
        buf_alpha040.store(i, v6);
        auto v7 = v6;
        auto v8 = buf_17c512b9c71d3d940.step(i);
        auto v9 = buf_120d49c2693eb0b8f.step(i);
        auto v10 = Add(v8, v9);
        auto v11 = buf_1ba3ef6f8151bb423.step(i);
        auto v12 = LessThan(v11, v10);
        auto v13 = constVec<8>(1.f);
        auto v14 = constVec<8>(0.f);
        auto v15 = Select(v12, v13, v14);
        buf_1668c43c265a85276.store(i, v15);
        auto v16 = v15;
    }
}

static void stage_alpha_101_stream__alpha062(Context* __ctx, size_t __stock_idx, size_t __total_time, size_t __start, size_t __length) {
    StreamWindow<float, 8, 1> buf_1ccb8076ab4602658{__ctx->buffers[477].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_104b7bca3b25ad8b3{__ctx->buffers[494].stream_buf, __stock_idx, __ctx->stock_count};
    StreamWindow<float, 8, 1> buf_alpha062{__ctx->buffers[63].stream_buf, __stock_idx, __ctx->stock_count};
    for(size_t i = 0;i < __length;i++) {
        auto v0 = buf_1ccb8076ab4602658.step(i);
        auto v1 = buf_104b7bca3b25ad8b3.step(i);
        auto v2 = LessThan(v0, v1);
        auto v3 = constVec<8>(1.f);
        auto v4 = constVec<8>(0.f);
        auto v5 = Select(v2, v3, v4);
        auto v6 = Sub(0.f, v5);
        buf_alpha062.store(i, v6);
        auto v7 = v6;
    }
}

static BufferInfo __buffers_alpha_101_stream[]{
    {0, "low", 8, BufferKind::INPUT, 0, 12},
    {1, "high", 6, BufferKind::INPUT, 0, 20},
    {2, "close", 10, BufferKind::INPUT, 0, 200},
    {3, "open", 8, BufferKind::INPUT, 0, 14},
    {4, "amount", 1, BufferKind::INPUT, 0, 1},
    {5, "volume", 9, BufferKind::INPUT, 0, 180},
    {6, "alpha001", 0, BufferKind::OUTPUT, 26, 1},
    {7, "alpha002", 0, BufferKind::OUTPUT, 8, 1},
    {8, "alpha003", 0, BufferKind::OUTPUT, 10, 1},
    {9, "alpha004", 0, BufferKind::OUTPUT, 9, 1},
    {10, "alpha005", 0, BufferKind::OUTPUT, 10, 1},
    {11, "alpha006", 0, BufferKind::OUTPUT, 10, 1},
    {12, "alpha007", 0, BufferKind::OUTPUT, 67, 1},
    {13, "alpha008", 0, BufferKind::OUTPUT, 16, 1},
    {14, "alpha009", 0, BufferKind::OUTPUT, 6, 1},
    {15, "alpha010", 0, BufferKind::OUTPUT, 5, 1},
    {16, "alpha011", 0, BufferKind::OUTPUT, 3, 1},
    {17, "alpha012", 0, BufferKind::OUTPUT, 1, 1},
    {18, "alpha013", 0, BufferKind::OUTPUT, 5, 1},
    {19, "alpha014", 0, BufferKind::OUTPUT, 10, 1},
    {20, "alpha015", 0, BufferKind::OUTPUT, 6, 1},
    {21, "alpha016", 0, BufferKind::OUTPUT, 5, 1},
    {22, "alpha017", 0, BufferKind::OUTPUT, 25, 1},
    {23, "alpha018", 0, BufferKind::OUTPUT, 10, 1},
    {24, "alpha019", 0, BufferKind::OUTPUT, 251, 1},
    {25, "alpha020", 0, BufferKind::OUTPUT, 1, 1},
    {26, "alpha021", 0, BufferKind::OUTPUT, 20, 1},
    {27, "alpha022", 0, BufferKind::OUTPUT, 20, 1},
    {28, "alpha023", 0, BufferKind::OUTPUT, 20, 1},
    {29, "alpha024", 0, BufferKind::OUTPUT, 200, 1},
    {30, "alpha025", 0, BufferKind::OUTPUT, 20, 1},
    {31, "alpha026", 0, BufferKind::OUTPUT, 13, 1},
    {32, "alpha027", 0, BufferKind::OUTPUT, 8, 1},
    {33, "alpha028", 0, BufferKind::OUTPUT, 25, 1},
    {34, "alpha029", 0, BufferKind::OUTPUT, 12, 1},
    {35, "alpha030", 0, BufferKind::OUTPUT, 20, 1},
    {36, "alpha031", 0, BufferKind::OUTPUT, 32, 1},
    {37, "alpha032", 0, BufferKind::OUTPUT, 235, 1},
    {38, "alpha033", 0, BufferKind::OUTPUT, 0, 1},
    {39, "alpha034", 0, BufferKind::OUTPUT, 6, 1},
    {40, "alpha035", 0, BufferKind::OUTPUT, 33, 1},
    {41, "alpha036", 0, BufferKind::OUTPUT, 200, 1},
    {42, "alpha037", 0, BufferKind::OUTPUT, 201, 1},
    {43, "alpha038", 0, BufferKind::OUTPUT, 10, 1},
    {44, "alpha039", 0, BufferKind::OUTPUT, 251, 1},
    {45, "alpha040", 0, BufferKind::OUTPUT, 10, 1},
    {46, "alpha041", 0, BufferKind::OUTPUT, 0, 1},
    {47, "alpha042", 0, BufferKind::OUTPUT, 0, 1},
    {48, "alpha043", 0, BufferKind::OUTPUT, 40, 1},
    {49, "alpha044", 0, BufferKind::OUTPUT, 5, 1},
    {50, "alpha045", 0, BufferKind::OUTPUT, 25, 1},
    {51, "alpha046", 0, BufferKind::OUTPUT, 20, 1},
    {52, "alpha047", 0, BufferKind::OUTPUT, 20, 1},
    {53, "alpha049", 0, BufferKind::OUTPUT, 20, 1},
    {54, "alpha050", 0, BufferKind::OUTPUT, 10, 1},
    {55, "alpha051", 0, BufferKind::OUTPUT, 20, 1},
    {56, "alpha052", 0, BufferKind::OUTPUT, 241, 1},
    {57, "alpha053", 0, BufferKind::OUTPUT, 9, 1},
    {58, "alpha054", 0, BufferKind::OUTPUT, 0, 1},
    {59, "alpha055", 0, BufferKind::OUTPUT, 18, 1},
    {60, "alpha057", 0, BufferKind::OUTPUT, 32, 1},
    {61, "alpha060", 0, BufferKind::OUTPUT, 10, 1},
    {62, "alpha061", 0, BufferKind::OUTPUT, 198, 1},
    {63, "alpha062", 0, BufferKind::OUTPUT, 52, 1},
    {64, "alpha064", 0, BufferKind::OUTPUT, 150, 1},
    {65, "alpha065", 0, BufferKind::OUTPUT, 75, 1},
    {66, "alpha066", 0, BufferKind::OUTPUT, 18, 1},
    {67, "alpha068", 0, BufferKind::OUTPUT, 38, 1},
    {68, "alpha071", 0, BufferKind::OUTPUT, 230, 1},
    {69, "alpha072", 0, BufferKind::OUTPUT, 59, 1},
    {70, "alpha073", 0, BufferKind::OUTPUT, 22, 1},
    {71, "alpha074", 0, BufferKind::OUTPUT, 82, 1},
    {72, "alpha075", 0, BufferKind::OUTPUT, 62, 1},
    {73, "alpha077", 0, BufferKind::OUTPUT, 49, 1},
    {74, "alpha078", 0, BufferKind::OUTPUT, 67, 1},
    {75, "alpha081", 0, BufferKind::OUTPUT, 83, 1},
    {76, "alpha083", 0, BufferKind::OUTPUT, 7, 1},
    {77, "alpha084", 0, BufferKind::OUTPUT, 36, 1},
    {78, "alpha085", 0, BufferKind::OUTPUT, 40, 1},
    {79, "alpha086", 0, BufferKind::OUTPUT, 61, 1},
    {80, "alpha088", 0, BufferKind::OUTPUT, 99, 1},
    {81, "alpha092", 0, BufferKind::OUTPUT, 52, 1},
    {82, "alpha094", 0, BufferKind::OUTPUT, 85, 1},
    {83, "alpha095", 0, BufferKind::OUTPUT, 84, 1},
    {84, "alpha096", 0, BufferKind::OUTPUT, 108, 1},
    {85, "alpha098", 0, BufferKind::OUTPUT, 60, 1},
    {86, "alpha099", 0, BufferKind::OUTPUT, 89, 1},
    {87, "alpha101", 0, BufferKind::OUTPUT, 0, 1},
    {88, "1f0f01f1c83584d6f", 3, BufferKind::TEMP, 0, 21},
    {89, "19cc09703572f7b58", 2, BufferKind::TEMP, 0, 12},
    {90, "120d49c2693eb0b8f", 3, BufferKind::TEMP, 0, 9},
    {91, "18fdad286b28e4b7b", 2, BufferKind::TEMP, 0, 1},
    {92, "17c512b9c71d3d940", 1, BufferKind::TEMP, 0, 1},
    {93, "1d5bb7cf49a1cb4a0", 1, BufferKind::TEMP, 0, 1},
    {94, "1f769b559feed1b47", 1, BufferKind::TEMP, 0, 1},
    {95, "10c16d9a20e0387b3", 1, BufferKind::TEMP, 0, 1},
    {96, "1cb80bd72b1338485", 1, BufferKind::TEMP, 0, 1},
    {97, "17237c9420bba7634", 2, BufferKind::TEMP, 0, 11},
    {98, "60b7aa4774c90f7a", 2, BufferKind::TEMP, 0, 5},
    {99, "156e62fbc370dca38", 1, BufferKind::TEMP, 0, 1},
    {100, "16123614810853bf0", 1, BufferKind::TEMP, 0, 1},
    {101, "14969b303c48d510f", 1, BufferKind::TEMP, 0, 1},
    {102, "39cd69e7f6d85328", 1, BufferKind::TEMP, 0, 1},
    {103, "10156c5755f433df0", 1, BufferKind::TEMP, 0, 1},
    {104, "1cac888714ed9f6a4", 1, BufferKind::TEMP, 0, 1},
    {105, "82b65763b5b2c7e4", 1, BufferKind::TEMP, 0, 250},
    {106, "16e18489215dc7531", 1, BufferKind::TEMP, 0, 1},
    {107, "f4055f7b15338dfe", 2, BufferKind::TEMP, 0, 1},
    {108, "160b80f2567e1055c", 1, BufferKind::TEMP, 0, 1},
    {109, "161e738cf1eba2c95", 1, BufferKind::TEMP, 0, 1},
    {110, "e2c0a8e1c6ba083a", 1, BufferKind::TEMP, 0, 1},
    {111, "11883856b1555ef59", 1, BufferKind::TEMP, 0, 50},
    {112, "1eda8e378b6c06d1a", 1, BufferKind::TEMP, 0, 1},
    {113, "14a4506c73ffa04a3", 1, BufferKind::TEMP, 0, 1},
    {114, "1a81d3f6084e29607", 1, BufferKind::TEMP, 0, 1},
    {115, "93dc794f9443fc2e", 1, BufferKind::TEMP, 0, 1},
    {116, "19b7389e0c453d803", 1, BufferKind::TEMP, 0, 1},
    {117, "2c53c8eaac8635f5", 1, BufferKind::TEMP, 0, 1},
    {118, "15d5de9a420044862", 1, BufferKind::TEMP, 0, 1},
    {119, "00adb6936a973911", 1, BufferKind::TEMP, 0, 1},
    {120, "19f751d58c7a6b64", 1, BufferKind::TEMP, 0, 1},
    {121, "1d97c2770b5a6165f", 1, BufferKind::TEMP, 0, 1},
    {122, "1ba3ef6f8151bb423", 1, BufferKind::TEMP, 0, 1},
    {123, "18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_152", 0, BufferKind::TEMP, 0, 11},
    {124, "2edba89328237aeb", 1, BufferKind::TEMP, 0, 1},
    {125, "be958f2e6e459f60", 1, BufferKind::TEMP, 0, 1},
    {126, "ade4363da9807ef1", 1, BufferKind::TEMP, 0, 1},
    {127, "14ab5e930c67d541b", 1, BufferKind::TEMP, 0, 1},
    {128, "156ece8c9e5c0c4da", 1, BufferKind::TEMP, 0, 1},
    {129, "9d962035ba5f1ea4", 2, BufferKind::TEMP, 0, 5},
    {130, "1c1ae2fca6456a911", 1, BufferKind::TEMP, 0, 1},
    {131, "077b734a024fd813", 1, BufferKind::TEMP, 0, 1},
    {132, "11b9e2ab19c5cf07f", 1, BufferKind::TEMP, 0, 1},
    {133, "106499afd0abdce0c", 1, BufferKind::TEMP, 0, 2},
    {134, "54a7c757d1b2deb5", 2, BufferKind::TEMP, 0, 1},
    {135, "16fd514be19f48a38", 1, BufferKind::TEMP, 0, 1},
    {136, "0f32950657c256a9", 1, BufferKind::TEMP, 0, 1},
    {137, "78d7937a0020cd3f", 1, BufferKind::TEMP, 0, 9},
    {138, "a16e007555996c6e", 3, BufferKind::TEMP, 0, 15},
    {139, "16aad41bb0d085fc8", 1, BufferKind::TEMP, 0, 1},
    {140, "eca4f6b3acf43b21", 1, BufferKind::TEMP, 0, 1},
    {141, "118ca723e22053d82", 1, BufferKind::TEMP, 0, 1},
    {142, "bf8995d8792205e7", 3, BufferKind::TEMP, 0, 22},
    {143, "f4bf06972d9be22e", 1, BufferKind::TEMP, 0, 1},
    {144, "b1ef8918ee2f42d9", 1, BufferKind::TEMP, 0, 1},
    {145, "2b4119829ffbe8ca", 1, BufferKind::TEMP, 0, 1},
    {146, "68d8962d72605382", 1, BufferKind::TEMP, 0, 1},
    {147, "1da3294008ba11157", 1, BufferKind::TEMP, 0, 1},
    {148, "1b968497d4dea7e45", 1, BufferKind::TEMP, 0, 1},
    {149, "1821f4158341eaadd", 1, BufferKind::TEMP, 0, 1},
    {150, "1dcccc562c26e18cc", 1, BufferKind::TEMP, 0, 1},
    {151, "e2149fed4461a684", 1, BufferKind::TEMP, 0, 1},
    {152, "alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_21", 0, BufferKind::TEMP, 0, 60},
    {153, "alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_135", 0, BufferKind::TEMP, 0, 15},
    {154, "alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_162", 0, BufferKind::TEMP, 0, 5},
    {155, "alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_180", 0, BufferKind::TEMP, 0, 200},
    {156, "26a1c5dfd27db7bb", 1, BufferKind::TEMP, 0, 1},
    {157, "493c5c7c2e79a37f", 1, BufferKind::TEMP, 0, 1},
    {158, "1bec543c83b13b08c", 1, BufferKind::TEMP, 0, 1},
    {159, "e0e3079c4a901fa8", 10, BufferKind::TEMP, 0, 230},
    {160, "1bcc1d690ec58df7f", 1, BufferKind::TEMP, 0, 6},
    {161, "10bf2e96da60b4371", 1, BufferKind::TEMP, 0, 1},
    {162, "835a27cc9bdeb830", 1, BufferKind::TEMP, 0, 1},
    {163, "1f3d21de30288a87", 1, BufferKind::TEMP, 0, 1},
    {164, "12e33a12fe6cb8a9", 1, BufferKind::TEMP, 0, 1},
    {165, "14aa873133ad7c9f4", 1, BufferKind::TEMP, 0, 1},
    {166, "50636661b8037409", 1, BufferKind::TEMP, 0, 1},
    {167, "fa408c94db08823f", 1, BufferKind::TEMP, 0, 1},
    {168, "84f75b98293a92f1", 1, BufferKind::TEMP, 0, 1},
    {169, "ae449937b099b289", 1, BufferKind::TEMP, 0, 1},
    {170, "16f2a300b18e43c49", 1, BufferKind::TEMP, 0, 1},
    {171, "54c72aeffc3e3508", 2, BufferKind::TEMP, 0, 1},
    {172, "7bba8c2207236de9", 1, BufferKind::TEMP, 0, 1},
    {173, "3eda254c3f952022", 1, BufferKind::TEMP, 0, 1},
    {174, "1b087cce3aa0b937a", 1, BufferKind::TEMP, 0, 1},
    {175, "1dc772caca00e1584", 1, BufferKind::TEMP, 0, 18},
    {176, "cd0c872219e5d6d0", 1, BufferKind::TEMP, 0, 1},
    {177, "89deb5a8070c6bf4", 1, BufferKind::TEMP, 0, 1},
    {178, "82dce2338e7ee89f", 1, BufferKind::TEMP, 0, 7},
    {179, "1b38965bcd2746ceb", 1, BufferKind::TEMP, 0, 18},
    {180, "c062f9bb82def253", 1, BufferKind::TEMP, 0, 1},
    {181, "4f68c121edd4b69a", 1, BufferKind::TEMP, 0, 1},
    {182, "18644381e8646210a", 1, BufferKind::TEMP, 0, 1},
    {183, "12db32b8edf3abd9c", 1, BufferKind::TEMP, 0, 1},
    {184, "114be7640278a0027", 1, BufferKind::TEMP, 0, 1},
    {185, "a998c0027e40f4c6", 1, BufferKind::TEMP, 0, 1},
    {186, "662df1dc40577e1e", 1, BufferKind::TEMP, 0, 1},
    {187, "90a31d273592c40c", 1, BufferKind::TEMP, 0, 1},
    {188, "e8ef8c4a12962781", 1, BufferKind::TEMP, 0, 1},
    {189, "75e672355a4f049c", 1, BufferKind::TEMP, 0, 1},
    {190, "1ac920ea885d1ed2b", 1, BufferKind::TEMP, 0, 1},
    {191, "16599f4652129dcaa", 1, BufferKind::TEMP, 0, 1},
    {192, "6722cd923f64335c", 1, BufferKind::TEMP, 0, 1},
    {193, "196f4b404382c7357", 1, BufferKind::TEMP, 0, 1},
    {194, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_72", 0, BufferKind::TEMP, 0, 10},
    {195, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_127", 0, BufferKind::TEMP, 0, 18},
    {196, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_197", 0, BufferKind::TEMP, 0, 6},
    {197, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_219", 0, BufferKind::TEMP, 0, 230},
    {198, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_246", 0, BufferKind::TEMP, 0, 20},
    {199, "493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_260", 0, BufferKind::TEMP, 0, 21},
    {200, "b8d3ad670e02e188", 1, BufferKind::TEMP, 0, 1},
    {201, "4599ae1c53681419", 1, BufferKind::TEMP, 0, 1},
    {202, "42fef27a0c3f65f3", 1, BufferKind::TEMP, 0, 1},
    {203, "d6c2cd9d0a42eb53", 1, BufferKind::TEMP, 0, 1},
    {204, "17186aeb7dd951aaf", 1, BufferKind::TEMP, 0, 1},
    {205, "122a8870451b838e7", 1, BufferKind::TEMP, 0, 1},
    {206, "152b4b6c739dec9b7", 1, BufferKind::TEMP, 0, 1},
    {207, "19bcab12c0b751190", 1, BufferKind::TEMP, 0, 1},
    {208, "8f66f09e9d504a8c", 1, BufferKind::TEMP, 0, 2},
    {209, "e5741f7564c392ac", 2, BufferKind::TEMP, 0, 1},
    {210, "1eae54ac4d54608f9", 1, BufferKind::TEMP, 0, 1},
    {211, "350c605085b2027e", 1, BufferKind::TEMP, 0, 1},
    {212, "909092e48dafead6", 1, BufferKind::TEMP, 0, 1},
    {213, "1f2d443115bbe19b8", 1, BufferKind::TEMP, 0, 1},
    {214, "1d2731cd6670fbbb9", 1, BufferKind::TEMP, 0, 1},
    {215, "122a8a80286874cc8", 3, BufferKind::TEMP, 0, 3},
    {216, "1209b15d3b8ee8a84", 1, BufferKind::TEMP, 0, 1},
    {217, "9dd1ca3b1e0306d5", 1, BufferKind::TEMP, 0, 1},
    {218, "alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_10", 0, BufferKind::TEMP, 0, 10},
    {219, "alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_43", 0, BufferKind::TEMP, 0, 2},
    {220, "alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_44", 0, BufferKind::TEMP, 0, 2},
    {221, "1a9308070e053bb70", 1, BufferKind::TEMP, 0, 1},
    {222, "a9c228e0b9dafee8", 1, BufferKind::TEMP, 0, 1},
    {223, "869f38882be45286", 1, BufferKind::TEMP, 0, 1},
    {224, "a9c228c71b191482", 1, BufferKind::TEMP, 0, 1},
    {225, "b80797652a77217e", 1, BufferKind::TEMP, 0, 1},
    {226, "e28b6a66388aca96", 1, BufferKind::TEMP, 0, 1},
    {227, "14ce42d338ddcd7bc", 1, BufferKind::TEMP, 0, 1},
    {228, "18e6ff5693a16658f", 1, BufferKind::TEMP, 0, 1},
    {229, "b096a6b338e59bd1", 1, BufferKind::TEMP, 0, 6},
    {230, "92a82f927e12be36", 1, BufferKind::TEMP, 0, 1},
    {231, "62f1b56c1f91b803", 1, BufferKind::TEMP, 0, 1},
    {232, "106498830afb12b2f", 1, BufferKind::TEMP, 0, 1},
    {233, "db1ec42f638754c4", 1, BufferKind::TEMP, 0, 1},
    {234, "19448648e73a0e59a", 1, BufferKind::TEMP, 0, 1},
    {235, "46fde329d2fadda4", 1, BufferKind::TEMP, 0, 1},
    {236, "alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_42", 0, BufferKind::TEMP, 0, 101},
    {237, "1ad82bd2893556b95", 1, BufferKind::TEMP, 0, 1},
    {238, "f6f97214486a2cc1", 1, BufferKind::TEMP, 0, 1},
    {239, "400531d71d423d49", 1, BufferKind::TEMP, 0, 1},
    {240, "17feca80f99bb159b", 1, BufferKind::TEMP, 0, 1},
    {241, "6d2930339face684", 1, BufferKind::TEMP, 0, 1},
    {242, "93385477fefd8c17", 1, BufferKind::TEMP, 0, 1},
    {243, "c484102cd55ad1b1", 1, BufferKind::TEMP, 0, 1},
    {244, "148e235db294e7525", 1, BufferKind::TEMP, 0, 1},
    {245, "53d006c904f6f37f", 2, BufferKind::TEMP, 0, 1},
    {246, "c8a42a3708a91898", 1, BufferKind::TEMP, 0, 1},
    {247, "817836889c535887", 1, BufferKind::TEMP, 0, 1},
    {248, "7d709a995a29d77f", 1, BufferKind::TEMP, 0, 1},
    {249, "a7019f9a5c963187", 1, BufferKind::TEMP, 0, 1},
    {250, "d46a09aff428b211", 1, BufferKind::TEMP, 0, 1},
    {251, "1af234ead49ceac5a", 1, BufferKind::TEMP, 0, 1},
    {252, "1163db3251b923ec7", 1, BufferKind::TEMP, 0, 1},
    {253, "1015175dbdfa9818a", 1, BufferKind::TEMP, 0, 1},
    {254, "1c125ece16202104b", 1, BufferKind::TEMP, 0, 1},
    {255, "34585a48fb808f83", 1, BufferKind::TEMP, 0, 1},
    {256, "ab239b5b61f82d07", 1, BufferKind::TEMP, 0, 1},
    {257, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_18", 0, BufferKind::TEMP, 0, 10},
    {258, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_38", 0, BufferKind::TEMP, 0, 3},
    {259, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_71", 0, BufferKind::TEMP, 0, 6},
    {260, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_78", 0, BufferKind::TEMP, 0, 26},
    {261, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_83", 0, BufferKind::TEMP, 0, 5},
    {262, "f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_105", 0, BufferKind::TEMP, 0, 7},
    {263, "6f87169c15697aec", 1, BufferKind::TEMP, 0, 1},
    {264, "1fb795dcf1c9ec054", 1, BufferKind::TEMP, 0, 1},
    {265, "e714ed386b6c8ec3", 1, BufferKind::TEMP, 0, 1},
    {266, "07afa02613eebb6d", 1, BufferKind::TEMP, 0, 1},
    {267, "1a4e2f3ba89f42951", 1, BufferKind::TEMP, 0, 1},
    {268, "17230fa8d70cb536c", 1, BufferKind::TEMP, 0, 1},
    {269, "197046de60901aa41", 1, BufferKind::TEMP, 0, 1},
    {270, "12d3f9faf4df7c584", 1, BufferKind::TEMP, 0, 1},
    {271, "1a12cbc7ebe63cfcc", 1, BufferKind::TEMP, 0, 1},
    {272, "1e7f2de38b65af18f", 1, BufferKind::TEMP, 0, 1},
    {273, "1457ce2c155a61fd4", 1, BufferKind::TEMP, 0, 1},
    {274, "78e4f6e7cee909a8", 1, BufferKind::TEMP, 0, 1},
    {275, "76fe21605013c9a7", 1, BufferKind::TEMP, 0, 1},
    {276, "381bba73519aac77", 1, BufferKind::TEMP, 0, 8},
    {277, "20e24891e9d62d6b", 1, BufferKind::TEMP, 0, 1},
    {278, "1555fc2ac5b1f47bd", 1, BufferKind::TEMP, 0, 1},
    {279, "e88f9cd0d1d87daa", 1, BufferKind::TEMP, 0, 1},
    {280, "a88237a38c9d018f", 1, BufferKind::TEMP, 0, 1},
    {281, "149416df8d2c4dac", 4, BufferKind::TEMP, 0, 20},
    {282, "718b6e819250274f", 1, BufferKind::TEMP, 0, 1},
    {283, "1d423b99fe89ce6c8", 1, BufferKind::TEMP, 0, 7},
    {284, "ac8b438bbe1cf240", 1, BufferKind::TEMP, 0, 15},
    {285, "ca86c5db4dae4c75", 1, BufferKind::TEMP, 0, 1},
    {286, "1d4580520b32d9d74", 1, BufferKind::TEMP, 0, 1},
    {287, "c7f03cfe4a9a0496", 1, BufferKind::TEMP, 0, 7},
    {288, "11ba1c0582ed7958d", 1, BufferKind::TEMP, 0, 18},
    {289, "7e2c3205f536c762", 1, BufferKind::TEMP, 0, 1},
    {290, "e083a5768021835f", 1, BufferKind::TEMP, 0, 1},
    {291, "168fddb21a03fc027", 1, BufferKind::TEMP, 0, 1},
    {292, "15c36dacf75e527a0", 1, BufferKind::TEMP, 0, 1},
    {293, "1f724efa04733d837", 1, BufferKind::TEMP, 0, 1},
    {294, "16ba41787258d0ac2", 1, BufferKind::TEMP, 0, 1},
    {295, "711b2797a20851f9", 1, BufferKind::TEMP, 0, 1},
    {296, "1d0d5bd22659e1cc3", 1, BufferKind::TEMP, 0, 1},
    {297, "13990b65aba0b7628", 1, BufferKind::TEMP, 0, 1},
    {298, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_17", 0, BufferKind::TEMP, 0, 16},
    {299, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_25", 0, BufferKind::TEMP, 0, 2},
    {300, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_33", 0, BufferKind::TEMP, 0, 6},
    {301, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_40", 0, BufferKind::TEMP, 0, 10},
    {302, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_55", 0, BufferKind::TEMP, 0, 37},
    {303, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_76", 0, BufferKind::TEMP, 0, 15},
    {304, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_111", 0, BufferKind::TEMP, 0, 18},
    {305, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_136", 0, BufferKind::TEMP, 0, 4},
    {306, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_140", 0, BufferKind::TEMP, 0, 16},
    {307, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_153", 0, BufferKind::TEMP, 0, 8},
    {308, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_155", 0, BufferKind::TEMP, 0, 21},
    {309, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_165", 0, BufferKind::TEMP, 0, 8},
    {310, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_192", 0, BufferKind::TEMP, 0, 6},
    {311, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_199", 0, BufferKind::TEMP, 0, 6},
    {312, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_221", 0, BufferKind::TEMP, 0, 9},
    {313, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_228", 0, BufferKind::TEMP, 0, 20},
    {314, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_254", 0, BufferKind::TEMP, 0, 9},
    {315, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_315", 0, BufferKind::TEMP, 0, 13},
    {316, "1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_317", 0, BufferKind::TEMP, 0, 13},
    {317, "1d4b7681a80713990", 1, BufferKind::TEMP, 0, 1},
    {318, "49be5d9c39d10d72", 1, BufferKind::TEMP, 0, 1},
    {319, "1a231b2e05e8601d3", 1, BufferKind::TEMP, 0, 1},
    {320, "351f7bdaf8ced412", 1, BufferKind::TEMP, 0, 1},
    {321, "db7aef550751a1cd", 1, BufferKind::TEMP, 0, 1},
    {322, "0189b1479ac96a49", 1, BufferKind::TEMP, 0, 1},
    {323, "4b88f8befc88e458", 1, BufferKind::TEMP, 0, 1},
    {324, "1d55ca7c2bb592f6b", 1, BufferKind::TEMP, 0, 1},
    {325, "562cec0cb33847c8", 1, BufferKind::TEMP, 0, 1},
    {326, "1bf81dfeb065cab7e", 1, BufferKind::TEMP, 0, 1},
    {327, "15b2fff9c11057958", 1, BufferKind::TEMP, 0, 1},
    {328, "d782b67ea7f219d8", 1, BufferKind::TEMP, 0, 1},
    {329, "1500a66d04da7a0f4", 1, BufferKind::TEMP, 0, 1},
    {330, "13ceb33893cac5614", 1, BufferKind::TEMP, 0, 1},
    {331, "d45ec672ff6c56cb", 1, BufferKind::TEMP, 0, 1},
    {332, "1637895cfc77c386d", 1, BufferKind::TEMP, 0, 1},
    {333, "13a8169589968006c", 1, BufferKind::TEMP, 0, 6},
    {334, "1cf1f6a8d90ea2e78", 1, BufferKind::TEMP, 0, 1},
    {335, "14c03c015491e6f07", 1, BufferKind::TEMP, 0, 1},
    {336, "1859886e85061c544", 1, BufferKind::TEMP, 0, 1},
    {337, "123e2c4ec6491301c", 1, BufferKind::TEMP, 0, 1},
    {338, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_4", 0, BufferKind::TEMP, 0, 12},
    {339, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_22", 0, BufferKind::TEMP, 0, 11},
    {340, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_26", 0, BufferKind::TEMP, 0, 7},
    {341, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_41", 0, BufferKind::TEMP, 0, 7},
    {342, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_50", 0, BufferKind::TEMP, 0, 3},
    {343, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_55", 0, BufferKind::TEMP, 0, 3},
    {344, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_59", 0, BufferKind::TEMP, 0, 17},
    {345, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_64", 0, BufferKind::TEMP, 0, 13},
    {346, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_69", 0, BufferKind::TEMP, 0, 17},
    {347, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_82", 0, BufferKind::TEMP, 0, 13},
    {348, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_87", 0, BufferKind::TEMP, 0, 17},
    {349, "49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_138", 0, BufferKind::TEMP, 0, 7},
    {350, "96cdc5fc00ecf5ae", 1, BufferKind::TEMP, 0, 1},
    {351, "ad74e3f90fab1c5b", 1, BufferKind::TEMP, 0, 1},
    {352, "507411a9986abb68", 1, BufferKind::TEMP, 0, 12},
    {353, "cd7e62b0652a2be4", 1, BufferKind::TEMP, 0, 1},
    {354, "1f45d88fcb5643470", 1, BufferKind::TEMP, 0, 12},
    {355, "1b87bba46739d9a98", 1, BufferKind::TEMP, 0, 1},
    {356, "alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_55", 0, BufferKind::TEMP, 0, 19},
    {357, "alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_88", 0, BufferKind::TEMP, 0, 7},
    {358, "alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_92", 0, BufferKind::TEMP, 0, 7},
    {359, "alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_112", 0, BufferKind::TEMP, 0, 7},
    {360, "alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_137", 0, BufferKind::TEMP, 0, 3},
    {361, "52b6ff208174d149", 1, BufferKind::TEMP, 0, 1},
    {362, "5bb02c19c3dd6729", 1, BufferKind::TEMP, 0, 1},
    {363, "11c9b76e737d91c1b", 1, BufferKind::TEMP, 0, 1},
    {364, "1107e617187eb74dc", 1, BufferKind::TEMP, 0, 1},
    {365, "1e5ad191dd30293be", 1, BufferKind::TEMP, 0, 1},
    {366, "1a07b0431ada8801a", 1, BufferKind::TEMP, 0, 1},
    {367, "1d08e7dc84e257dab", 1, BufferKind::TEMP, 0, 1},
    {368, "17428b32875256d19", 1, BufferKind::TEMP, 0, 1},
    {369, "eb6ce72b67e4317e", 1, BufferKind::TEMP, 0, 6},
    {370, "115ae56c1acc5da93", 1, BufferKind::TEMP, 0, 1},
    {371, "e07e2753279851fe", 1, BufferKind::TEMP, 0, 1},
    {372, "c6a5b1cc4b64300a", 1, BufferKind::TEMP, 0, 1},
    {373, "226e640ee8e31e3d", 1, BufferKind::TEMP, 0, 1},
    {374, "15264260244c74405", 1, BufferKind::TEMP, 0, 1},
    {375, "e05c149cd44339a3", 1, BufferKind::TEMP, 0, 21},
    {376, "16ebd20a45d3e92fe", 1, BufferKind::TEMP, 0, 5},
    {377, "cbfb1d0ffb904289", 1, BufferKind::TEMP, 0, 1},
    {378, "12c2c23e763e94135", 1, BufferKind::TEMP, 0, 1},
    {379, "fc74c311397bb045", 1, BufferKind::TEMP, 0, 1},
    {380, "165fc323207ecacad", 1, BufferKind::TEMP, 0, 1},
    {381, "64394211e6651f69", 1, BufferKind::TEMP, 0, 1},
    {382, "3af437316f85598d", 1, BufferKind::TEMP, 0, 1},
    {383, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_5", 0, BufferKind::TEMP, 0, 10},
    {384, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_49", 0, BufferKind::TEMP, 0, 20},
    {385, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_53", 0, BufferKind::TEMP, 0, 7},
    {386, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_74", 0, BufferKind::TEMP, 0, 6},
    {387, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_83", 0, BufferKind::TEMP, 0, 4},
    {388, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_132", 0, BufferKind::TEMP, 0, 3},
    {389, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_138", 0, BufferKind::TEMP, 0, 13},
    {390, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_143", 0, BufferKind::TEMP, 0, 14},
    {391, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_147", 0, BufferKind::TEMP, 0, 13},
    {392, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_166", 0, BufferKind::TEMP, 0, 20},
    {393, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_175", 0, BufferKind::TEMP, 0, 5},
    {394, "5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_205", 0, BufferKind::TEMP, 0, 3},
    {395, "186e7dd3a2080800a", 1, BufferKind::TEMP, 0, 1},
    {396, "e0868dec9669359f", 1, BufferKind::TEMP, 0, 1},
    {397, "85cedbf1ff9283ca", 1, BufferKind::TEMP, 0, 1},
    {398, "fc30d8436cd2e6f0", 1, BufferKind::TEMP, 0, 1},
    {399, "9fda089d0982e762", 1, BufferKind::TEMP, 0, 11},
    {400, "15d7d8ba49746a937", 1, BufferKind::TEMP, 0, 1},
    {401, "1cbce9dc8fd160ab0", 1, BufferKind::TEMP, 0, 1},
    {402, "1fa7018abf0f3a453", 1, BufferKind::TEMP, 0, 1},
    {403, "1acaac361d6d31bdf", 1, BufferKind::TEMP, 0, 5},
    {404, "17bd17f7435f12bee", 1, BufferKind::TEMP, 0, 1},
    {405, "1693aa3aaeb129a60", 1, BufferKind::TEMP, 0, 11},
    {406, "dad364f59a26ac50", 1, BufferKind::TEMP, 0, 1},
    {407, "dfe1845a1a8fb41d", 1, BufferKind::TEMP, 0, 1},
    {408, "164447fd516042841", 1, BufferKind::TEMP, 0, 1},
    {409, "10bf852bbded84010", 1, BufferKind::TEMP, 0, 1},
    {410, "140f4347a2e0bf204", 1, BufferKind::TEMP, 0, 1},
    {411, "cfbdcaaf4ede80b1", 1, BufferKind::TEMP, 0, 1},
    {412, "1406245cbf0a5c46f", 1, BufferKind::TEMP, 0, 1},
    {413, "137906673daae4438", 1, BufferKind::TEMP, 0, 1},
    {414, "17832f3ff2a94f427", 1, BufferKind::TEMP, 0, 1},
    {415, "1d43b462e5d69ba5d", 1, BufferKind::TEMP, 0, 1},
    {416, "2b4deadd228e75a9", 1, BufferKind::TEMP, 0, 1},
    {417, "13f9e1b099bf55621", 1, BufferKind::TEMP, 0, 1},
    {418, "95b7f4bc0dee6ab3", 1, BufferKind::TEMP, 0, 1},
    {419, "alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_280", 0, BufferKind::TEMP, 0, 14},
    {420, "1cfbffed3e23f4455", 1, BufferKind::TEMP, 0, 1},
    {421, "1494da145191886fa", 1, BufferKind::TEMP, 0, 1},
    {422, "1eb68af50dcd04d42", 1, BufferKind::TEMP, 0, 1},
    {423, "1c983e545d91c9866", 1, BufferKind::TEMP, 0, 1},
    {424, "1722e93023ecc26d8", 1, BufferKind::TEMP, 0, 3},
    {425, "0785974bf2709530", 1, BufferKind::TEMP, 0, 1},
    {426, "593d8be97d887c4e", 1, BufferKind::TEMP, 0, 1},
    {427, "aaf0b20fc619bbb2", 1, BufferKind::TEMP, 0, 1},
    {428, "18c68193caf707703", 1, BufferKind::TEMP, 0, 1},
    {429, "alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_48", 0, BufferKind::TEMP, 0, 2},
    {430, "alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_63", 0, BufferKind::TEMP, 0, 4},
    {431, "alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_67", 0, BufferKind::TEMP, 0, 8},
    {432, "577d72745758f4ff", 1, BufferKind::TEMP, 0, 1},
    {433, "19e3239463a2eeb42", 1, BufferKind::TEMP, 0, 1},
    {434, "3c0324e6b050f678", 1, BufferKind::TEMP, 0, 1},
    {435, "1502ee23db6b90d7c", 1, BufferKind::TEMP, 0, 1},
    {436, "1c1e02ad578fee8b1", 1, BufferKind::TEMP, 0, 1},
    {437, "1c760288c6fcebd4", 1, BufferKind::TEMP, 0, 1},
    {438, "9df22aa19187c2f6", 1, BufferKind::TEMP, 0, 1},
    {439, "alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_24", 0, BufferKind::TEMP, 0, 9},
    {440, "alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_29", 0, BufferKind::TEMP, 0, 7},
    {441, "alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_33", 0, BufferKind::TEMP, 0, 8},
    {442, "alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_57", 0, BufferKind::TEMP, 0, 6},
    {443, "1082a9d10e478141d", 1, BufferKind::TEMP, 0, 1},
    {444, "180ac7118f5030ca1", 1, BufferKind::TEMP, 0, 1},
    {445, "1e5e0d408d12d9522", 1, BufferKind::TEMP, 0, 1},
    {446, "c13f2073feaded25", 1, BufferKind::TEMP, 0, 1},
    {447, "30778bd3fe6f9ee4", 1, BufferKind::TEMP, 0, 1},
    {448, "132137a7499bdd6c5", 1, BufferKind::TEMP, 0, 2},
    {449, "dcfa7dc0411b83e2", 1, BufferKind::TEMP, 0, 1},
    {450, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_21", 0, BufferKind::TEMP, 0, 5},
    {451, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_29", 0, BufferKind::TEMP, 0, 7},
    {452, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_33", 0, BufferKind::TEMP, 0, 3},
    {453, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_44", 0, BufferKind::TEMP, 0, 8},
    {454, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_51", 0, BufferKind::TEMP, 0, 16},
    {455, "alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_55", 0, BufferKind::TEMP, 0, 4},
    {456, "1856762a1003c5303", 1, BufferKind::TEMP, 0, 1},
    {457, "17852424e0c297f0e", 1, BufferKind::TEMP, 0, 5},
    {458, "bc280680bb3448f5", 1, BufferKind::TEMP, 0, 1},
    {459, "1134198af3a9d5546", 1, BufferKind::TEMP, 0, 1},
    {460, "1a409bd1b3fb41908", 1, BufferKind::TEMP, 0, 1},
    {461, "e5cadef0dfe4e951", 1, BufferKind::TEMP, 0, 1},
    {462, "d7e8c3e6f7f6a559", 1, BufferKind::TEMP, 0, 15},
    {463, "1e2fe07181b3a06db", 1, BufferKind::TEMP, 0, 1},
    {464, "13197e06412cf906c", 1, BufferKind::TEMP, 0, 1},
    {465, "1f1a2321d133ec98c", 1, BufferKind::TEMP, 0, 1},
    {466, "c223205aa1dfceba", 1, BufferKind::TEMP, 0, 1},
    {467, "3cef9bf07276a7bd", 1, BufferKind::TEMP, 0, 3},
    {468, "9361c0593542cca8", 1, BufferKind::TEMP, 0, 1},
    {469, "ac3647e8d5b400bf", 1, BufferKind::TEMP, 0, 1},
    {470, "alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_6", 0, BufferKind::TEMP, 0, 7},
    {471, "alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_8", 0, BufferKind::TEMP, 0, 5},
    {472, "alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_34", 0, BufferKind::TEMP, 0, 8},
    {473, "0c3587e4cc9fb312", 1, BufferKind::TEMP, 0, 1},
    {474, "ad098f0979977549", 1, BufferKind::TEMP, 0, 1},
    {475, "11edbf4250d427421", 1, BufferKind::TEMP, 0, 1},
    {476, "4ee2dea1b7e3190e", 1, BufferKind::TEMP, 0, 1},
    {477, "1ccb8076ab4602658", 1, BufferKind::TEMP, 0, 1},
    {478, "11eb8919f2723bf62", 1, BufferKind::TEMP, 0, 1},
    {479, "6df3b3f2c4401862", 1, BufferKind::TEMP, 0, 1},
    {480, "1f6a94fd48767fcd5", 1, BufferKind::TEMP, 0, 1},
    {481, "d1aea8e58de31174", 1, BufferKind::TEMP, 0, 1},
    {482, "48714155402cad1d", 1, BufferKind::TEMP, 0, 1},
    {483, "1d85621e17bd8a9d6", 1, BufferKind::TEMP, 0, 1},
    {484, "0723b75820a79324", 1, BufferKind::TEMP, 0, 1},
    {485, "16c47e4b623012393", 1, BufferKind::TEMP, 0, 1},
    {486, "009da94a34e00581", 1, BufferKind::TEMP, 0, 1},
    {487, "1fecb90f5ea6b7a42", 1, BufferKind::TEMP, 0, 1},
    {488, "alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_48", 0, BufferKind::TEMP, 0, 20},
    {489, "alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_57", 0, BufferKind::TEMP, 0, 8},
    {490, "9b92ce11ac49056c", 1, BufferKind::TEMP, 0, 1},
    {491, "1295929018b7108e8", 1, BufferKind::TEMP, 0, 1},
    {492, "alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_35", 0, BufferKind::TEMP, 0, 5},
    {493, "1668c43c265a85276", 1, BufferKind::TEMP, 0, 1},
    {494, "104b7bca3b25ad8b3", 1, BufferKind::TEMP, 0, 1}
};

static BufferInfo *stage_1f0f01f1c83584d6f_in_buf[] = {&__buffers_alpha_101_stream[3]};
static BufferInfo *stage_1f0f01f1c83584d6f_out_buf[] = {&__buffers_alpha_101_stream[88]};
static BufferInfo *stage_19cc09703572f7b58_in_buf[] = {&__buffers_alpha_101_stream[0]};
static BufferInfo *stage_19cc09703572f7b58_out_buf[] = {&__buffers_alpha_101_stream[89]};
static BufferInfo *stage_120d49c2693eb0b8f_in_buf[] = {&__buffers_alpha_101_stream[1]};
static BufferInfo *stage_120d49c2693eb0b8f_out_buf[] = {&__buffers_alpha_101_stream[90]};
static BufferInfo *stage_17c512b9c71d3d940_in_buf[] = {&__buffers_alpha_101_stream[91]};
static BufferInfo *stage_17c512b9c71d3d940_out_buf[] = {&__buffers_alpha_101_stream[92]};
static BufferInfo *stage_1f769b559feed1b47_in_buf[] = {&__buffers_alpha_101_stream[93]};
static BufferInfo *stage_1f769b559feed1b47_out_buf[] = {&__buffers_alpha_101_stream[94]};
static BufferInfo *stage_1cb80bd72b1338485_in_buf[] = {&__buffers_alpha_101_stream[95]};
static BufferInfo *stage_1cb80bd72b1338485_out_buf[] = {&__buffers_alpha_101_stream[96]};
static BufferInfo *stage_17237c9420bba7634_in_buf[] = {&__buffers_alpha_101_stream[5]};
static BufferInfo *stage_17237c9420bba7634_out_buf[] = {&__buffers_alpha_101_stream[97]};
static BufferInfo *stage_60b7aa4774c90f7a_in_buf[] = {&__buffers_alpha_101_stream[2]};
static BufferInfo *stage_60b7aa4774c90f7a_out_buf[] = {&__buffers_alpha_101_stream[98]};
static BufferInfo *stage_16123614810853bf0_in_buf[] = {&__buffers_alpha_101_stream[99]};
static BufferInfo *stage_16123614810853bf0_out_buf[] = {&__buffers_alpha_101_stream[100]};
static BufferInfo *stage_39cd69e7f6d85328_in_buf[] = {&__buffers_alpha_101_stream[101]};
static BufferInfo *stage_39cd69e7f6d85328_out_buf[] = {&__buffers_alpha_101_stream[102]};
static BufferInfo *stage_1cac888714ed9f6a4_in_buf[] = {&__buffers_alpha_101_stream[103]};
static BufferInfo *stage_1cac888714ed9f6a4_out_buf[] = {&__buffers_alpha_101_stream[104]};
static BufferInfo *stage_18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_in_buf[] = {&__buffers_alpha_101_stream[88], &__buffers_alpha_101_stream[1], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[2]};
static BufferInfo *stage_18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_out_buf[] = {&__buffers_alpha_101_stream[91], &__buffers_alpha_101_stream[28], &__buffers_alpha_101_stream[93], &__buffers_alpha_101_stream[95], &__buffers_alpha_101_stream[11], &__buffers_alpha_101_stream[105], &__buffers_alpha_101_stream[99], &__buffers_alpha_101_stream[101], &__buffers_alpha_101_stream[103], &__buffers_alpha_101_stream[106], &__buffers_alpha_101_stream[107], &__buffers_alpha_101_stream[108], &__buffers_alpha_101_stream[109], &__buffers_alpha_101_stream[110], &__buffers_alpha_101_stream[111], &__buffers_alpha_101_stream[112], &__buffers_alpha_101_stream[113], &__buffers_alpha_101_stream[114], &__buffers_alpha_101_stream[115], &__buffers_alpha_101_stream[116], &__buffers_alpha_101_stream[117], &__buffers_alpha_101_stream[118], &__buffers_alpha_101_stream[119], &__buffers_alpha_101_stream[120], &__buffers_alpha_101_stream[121], &__buffers_alpha_101_stream[122]};
static BufferInfo *stage_2edba89328237aeb_in_buf[] = {&__buffers_alpha_101_stream[106]};
static BufferInfo *stage_2edba89328237aeb_out_buf[] = {&__buffers_alpha_101_stream[124]};
static BufferInfo *stage_ade4363da9807ef1_in_buf[] = {&__buffers_alpha_101_stream[125]};
static BufferInfo *stage_ade4363da9807ef1_out_buf[] = {&__buffers_alpha_101_stream[126]};
static BufferInfo *stage_156ece8c9e5c0c4da_in_buf[] = {&__buffers_alpha_101_stream[127]};
static BufferInfo *stage_156ece8c9e5c0c4da_out_buf[] = {&__buffers_alpha_101_stream[128]};
static BufferInfo *stage_1c1ae2fca6456a911_in_buf[] = {&__buffers_alpha_101_stream[129]};
static BufferInfo *stage_1c1ae2fca6456a911_out_buf[] = {&__buffers_alpha_101_stream[130]};
static BufferInfo *stage_11b9e2ab19c5cf07f_in_buf[] = {&__buffers_alpha_101_stream[131]};
static BufferInfo *stage_11b9e2ab19c5cf07f_out_buf[] = {&__buffers_alpha_101_stream[132]};
static BufferInfo *stage_54a7c757d1b2deb5_in_buf[] = {&__buffers_alpha_101_stream[133]};
static BufferInfo *stage_54a7c757d1b2deb5_out_buf[] = {&__buffers_alpha_101_stream[134]};
static BufferInfo *stage_0f32950657c256a9_in_buf[] = {&__buffers_alpha_101_stream[135]};
static BufferInfo *stage_0f32950657c256a9_out_buf[] = {&__buffers_alpha_101_stream[136]};
static BufferInfo *stage_alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_in_buf[] = {&__buffers_alpha_101_stream[124], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[107], &__buffers_alpha_101_stream[3]};
static BufferInfo *stage_alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_out_buf[] = {&__buffers_alpha_101_stream[24], &__buffers_alpha_101_stream[137], &__buffers_alpha_101_stream[125], &__buffers_alpha_101_stream[127], &__buffers_alpha_101_stream[129], &__buffers_alpha_101_stream[131], &__buffers_alpha_101_stream[17], &__buffers_alpha_101_stream[138], &__buffers_alpha_101_stream[133], &__buffers_alpha_101_stream[135], &__buffers_alpha_101_stream[139], &__buffers_alpha_101_stream[12], &__buffers_alpha_101_stream[26], &__buffers_alpha_101_stream[15], &__buffers_alpha_101_stream[51], &__buffers_alpha_101_stream[53], &__buffers_alpha_101_stream[55], &__buffers_alpha_101_stream[14], &__buffers_alpha_101_stream[140], &__buffers_alpha_101_stream[141], &__buffers_alpha_101_stream[142], &__buffers_alpha_101_stream[143], &__buffers_alpha_101_stream[144], &__buffers_alpha_101_stream[145], &__buffers_alpha_101_stream[146], &__buffers_alpha_101_stream[147], &__buffers_alpha_101_stream[148], &__buffers_alpha_101_stream[149], &__buffers_alpha_101_stream[150], &__buffers_alpha_101_stream[151]};
static BufferInfo *stage_26a1c5dfd27db7bb_in_buf[] = {&__buffers_alpha_101_stream[139]};
static BufferInfo *stage_26a1c5dfd27db7bb_out_buf[] = {&__buffers_alpha_101_stream[156]};
static BufferInfo *stage_1bec543c83b13b08c_in_buf[] = {&__buffers_alpha_101_stream[157]};
static BufferInfo *stage_1bec543c83b13b08c_out_buf[] = {&__buffers_alpha_101_stream[158]};
static BufferInfo *stage_1bcc1d690ec58df7f_in_buf[] = {&__buffers_alpha_101_stream[159]};
static BufferInfo *stage_1bcc1d690ec58df7f_out_buf[] = {&__buffers_alpha_101_stream[160]};
static BufferInfo *stage_835a27cc9bdeb830_in_buf[] = {&__buffers_alpha_101_stream[161]};
static BufferInfo *stage_835a27cc9bdeb830_out_buf[] = {&__buffers_alpha_101_stream[162]};
static BufferInfo *stage_12e33a12fe6cb8a9_in_buf[] = {&__buffers_alpha_101_stream[163]};
static BufferInfo *stage_12e33a12fe6cb8a9_out_buf[] = {&__buffers_alpha_101_stream[164]};
static BufferInfo *stage_50636661b8037409_in_buf[] = {&__buffers_alpha_101_stream[165]};
static BufferInfo *stage_50636661b8037409_out_buf[] = {&__buffers_alpha_101_stream[166]};
static BufferInfo *stage_84f75b98293a92f1_in_buf[] = {&__buffers_alpha_101_stream[167]};
static BufferInfo *stage_84f75b98293a92f1_out_buf[] = {&__buffers_alpha_101_stream[168]};
static BufferInfo *stage_16f2a300b18e43c49_in_buf[] = {&__buffers_alpha_101_stream[169]};
static BufferInfo *stage_16f2a300b18e43c49_out_buf[] = {&__buffers_alpha_101_stream[170]};
static BufferInfo *stage_493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_in_buf[] = {&__buffers_alpha_101_stream[156], &__buffers_alpha_101_stream[134], &__buffers_alpha_101_stream[140], &__buffers_alpha_101_stream[141], &__buffers_alpha_101_stream[142], &__buffers_alpha_101_stream[4], &__buffers_alpha_101_stream[108], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[143]};
static BufferInfo *stage_493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_out_buf[] = {&__buffers_alpha_101_stream[157], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[161], &__buffers_alpha_101_stream[163], &__buffers_alpha_101_stream[165], &__buffers_alpha_101_stream[167], &__buffers_alpha_101_stream[77], &__buffers_alpha_101_stream[169], &__buffers_alpha_101_stream[171], &__buffers_alpha_101_stream[172], &__buffers_alpha_101_stream[173], &__buffers_alpha_101_stream[174], &__buffers_alpha_101_stream[175], &__buffers_alpha_101_stream[176], &__buffers_alpha_101_stream[177], &__buffers_alpha_101_stream[178], &__buffers_alpha_101_stream[179], &__buffers_alpha_101_stream[180], &__buffers_alpha_101_stream[181], &__buffers_alpha_101_stream[182], &__buffers_alpha_101_stream[183], &__buffers_alpha_101_stream[184], &__buffers_alpha_101_stream[185], &__buffers_alpha_101_stream[186], &__buffers_alpha_101_stream[187], &__buffers_alpha_101_stream[188], &__buffers_alpha_101_stream[189], &__buffers_alpha_101_stream[190], &__buffers_alpha_101_stream[191], &__buffers_alpha_101_stream[192], &__buffers_alpha_101_stream[193]};
static BufferInfo *stage_b8d3ad670e02e188_in_buf[] = {&__buffers_alpha_101_stream[171]};
static BufferInfo *stage_b8d3ad670e02e188_out_buf[] = {&__buffers_alpha_101_stream[200]};
static BufferInfo *stage_42fef27a0c3f65f3_in_buf[] = {&__buffers_alpha_101_stream[201]};
static BufferInfo *stage_42fef27a0c3f65f3_out_buf[] = {&__buffers_alpha_101_stream[202]};
static BufferInfo *stage_17186aeb7dd951aaf_in_buf[] = {&__buffers_alpha_101_stream[203]};
static BufferInfo *stage_17186aeb7dd951aaf_out_buf[] = {&__buffers_alpha_101_stream[204]};
static BufferInfo *stage_152b4b6c739dec9b7_in_buf[] = {&__buffers_alpha_101_stream[205]};
static BufferInfo *stage_152b4b6c739dec9b7_out_buf[] = {&__buffers_alpha_101_stream[206]};
static BufferInfo *stage_8f66f09e9d504a8c_in_buf[] = {&__buffers_alpha_101_stream[207]};
static BufferInfo *stage_8f66f09e9d504a8c_out_buf[] = {&__buffers_alpha_101_stream[208]};
static BufferInfo *stage_1eae54ac4d54608f9_in_buf[] = {&__buffers_alpha_101_stream[209]};
static BufferInfo *stage_1eae54ac4d54608f9_out_buf[] = {&__buffers_alpha_101_stream[210]};
static BufferInfo *stage_909092e48dafead6_in_buf[] = {&__buffers_alpha_101_stream[211]};
static BufferInfo *stage_909092e48dafead6_out_buf[] = {&__buffers_alpha_101_stream[212]};
static BufferInfo *stage_1d2731cd6670fbbb9_in_buf[] = {&__buffers_alpha_101_stream[213]};
static BufferInfo *stage_1d2731cd6670fbbb9_out_buf[] = {&__buffers_alpha_101_stream[214]};
static BufferInfo *stage_alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_in_buf[] = {&__buffers_alpha_101_stream[172], &__buffers_alpha_101_stream[164], &__buffers_alpha_101_stream[170], &__buffers_alpha_101_stream[168], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[159]};
static BufferInfo *stage_alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_out_buf[] = {&__buffers_alpha_101_stream[79], &__buffers_alpha_101_stream[201], &__buffers_alpha_101_stream[203], &__buffers_alpha_101_stream[205], &__buffers_alpha_101_stream[207], &__buffers_alpha_101_stream[209], &__buffers_alpha_101_stream[211], &__buffers_alpha_101_stream[213], &__buffers_alpha_101_stream[215], &__buffers_alpha_101_stream[216], &__buffers_alpha_101_stream[217]};
static BufferInfo *stage_1a9308070e053bb70_in_buf[] = {&__buffers_alpha_101_stream[215]};
static BufferInfo *stage_1a9308070e053bb70_out_buf[] = {&__buffers_alpha_101_stream[221]};
static BufferInfo *stage_869f38882be45286_in_buf[] = {&__buffers_alpha_101_stream[222]};
static BufferInfo *stage_869f38882be45286_out_buf[] = {&__buffers_alpha_101_stream[223]};
static BufferInfo *stage_b80797652a77217e_in_buf[] = {&__buffers_alpha_101_stream[224]};
static BufferInfo *stage_b80797652a77217e_out_buf[] = {&__buffers_alpha_101_stream[225]};
static BufferInfo *stage_14ce42d338ddcd7bc_in_buf[] = {&__buffers_alpha_101_stream[226]};
static BufferInfo *stage_14ce42d338ddcd7bc_out_buf[] = {&__buffers_alpha_101_stream[227]};
static BufferInfo *stage_b096a6b338e59bd1_in_buf[] = {&__buffers_alpha_101_stream[228]};
static BufferInfo *stage_b096a6b338e59bd1_out_buf[] = {&__buffers_alpha_101_stream[229]};
static BufferInfo *stage_62f1b56c1f91b803_in_buf[] = {&__buffers_alpha_101_stream[230]};
static BufferInfo *stage_62f1b56c1f91b803_out_buf[] = {&__buffers_alpha_101_stream[231]};
static BufferInfo *stage_alpha033_in_buf[] = {&__buffers_alpha_101_stream[232]};
static BufferInfo *stage_alpha033_out_buf[] = {&__buffers_alpha_101_stream[38]};
static BufferInfo *stage_alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_in_buf[] = {&__buffers_alpha_101_stream[221], &__buffers_alpha_101_stream[206], &__buffers_alpha_101_stream[215], &__buffers_alpha_101_stream[210], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[173], &__buffers_alpha_101_stream[138], &__buffers_alpha_101_stream[107], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[171]};
static BufferInfo *stage_alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_out_buf[] = {&__buffers_alpha_101_stream[47], &__buffers_alpha_101_stream[222], &__buffers_alpha_101_stream[224], &__buffers_alpha_101_stream[226], &__buffers_alpha_101_stream[228], &__buffers_alpha_101_stream[230], &__buffers_alpha_101_stream[232], &__buffers_alpha_101_stream[29], &__buffers_alpha_101_stream[233], &__buffers_alpha_101_stream[234], &__buffers_alpha_101_stream[235]};
static BufferInfo *stage_1ad82bd2893556b95_in_buf[] = {&__buffers_alpha_101_stream[233]};
static BufferInfo *stage_1ad82bd2893556b95_out_buf[] = {&__buffers_alpha_101_stream[237]};
static BufferInfo *stage_400531d71d423d49_in_buf[] = {&__buffers_alpha_101_stream[238]};
static BufferInfo *stage_400531d71d423d49_out_buf[] = {&__buffers_alpha_101_stream[239]};
static BufferInfo *stage_6d2930339face684_in_buf[] = {&__buffers_alpha_101_stream[240]};
static BufferInfo *stage_6d2930339face684_out_buf[] = {&__buffers_alpha_101_stream[241]};
static BufferInfo *stage_93385477fefd8c17_in_buf[] = {&__buffers_alpha_101_stream[241]};
static BufferInfo *stage_93385477fefd8c17_out_buf[] = {&__buffers_alpha_101_stream[242]};
static BufferInfo *stage_148e235db294e7525_in_buf[] = {&__buffers_alpha_101_stream[243]};
static BufferInfo *stage_148e235db294e7525_out_buf[] = {&__buffers_alpha_101_stream[244]};
static BufferInfo *stage_c8a42a3708a91898_in_buf[] = {&__buffers_alpha_101_stream[245]};
static BufferInfo *stage_c8a42a3708a91898_out_buf[] = {&__buffers_alpha_101_stream[246]};
static BufferInfo *stage_7d709a995a29d77f_in_buf[] = {&__buffers_alpha_101_stream[247]};
static BufferInfo *stage_7d709a995a29d77f_out_buf[] = {&__buffers_alpha_101_stream[248]};
static BufferInfo *stage_f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_in_buf[] = {&__buffers_alpha_101_stream[94], &__buffers_alpha_101_stream[231], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[1], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[138], &__buffers_alpha_101_stream[216], &__buffers_alpha_101_stream[215], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[128], &__buffers_alpha_101_stream[142], &__buffers_alpha_101_stream[144], &__buffers_alpha_101_stream[214], &__buffers_alpha_101_stream[174]};
static BufferInfo *stage_f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_out_buf[] = {&__buffers_alpha_101_stream[238], &__buffers_alpha_101_stream[57], &__buffers_alpha_101_stream[240], &__buffers_alpha_101_stream[87], &__buffers_alpha_101_stream[243], &__buffers_alpha_101_stream[245], &__buffers_alpha_101_stream[27], &__buffers_alpha_101_stream[247], &__buffers_alpha_101_stream[249], &__buffers_alpha_101_stream[250], &__buffers_alpha_101_stream[251], &__buffers_alpha_101_stream[252], &__buffers_alpha_101_stream[253], &__buffers_alpha_101_stream[254], &__buffers_alpha_101_stream[255], &__buffers_alpha_101_stream[256]};
static BufferInfo *stage_6f87169c15697aec_in_buf[] = {&__buffers_alpha_101_stream[249]};
static BufferInfo *stage_6f87169c15697aec_out_buf[] = {&__buffers_alpha_101_stream[263]};
static BufferInfo *stage_e714ed386b6c8ec3_in_buf[] = {&__buffers_alpha_101_stream[264]};
static BufferInfo *stage_e714ed386b6c8ec3_out_buf[] = {&__buffers_alpha_101_stream[265]};
static BufferInfo *stage_1a4e2f3ba89f42951_in_buf[] = {&__buffers_alpha_101_stream[266]};
static BufferInfo *stage_1a4e2f3ba89f42951_out_buf[] = {&__buffers_alpha_101_stream[267]};
static BufferInfo *stage_17230fa8d70cb536c_in_buf[] = {&__buffers_alpha_101_stream[267]};
static BufferInfo *stage_17230fa8d70cb536c_out_buf[] = {&__buffers_alpha_101_stream[268]};
static BufferInfo *stage_12d3f9faf4df7c584_in_buf[] = {&__buffers_alpha_101_stream[269]};
static BufferInfo *stage_12d3f9faf4df7c584_out_buf[] = {&__buffers_alpha_101_stream[270]};
static BufferInfo *stage_1e7f2de38b65af18f_in_buf[] = {&__buffers_alpha_101_stream[271]};
static BufferInfo *stage_1e7f2de38b65af18f_out_buf[] = {&__buffers_alpha_101_stream[272]};
static BufferInfo *stage_78e4f6e7cee909a8_in_buf[] = {&__buffers_alpha_101_stream[273]};
static BufferInfo *stage_78e4f6e7cee909a8_out_buf[] = {&__buffers_alpha_101_stream[274]};
static BufferInfo *stage_381bba73519aac77_in_buf[] = {&__buffers_alpha_101_stream[275]};
static BufferInfo *stage_381bba73519aac77_out_buf[] = {&__buffers_alpha_101_stream[276]};
static BufferInfo *stage_1555fc2ac5b1f47bd_in_buf[] = {&__buffers_alpha_101_stream[277]};
static BufferInfo *stage_1555fc2ac5b1f47bd_out_buf[] = {&__buffers_alpha_101_stream[278]};
static BufferInfo *stage_a88237a38c9d018f_in_buf[] = {&__buffers_alpha_101_stream[279]};
static BufferInfo *stage_a88237a38c9d018f_out_buf[] = {&__buffers_alpha_101_stream[280]};
static BufferInfo *stage_1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_in_buf[] = {&__buffers_alpha_101_stream[1], &__buffers_alpha_101_stream[246], &__buffers_alpha_101_stream[250], &__buffers_alpha_101_stream[251], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[175], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[91]};
static BufferInfo *stage_1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_out_buf[] = {&__buffers_alpha_101_stream[264], &__buffers_alpha_101_stream[266], &__buffers_alpha_101_stream[269], &__buffers_alpha_101_stream[271], &__buffers_alpha_101_stream[273], &__buffers_alpha_101_stream[275], &__buffers_alpha_101_stream[277], &__buffers_alpha_101_stream[279], &__buffers_alpha_101_stream[281], &__buffers_alpha_101_stream[282], &__buffers_alpha_101_stream[283], &__buffers_alpha_101_stream[284], &__buffers_alpha_101_stream[285], &__buffers_alpha_101_stream[286], &__buffers_alpha_101_stream[287], &__buffers_alpha_101_stream[288], &__buffers_alpha_101_stream[289], &__buffers_alpha_101_stream[290], &__buffers_alpha_101_stream[291], &__buffers_alpha_101_stream[292], &__buffers_alpha_101_stream[293], &__buffers_alpha_101_stream[294], &__buffers_alpha_101_stream[295], &__buffers_alpha_101_stream[296], &__buffers_alpha_101_stream[297]};
static BufferInfo *stage_1d4b7681a80713990_in_buf[] = {&__buffers_alpha_101_stream[282]};
static BufferInfo *stage_1d4b7681a80713990_out_buf[] = {&__buffers_alpha_101_stream[317]};
static BufferInfo *stage_1a231b2e05e8601d3_in_buf[] = {&__buffers_alpha_101_stream[318]};
static BufferInfo *stage_1a231b2e05e8601d3_out_buf[] = {&__buffers_alpha_101_stream[319]};
static BufferInfo *stage_db7aef550751a1cd_in_buf[] = {&__buffers_alpha_101_stream[320]};
static BufferInfo *stage_db7aef550751a1cd_out_buf[] = {&__buffers_alpha_101_stream[321]};
static BufferInfo *stage_4b88f8befc88e458_in_buf[] = {&__buffers_alpha_101_stream[322]};
static BufferInfo *stage_4b88f8befc88e458_out_buf[] = {&__buffers_alpha_101_stream[323]};
static BufferInfo *stage_562cec0cb33847c8_in_buf[] = {&__buffers_alpha_101_stream[324]};
static BufferInfo *stage_562cec0cb33847c8_out_buf[] = {&__buffers_alpha_101_stream[325]};
static BufferInfo *stage_15b2fff9c11057958_in_buf[] = {&__buffers_alpha_101_stream[326]};
static BufferInfo *stage_15b2fff9c11057958_out_buf[] = {&__buffers_alpha_101_stream[327]};
static BufferInfo *stage_1500a66d04da7a0f4_in_buf[] = {&__buffers_alpha_101_stream[328]};
static BufferInfo *stage_1500a66d04da7a0f4_out_buf[] = {&__buffers_alpha_101_stream[329]};
static BufferInfo *stage_d45ec672ff6c56cb_in_buf[] = {&__buffers_alpha_101_stream[330]};
static BufferInfo *stage_d45ec672ff6c56cb_out_buf[] = {&__buffers_alpha_101_stream[331]};
static BufferInfo *stage_13a8169589968006c_in_buf[] = {&__buffers_alpha_101_stream[332]};
static BufferInfo *stage_13a8169589968006c_out_buf[] = {&__buffers_alpha_101_stream[333]};
static BufferInfo *stage_49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_in_buf[] = {&__buffers_alpha_101_stream[317], &__buffers_alpha_101_stream[3], &__buffers_alpha_101_stream[281], &__buffers_alpha_101_stream[176], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[177], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[283]};
static BufferInfo *stage_49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_out_buf[] = {&__buffers_alpha_101_stream[318], &__buffers_alpha_101_stream[320], &__buffers_alpha_101_stream[322], &__buffers_alpha_101_stream[324], &__buffers_alpha_101_stream[326], &__buffers_alpha_101_stream[328], &__buffers_alpha_101_stream[330], &__buffers_alpha_101_stream[332], &__buffers_alpha_101_stream[334], &__buffers_alpha_101_stream[335], &__buffers_alpha_101_stream[336], &__buffers_alpha_101_stream[337]};
static BufferInfo *stage_96cdc5fc00ecf5ae_in_buf[] = {&__buffers_alpha_101_stream[334]};
static BufferInfo *stage_96cdc5fc00ecf5ae_out_buf[] = {&__buffers_alpha_101_stream[350]};
static BufferInfo *stage_507411a9986abb68_in_buf[] = {&__buffers_alpha_101_stream[351]};
static BufferInfo *stage_507411a9986abb68_out_buf[] = {&__buffers_alpha_101_stream[352]};
static BufferInfo *stage_alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_in_buf[] = {&__buffers_alpha_101_stream[280], &__buffers_alpha_101_stream[323], &__buffers_alpha_101_stream[319], &__buffers_alpha_101_stream[335], &__buffers_alpha_101_stream[333], &__buffers_alpha_101_stream[229], &__buffers_alpha_101_stream[325], &__buffers_alpha_101_stream[336], &__buffers_alpha_101_stream[321], &__buffers_alpha_101_stream[237], &__buffers_alpha_101_stream[284], &__buffers_alpha_101_stream[276], &__buffers_alpha_101_stream[89], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[178]};
static BufferInfo *stage_alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_out_buf[] = {&__buffers_alpha_101_stream[83], &__buffers_alpha_101_stream[65], &__buffers_alpha_101_stream[7], &__buffers_alpha_101_stream[66], &__buffers_alpha_101_stream[43], &__buffers_alpha_101_stream[351], &__buffers_alpha_101_stream[9], &__buffers_alpha_101_stream[81], &__buffers_alpha_101_stream[353], &__buffers_alpha_101_stream[354], &__buffers_alpha_101_stream[355]};
static BufferInfo *stage_52b6ff208174d149_in_buf[] = {&__buffers_alpha_101_stream[353]};
static BufferInfo *stage_52b6ff208174d149_out_buf[] = {&__buffers_alpha_101_stream[361]};
static BufferInfo *stage_11c9b76e737d91c1b_in_buf[] = {&__buffers_alpha_101_stream[362]};
static BufferInfo *stage_11c9b76e737d91c1b_out_buf[] = {&__buffers_alpha_101_stream[363]};
static BufferInfo *stage_1e5ad191dd30293be_in_buf[] = {&__buffers_alpha_101_stream[364]};
static BufferInfo *stage_1e5ad191dd30293be_out_buf[] = {&__buffers_alpha_101_stream[365]};
static BufferInfo *stage_1d08e7dc84e257dab_in_buf[] = {&__buffers_alpha_101_stream[366]};
static BufferInfo *stage_1d08e7dc84e257dab_out_buf[] = {&__buffers_alpha_101_stream[367]};
static BufferInfo *stage_eb6ce72b67e4317e_in_buf[] = {&__buffers_alpha_101_stream[368]};
static BufferInfo *stage_eb6ce72b67e4317e_out_buf[] = {&__buffers_alpha_101_stream[369]};
static BufferInfo *stage_e07e2753279851fe_in_buf[] = {&__buffers_alpha_101_stream[370]};
static BufferInfo *stage_e07e2753279851fe_out_buf[] = {&__buffers_alpha_101_stream[371]};
static BufferInfo *stage_226e640ee8e31e3d_in_buf[] = {&__buffers_alpha_101_stream[372]};
static BufferInfo *stage_226e640ee8e31e3d_out_buf[] = {&__buffers_alpha_101_stream[373]};
static BufferInfo *stage_e05c149cd44339a3_in_buf[] = {&__buffers_alpha_101_stream[374]};
static BufferInfo *stage_e05c149cd44339a3_out_buf[] = {&__buffers_alpha_101_stream[375]};
static BufferInfo *stage_5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_in_buf[] = {&__buffers_alpha_101_stream[361], &__buffers_alpha_101_stream[285], &__buffers_alpha_101_stream[286], &__buffers_alpha_101_stream[352], &__buffers_alpha_101_stream[355], &__buffers_alpha_101_stream[354], &__buffers_alpha_101_stream[287], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[0], &__buffers_alpha_101_stream[2], &__buffers_alpha_101_stream[288], &__buffers_alpha_101_stream[179], &__buffers_alpha_101_stream[1], &__buffers_alpha_101_stream[281], &__buffers_alpha_101_stream[252], &__buffers_alpha_101_stream[5], &__buffers_alpha_101_stream[109], &__buffers_alpha_101_stream[110], &__buffers_alpha_101_stream[180]};
static BufferInfo *stage_5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_out_buf[] = {&__buffers_alpha_101_stream[362], &__buffers_alpha_101_stream[364], &__buffers_alpha_101_stream[366], &__buffers_alpha_101_stream[368], &__buffers_alpha_101_stream[370], &__buffers_alpha_101_stream[376], &__buffers_alpha_101_stream[31], &__buffers_alpha_101_stream[372], &__buffers_alpha_101_stream[374], &__buffers_alpha_101_stream[377], &__buffers_alpha_101_stream[378], &__buffers_alpha_101_stream[379], &__buffers_alpha_101_stream[380], &__buffers_alpha_101_stream[381], &__buffers_alpha_101_stream[382]};
static BufferInfo *stage_186e7dd3a2080800a_in_buf[] = {&__buffers_alpha_101_stream[377]};
static BufferInfo *stage_186e7dd3a2080800a_out_buf[] = {&__buffers_alpha_101_stream[395]};
static BufferInfo *stage_85cedbf1ff9283ca_in_buf[] = {&__buffers_alpha_101_stream[396]};
static BufferInfo *stage_85cedbf1ff9283ca_out_buf[] = {&__buffers_alpha_101_stream[397]};
static BufferInfo *stage_9fda089d0982e762_in_buf[] = {&__buffers_alpha_101_stream[398]};
static BufferInfo *stage_9fda089d0982e762_out_buf[] = {&__buffers_alpha_101_stream[399]};
static BufferInfo *stage_1cbce9dc8fd160ab0_in_buf[] = {&__buffers_alpha_101_stream[400]};
static BufferInfo *stage_1cbce9dc8fd160ab0_out_buf[] = {&__buffers_alpha_101_stream[401]};
static BufferInfo *stage_1acaac361d6d31bdf_in_buf[] = {&__buffers_alpha_101_stream[402]};
static BufferInfo *stage_1acaac361d6d31bdf_out_buf[] = {&__buffers_alpha_101_stream[403]};
static BufferInfo *stage_alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_in_buf[] = {&__buffers_alpha_101_stream[395], &__buffers_alpha_101_stream[378], &__buffers_alpha_101_stream[379], &__buffers_alpha_101_stream[373], &__buffers_alpha_101_stream[376], &__buffers_alpha_101_stream[375], &__buffers_alpha_101_stream[88], &__buffers_alpha_101_stream[97], &__buffers_alpha_101_stream[160], &__buffers_alpha_101_stream[90], &__buffers_alpha_101_stream[1], &__buffers_alpha_101_stream[253], &__buffers_alpha_101_stream[254], &__buffers_alpha_101_stream[369], &__buffers_alpha_101_stream[98], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[380], &__buffers_alpha_101_stream[265]};
static BufferInfo *stage_alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_out_buf[] = {&__buffers_alpha_101_stream[82], &__buffers_alpha_101_stream[56], &__buffers_alpha_101_stream[396], &__buffers_alpha_101_stream[59], &__buffers_alpha_101_stream[398], &__buffers_alpha_101_stream[8], &__buffers_alpha_101_stream[400], &__buffers_alpha_101_stream[402], &__buffers_alpha_101_stream[67], &__buffers_alpha_101_stream[404], &__buffers_alpha_101_stream[405], &__buffers_alpha_101_stream[406], &__buffers_alpha_101_stream[407], &__buffers_alpha_101_stream[408], &__buffers_alpha_101_stream[409], &__buffers_alpha_101_stream[410], &__buffers_alpha_101_stream[411], &__buffers_alpha_101_stream[412], &__buffers_alpha_101_stream[413], &__buffers_alpha_101_stream[414], &__buffers_alpha_101_stream[415], &__buffers_alpha_101_stream[416], &__buffers_alpha_101_stream[417], &__buffers_alpha_101_stream[418]};
static BufferInfo *stage_1cfbffed3e23f4455_in_buf[] = {&__buffers_alpha_101_stream[404]};
static BufferInfo *stage_1cfbffed3e23f4455_out_buf[] = {&__buffers_alpha_101_stream[420]};
static BufferInfo *stage_1eb68af50dcd04d42_in_buf[] = {&__buffers_alpha_101_stream[421]};
static BufferInfo *stage_1eb68af50dcd04d42_out_buf[] = {&__buffers_alpha_101_stream[422]};
static BufferInfo *stage_1722e93023ecc26d8_in_buf[] = {&__buffers_alpha_101_stream[423]};
static BufferInfo *stage_1722e93023ecc26d8_out_buf[] = {&__buffers_alpha_101_stream[424]};
static BufferInfo *stage_593d8be97d887c4e_in_buf[] = {&__buffers_alpha_101_stream[425]};
static BufferInfo *stage_593d8be97d887c4e_out_buf[] = {&__buffers_alpha_101_stream[426]};
static BufferInfo *stage_18c68193caf707703_in_buf[] = {&__buffers_alpha_101_stream[427]};
static BufferInfo *stage_18c68193caf707703_out_buf[] = {&__buffers_alpha_101_stream[428]};
static BufferInfo *stage_alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_in_buf[] = {&__buffers_alpha_101_stream[420], &__buffers_alpha_101_stream[403], &__buffers_alpha_101_stream[399], &__buffers_alpha_101_stream[405], &__buffers_alpha_101_stream[406], &__buffers_alpha_101_stream[397], &__buffers_alpha_101_stream[407], &__buffers_alpha_101_stream[408], &__buffers_alpha_101_stream[409], &__buffers_alpha_101_stream[410], &__buffers_alpha_101_stream[411], &__buffers_alpha_101_stream[412], &__buffers_alpha_101_stream[413], &__buffers_alpha_101_stream[414], &__buffers_alpha_101_stream[415], &__buffers_alpha_101_stream[416], &__buffers_alpha_101_stream[381]};
static BufferInfo *stage_alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_out_buf[] = {&__buffers_alpha_101_stream[21], &__buffers_alpha_101_stream[54], &__buffers_alpha_101_stream[421], &__buffers_alpha_101_stream[18], &__buffers_alpha_101_stream[423], &__buffers_alpha_101_stream[425], &__buffers_alpha_101_stream[427], &__buffers_alpha_101_stream[49], &__buffers_alpha_101_stream[84]};
static BufferInfo *stage_19e3239463a2eeb42_in_buf[] = {&__buffers_alpha_101_stream[432]};
static BufferInfo *stage_19e3239463a2eeb42_out_buf[] = {&__buffers_alpha_101_stream[433]};
static BufferInfo *stage_1502ee23db6b90d7c_in_buf[] = {&__buffers_alpha_101_stream[434]};
static BufferInfo *stage_1502ee23db6b90d7c_out_buf[] = {&__buffers_alpha_101_stream[435]};
static BufferInfo *stage_1c760288c6fcebd4_in_buf[] = {&__buffers_alpha_101_stream[436]};
static BufferInfo *stage_1c760288c6fcebd4_out_buf[] = {&__buffers_alpha_101_stream[437]};
static BufferInfo *stage_alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_in_buf[] = {&__buffers_alpha_101_stream[428], &__buffers_alpha_101_stream[274], &__buffers_alpha_101_stream[422], &__buffers_alpha_101_stream[424], &__buffers_alpha_101_stream[417], &__buffers_alpha_101_stream[418], &__buffers_alpha_101_stream[367], &__buffers_alpha_101_stream[426], &__buffers_alpha_101_stream[363], &__buffers_alpha_101_stream[382], &__buffers_alpha_101_stream[289], &__buffers_alpha_101_stream[290], &__buffers_alpha_101_stream[291], &__buffers_alpha_101_stream[292], &__buffers_alpha_101_stream[181], &__buffers_alpha_101_stream[281], &__buffers_alpha_101_stream[2]};
static BufferInfo *stage_alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_out_buf[] = {&__buffers_alpha_101_stream[71], &__buffers_alpha_101_stream[32], &__buffers_alpha_101_stream[20], &__buffers_alpha_101_stream[432], &__buffers_alpha_101_stream[74], &__buffers_alpha_101_stream[69], &__buffers_alpha_101_stream[434], &__buffers_alpha_101_stream[436], &__buffers_alpha_101_stream[438]};
static BufferInfo *stage_alpha028_in_buf[] = {&__buffers_alpha_101_stream[438]};
static BufferInfo *stage_alpha028_out_buf[] = {&__buffers_alpha_101_stream[33]};
static BufferInfo *stage_180ac7118f5030ca1_in_buf[] = {&__buffers_alpha_101_stream[443]};
static BufferInfo *stage_180ac7118f5030ca1_out_buf[] = {&__buffers_alpha_101_stream[444]};
static BufferInfo *stage_c13f2073feaded25_in_buf[] = {&__buffers_alpha_101_stream[445]};
static BufferInfo *stage_c13f2073feaded25_out_buf[] = {&__buffers_alpha_101_stream[446]};
static BufferInfo *stage_132137a7499bdd6c5_in_buf[] = {&__buffers_alpha_101_stream[447]};
static BufferInfo *stage_132137a7499bdd6c5_out_buf[] = {&__buffers_alpha_101_stream[448]};
static BufferInfo *stage_alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_in_buf[] = {&__buffers_alpha_101_stream[435], &__buffers_alpha_101_stream[331], &__buffers_alpha_101_stream[371], &__buffers_alpha_101_stream[437], &__buffers_alpha_101_stream[248], &__buffers_alpha_101_stream[433], &__buffers_alpha_101_stream[281], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[293], &__buffers_alpha_101_stream[294], &__buffers_alpha_101_stream[90], &__buffers_alpha_101_stream[98], &__buffers_alpha_101_stream[88], &__buffers_alpha_101_stream[89], &__buffers_alpha_101_stream[278], &__buffers_alpha_101_stream[295], &__buffers_alpha_101_stream[272], &__buffers_alpha_101_stream[350], &__buffers_alpha_101_stream[270], &__buffers_alpha_101_stream[244], &__buffers_alpha_101_stream[97], &__buffers_alpha_101_stream[255]};
static BufferInfo *stage_alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_out_buf[] = {&__buffers_alpha_101_stream[86], &__buffers_alpha_101_stream[73], &__buffers_alpha_101_stream[85], &__buffers_alpha_101_stream[443], &__buffers_alpha_101_stream[445], &__buffers_alpha_101_stream[68], &__buffers_alpha_101_stream[78], &__buffers_alpha_101_stream[447], &__buffers_alpha_101_stream[76], &__buffers_alpha_101_stream[449]};
static BufferInfo *stage_17852424e0c297f0e_in_buf[] = {&__buffers_alpha_101_stream[456]};
static BufferInfo *stage_17852424e0c297f0e_out_buf[] = {&__buffers_alpha_101_stream[457]};
static BufferInfo *stage_alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_in_buf[] = {&__buffers_alpha_101_stream[327], &__buffers_alpha_101_stream[444], &__buffers_alpha_101_stream[448], &__buffers_alpha_101_stream[449], &__buffers_alpha_101_stream[446], &__buffers_alpha_101_stream[242], &__buffers_alpha_101_stream[268], &__buffers_alpha_101_stream[256], &__buffers_alpha_101_stream[239], &__buffers_alpha_101_stream[227], &__buffers_alpha_101_stream[234], &__buffers_alpha_101_stream[225], &__buffers_alpha_101_stream[223], &__buffers_alpha_101_stream[329], &__buffers_alpha_101_stream[208], &__buffers_alpha_101_stream[209], &__buffers_alpha_101_stream[204], &__buffers_alpha_101_stream[217]};
static BufferInfo *stage_alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_out_buf[] = {&__buffers_alpha_101_stream[64], &__buffers_alpha_101_stream[456], &__buffers_alpha_101_stream[80], &__buffers_alpha_101_stream[61], &__buffers_alpha_101_stream[25], &__buffers_alpha_101_stream[10], &__buffers_alpha_101_stream[16], &__buffers_alpha_101_stream[60], &__buffers_alpha_101_stream[37]};
static BufferInfo *stage_1134198af3a9d5546_in_buf[] = {&__buffers_alpha_101_stream[458]};
static BufferInfo *stage_1134198af3a9d5546_out_buf[] = {&__buffers_alpha_101_stream[459]};
static BufferInfo *stage_alpha025_in_buf[] = {&__buffers_alpha_101_stream[460]};
static BufferInfo *stage_alpha025_out_buf[] = {&__buffers_alpha_101_stream[30]};
static BufferInfo *stage_d7e8c3e6f7f6a559_in_buf[] = {&__buffers_alpha_101_stream[461]};
static BufferInfo *stage_d7e8c3e6f7f6a559_out_buf[] = {&__buffers_alpha_101_stream[462]};
static BufferInfo *stage_13197e06412cf906c_in_buf[] = {&__buffers_alpha_101_stream[463]};
static BufferInfo *stage_13197e06412cf906c_out_buf[] = {&__buffers_alpha_101_stream[464]};
static BufferInfo *stage_c223205aa1dfceba_in_buf[] = {&__buffers_alpha_101_stream[465]};
static BufferInfo *stage_c223205aa1dfceba_out_buf[] = {&__buffers_alpha_101_stream[466]};
static BufferInfo *stage_9361c0593542cca8_in_buf[] = {&__buffers_alpha_101_stream[467]};
static BufferInfo *stage_9361c0593542cca8_out_buf[] = {&__buffers_alpha_101_stream[468]};
static BufferInfo *stage_alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_in_buf[] = {&__buffers_alpha_101_stream[457], &__buffers_alpha_101_stream[105], &__buffers_alpha_101_stream[142], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[245], &__buffers_alpha_101_stream[202], &__buffers_alpha_101_stream[200], &__buffers_alpha_101_stream[182], &__buffers_alpha_101_stream[183], &__buffers_alpha_101_stream[111], &__buffers_alpha_101_stream[184], &__buffers_alpha_101_stream[185], &__buffers_alpha_101_stream[186], &__buffers_alpha_101_stream[187], &__buffers_alpha_101_stream[188]};
static BufferInfo *stage_alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_out_buf[] = {&__buffers_alpha_101_stream[34], &__buffers_alpha_101_stream[458], &__buffers_alpha_101_stream[460], &__buffers_alpha_101_stream[36], &__buffers_alpha_101_stream[461], &__buffers_alpha_101_stream[463], &__buffers_alpha_101_stream[465], &__buffers_alpha_101_stream[467], &__buffers_alpha_101_stream[469]};
static BufferInfo *stage_0c3587e4cc9fb312_in_buf[] = {&__buffers_alpha_101_stream[469]};
static BufferInfo *stage_0c3587e4cc9fb312_out_buf[] = {&__buffers_alpha_101_stream[473]};
static BufferInfo *stage_11edbf4250d427421_in_buf[] = {&__buffers_alpha_101_stream[474]};
static BufferInfo *stage_11edbf4250d427421_out_buf[] = {&__buffers_alpha_101_stream[475]};
static BufferInfo *stage_1ccb8076ab4602658_in_buf[] = {&__buffers_alpha_101_stream[476]};
static BufferInfo *stage_1ccb8076ab4602658_out_buf[] = {&__buffers_alpha_101_stream[477]};
static BufferInfo *stage_6df3b3f2c4401862_in_buf[] = {&__buffers_alpha_101_stream[478]};
static BufferInfo *stage_6df3b3f2c4401862_out_buf[] = {&__buffers_alpha_101_stream[479]};
static BufferInfo *stage_alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_in_buf[] = {&__buffers_alpha_101_stream[464], &__buffers_alpha_101_stream[365], &__buffers_alpha_101_stream[473], &__buffers_alpha_101_stream[337], &__buffers_alpha_101_stream[296], &__buffers_alpha_101_stream[468], &__buffers_alpha_101_stream[462], &__buffers_alpha_101_stream[459], &__buffers_alpha_101_stream[158], &__buffers_alpha_101_stream[189], &__buffers_alpha_101_stream[162], &__buffers_alpha_101_stream[190], &__buffers_alpha_101_stream[191], &__buffers_alpha_101_stream[192], &__buffers_alpha_101_stream[159], &__buffers_alpha_101_stream[193], &__buffers_alpha_101_stream[112], &__buffers_alpha_101_stream[136], &__buffers_alpha_101_stream[134]};
static BufferInfo *stage_alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_out_buf[] = {&__buffers_alpha_101_stream[72], &__buffers_alpha_101_stream[70], &__buffers_alpha_101_stream[52], &__buffers_alpha_101_stream[474], &__buffers_alpha_101_stream[41], &__buffers_alpha_101_stream[476], &__buffers_alpha_101_stream[478], &__buffers_alpha_101_stream[46], &__buffers_alpha_101_stream[42]};
static BufferInfo *stage_d1aea8e58de31174_in_buf[] = {&__buffers_alpha_101_stream[480]};
static BufferInfo *stage_d1aea8e58de31174_out_buf[] = {&__buffers_alpha_101_stream[481]};
static BufferInfo *stage_1d85621e17bd8a9d6_in_buf[] = {&__buffers_alpha_101_stream[482]};
static BufferInfo *stage_1d85621e17bd8a9d6_out_buf[] = {&__buffers_alpha_101_stream[483]};
static BufferInfo *stage_16c47e4b623012393_in_buf[] = {&__buffers_alpha_101_stream[484]};
static BufferInfo *stage_16c47e4b623012393_out_buf[] = {&__buffers_alpha_101_stream[485]};
static BufferInfo *stage_1fecb90f5ea6b7a42_in_buf[] = {&__buffers_alpha_101_stream[486]};
static BufferInfo *stage_1fecb90f5ea6b7a42_out_buf[] = {&__buffers_alpha_101_stream[487]};
static BufferInfo *stage_alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_in_buf[] = {&__buffers_alpha_101_stream[479], &__buffers_alpha_101_stream[466], &__buffers_alpha_101_stream[475], &__buffers_alpha_101_stream[401], &__buffers_alpha_101_stream[145], &__buffers_alpha_101_stream[138], &__buffers_alpha_101_stream[113], &__buffers_alpha_101_stream[114], &__buffers_alpha_101_stream[132], &__buffers_alpha_101_stream[146], &__buffers_alpha_101_stream[129], &__buffers_alpha_101_stream[147], &__buffers_alpha_101_stream[148], &__buffers_alpha_101_stream[149], &__buffers_alpha_101_stream[166], &__buffers_alpha_101_stream[212], &__buffers_alpha_101_stream[126], &__buffers_alpha_101_stream[150], &__buffers_alpha_101_stream[137]};
static BufferInfo *stage_alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_out_buf[] = {&__buffers_alpha_101_stream[75], &__buffers_alpha_101_stream[62], &__buffers_alpha_101_stream[480], &__buffers_alpha_101_stream[35], &__buffers_alpha_101_stream[482], &__buffers_alpha_101_stream[50], &__buffers_alpha_101_stream[484], &__buffers_alpha_101_stream[486], &__buffers_alpha_101_stream[48]};
static BufferInfo *stage_alpha034_in_buf[] = {&__buffers_alpha_101_stream[490]};
static BufferInfo *stage_alpha034_out_buf[] = {&__buffers_alpha_101_stream[39]};
static BufferInfo *stage_alpha001_in_buf[] = {&__buffers_alpha_101_stream[491]};
static BufferInfo *stage_alpha001_out_buf[] = {&__buffers_alpha_101_stream[6]};
static BufferInfo *stage_alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_in_buf[] = {&__buffers_alpha_101_stream[485], &__buffers_alpha_101_stream[151], &__buffers_alpha_101_stream[263], &__buffers_alpha_101_stream[483], &__buffers_alpha_101_stream[487], &__buffers_alpha_101_stream[481], &__buffers_alpha_101_stream[104], &__buffers_alpha_101_stream[130], &__buffers_alpha_101_stream[102], &__buffers_alpha_101_stream[100], &__buffers_alpha_101_stream[115], &__buffers_alpha_101_stream[116], &__buffers_alpha_101_stream[297], &__buffers_alpha_101_stream[117], &__buffers_alpha_101_stream[118], &__buffers_alpha_101_stream[119], &__buffers_alpha_101_stream[235]};
static BufferInfo *stage_alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_out_buf[] = {&__buffers_alpha_101_stream[44], &__buffers_alpha_101_stream[22], &__buffers_alpha_101_stream[23], &__buffers_alpha_101_stream[490], &__buffers_alpha_101_stream[13], &__buffers_alpha_101_stream[19], &__buffers_alpha_101_stream[40], &__buffers_alpha_101_stream[491], &__buffers_alpha_101_stream[58]};
static BufferInfo *stage_104b7bca3b25ad8b3_in_buf[] = {&__buffers_alpha_101_stream[493]};
static BufferInfo *stage_104b7bca3b25ad8b3_out_buf[] = {&__buffers_alpha_101_stream[494]};
static BufferInfo *stage_alpha040_1668c43c265a85276_in_buf[] = {&__buffers_alpha_101_stream[120], &__buffers_alpha_101_stream[121], &__buffers_alpha_101_stream[96], &__buffers_alpha_101_stream[92], &__buffers_alpha_101_stream[90], &__buffers_alpha_101_stream[122]};
static BufferInfo *stage_alpha040_1668c43c265a85276_out_buf[] = {&__buffers_alpha_101_stream[45], &__buffers_alpha_101_stream[493]};
static BufferInfo *stage_alpha062_in_buf[] = {&__buffers_alpha_101_stream[477], &__buffers_alpha_101_stream[494]};
static BufferInfo *stage_alpha062_out_buf[] = {&__buffers_alpha_101_stream[63]};

namespace {
extern Stage *stage_alpha_101_stream__1f0f01f1c83584d6f_dep[3];
extern Stage *stage_alpha_101_stream__19cc09703572f7b58_dep[2];
extern Stage *stage_alpha_101_stream__120d49c2693eb0b8f_dep[3];
extern Stage *stage_alpha_101_stream__17c512b9c71d3d940_dep[1];
extern Stage *stage_alpha_101_stream__1f769b559feed1b47_dep[1];
extern Stage *stage_alpha_101_stream__1cb80bd72b1338485_dep[1];
extern Stage *stage_alpha_101_stream__17237c9420bba7634_dep[2];
extern Stage *stage_alpha_101_stream__60b7aa4774c90f7a_dep[2];
extern Stage *stage_alpha_101_stream__16123614810853bf0_dep[1];
extern Stage *stage_alpha_101_stream__39cd69e7f6d85328_dep[1];
extern Stage *stage_alpha_101_stream__1cac888714ed9f6a4_dep[1];
extern Stage *stage_alpha_101_stream__18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_dep[17];
extern Stage *stage_alpha_101_stream__2edba89328237aeb_dep[1];
extern Stage *stage_alpha_101_stream__ade4363da9807ef1_dep[1];
extern Stage *stage_alpha_101_stream__156ece8c9e5c0c4da_dep[1];
extern Stage *stage_alpha_101_stream__1c1ae2fca6456a911_dep[1];
extern Stage *stage_alpha_101_stream__11b9e2ab19c5cf07f_dep[1];
extern Stage *stage_alpha_101_stream__54a7c757d1b2deb5_dep[2];
extern Stage *stage_alpha_101_stream__0f32950657c256a9_dep[1];
extern Stage *stage_alpha_101_stream__alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_dep[13];
extern Stage *stage_alpha_101_stream__26a1c5dfd27db7bb_dep[1];
extern Stage *stage_alpha_101_stream__1bec543c83b13b08c_dep[1];
extern Stage *stage_alpha_101_stream__1bcc1d690ec58df7f_dep[1];
extern Stage *stage_alpha_101_stream__835a27cc9bdeb830_dep[1];
extern Stage *stage_alpha_101_stream__12e33a12fe6cb8a9_dep[1];
extern Stage *stage_alpha_101_stream__50636661b8037409_dep[1];
extern Stage *stage_alpha_101_stream__84f75b98293a92f1_dep[1];
extern Stage *stage_alpha_101_stream__16f2a300b18e43c49_dep[1];
extern Stage *stage_alpha_101_stream__493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_dep[20];
extern Stage *stage_alpha_101_stream__b8d3ad670e02e188_dep[1];
extern Stage *stage_alpha_101_stream__42fef27a0c3f65f3_dep[1];
extern Stage *stage_alpha_101_stream__17186aeb7dd951aaf_dep[1];
extern Stage *stage_alpha_101_stream__152b4b6c739dec9b7_dep[1];
extern Stage *stage_alpha_101_stream__8f66f09e9d504a8c_dep[1];
extern Stage *stage_alpha_101_stream__1eae54ac4d54608f9_dep[1];
extern Stage *stage_alpha_101_stream__909092e48dafead6_dep[1];
extern Stage *stage_alpha_101_stream__1d2731cd6670fbbb9_dep[1];
extern Stage *stage_alpha_101_stream__alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_dep[11];
extern Stage *stage_alpha_101_stream__1a9308070e053bb70_dep[1];
extern Stage *stage_alpha_101_stream__869f38882be45286_dep[1];
extern Stage *stage_alpha_101_stream__b80797652a77217e_dep[1];
extern Stage *stage_alpha_101_stream__14ce42d338ddcd7bc_dep[1];
extern Stage *stage_alpha_101_stream__b096a6b338e59bd1_dep[1];
extern Stage *stage_alpha_101_stream__62f1b56c1f91b803_dep[1];
Stage **stage_alpha_101_stream__alpha033_dep = nullptr;
extern Stage *stage_alpha_101_stream__alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_dep[9];
extern Stage *stage_alpha_101_stream__1ad82bd2893556b95_dep[1];
extern Stage *stage_alpha_101_stream__400531d71d423d49_dep[1];
extern Stage *stage_alpha_101_stream__6d2930339face684_dep[1];
extern Stage *stage_alpha_101_stream__93385477fefd8c17_dep[1];
extern Stage *stage_alpha_101_stream__148e235db294e7525_dep[1];
extern Stage *stage_alpha_101_stream__c8a42a3708a91898_dep[1];
extern Stage *stage_alpha_101_stream__7d709a995a29d77f_dep[1];
extern Stage *stage_alpha_101_stream__f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_dep[12];
extern Stage *stage_alpha_101_stream__6f87169c15697aec_dep[1];
extern Stage *stage_alpha_101_stream__e714ed386b6c8ec3_dep[1];
extern Stage *stage_alpha_101_stream__1a4e2f3ba89f42951_dep[1];
extern Stage *stage_alpha_101_stream__17230fa8d70cb536c_dep[1];
extern Stage *stage_alpha_101_stream__12d3f9faf4df7c584_dep[1];
extern Stage *stage_alpha_101_stream__1e7f2de38b65af18f_dep[1];
extern Stage *stage_alpha_101_stream__78e4f6e7cee909a8_dep[1];
extern Stage *stage_alpha_101_stream__381bba73519aac77_dep[1];
extern Stage *stage_alpha_101_stream__1555fc2ac5b1f47bd_dep[1];
extern Stage *stage_alpha_101_stream__a88237a38c9d018f_dep[1];
extern Stage *stage_alpha_101_stream__1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_dep[16];
extern Stage *stage_alpha_101_stream__1d4b7681a80713990_dep[1];
extern Stage *stage_alpha_101_stream__1a231b2e05e8601d3_dep[1];
extern Stage *stage_alpha_101_stream__db7aef550751a1cd_dep[1];
extern Stage *stage_alpha_101_stream__4b88f8befc88e458_dep[1];
extern Stage *stage_alpha_101_stream__562cec0cb33847c8_dep[1];
extern Stage *stage_alpha_101_stream__15b2fff9c11057958_dep[1];
extern Stage *stage_alpha_101_stream__1500a66d04da7a0f4_dep[1];
extern Stage *stage_alpha_101_stream__d45ec672ff6c56cb_dep[1];
extern Stage *stage_alpha_101_stream__13a8169589968006c_dep[1];
extern Stage *stage_alpha_101_stream__49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_dep[11];
extern Stage *stage_alpha_101_stream__96cdc5fc00ecf5ae_dep[1];
extern Stage *stage_alpha_101_stream__507411a9986abb68_dep[1];
extern Stage *stage_alpha_101_stream__alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_dep[3];
extern Stage *stage_alpha_101_stream__52b6ff208174d149_dep[1];
extern Stage *stage_alpha_101_stream__11c9b76e737d91c1b_dep[1];
extern Stage *stage_alpha_101_stream__1e5ad191dd30293be_dep[1];
extern Stage *stage_alpha_101_stream__1d08e7dc84e257dab_dep[1];
extern Stage *stage_alpha_101_stream__eb6ce72b67e4317e_dep[1];
extern Stage *stage_alpha_101_stream__e07e2753279851fe_dep[1];
extern Stage *stage_alpha_101_stream__226e640ee8e31e3d_dep[1];
extern Stage *stage_alpha_101_stream__e05c149cd44339a3_dep[1];
extern Stage *stage_alpha_101_stream__5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_dep[11];
extern Stage *stage_alpha_101_stream__186e7dd3a2080800a_dep[1];
extern Stage *stage_alpha_101_stream__85cedbf1ff9283ca_dep[1];
extern Stage *stage_alpha_101_stream__9fda089d0982e762_dep[1];
extern Stage *stage_alpha_101_stream__1cbce9dc8fd160ab0_dep[1];
extern Stage *stage_alpha_101_stream__1acaac361d6d31bdf_dep[1];
extern Stage *stage_alpha_101_stream__alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_dep[7];
extern Stage *stage_alpha_101_stream__1cfbffed3e23f4455_dep[1];
extern Stage *stage_alpha_101_stream__1eb68af50dcd04d42_dep[1];
extern Stage *stage_alpha_101_stream__1722e93023ecc26d8_dep[1];
extern Stage *stage_alpha_101_stream__593d8be97d887c4e_dep[1];
extern Stage *stage_alpha_101_stream__18c68193caf707703_dep[1];
extern Stage *stage_alpha_101_stream__alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_dep[4];
extern Stage *stage_alpha_101_stream__19e3239463a2eeb42_dep[1];
extern Stage *stage_alpha_101_stream__1502ee23db6b90d7c_dep[1];
extern Stage *stage_alpha_101_stream__1c760288c6fcebd4_dep[1];
extern Stage *stage_alpha_101_stream__alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_dep[4];
Stage **stage_alpha_101_stream__alpha028_dep = nullptr;
extern Stage *stage_alpha_101_stream__180ac7118f5030ca1_dep[1];
extern Stage *stage_alpha_101_stream__c13f2073feaded25_dep[1];
extern Stage *stage_alpha_101_stream__132137a7499bdd6c5_dep[1];
extern Stage *stage_alpha_101_stream__alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_dep[4];
extern Stage *stage_alpha_101_stream__17852424e0c297f0e_dep[1];
extern Stage *stage_alpha_101_stream__alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_dep[1];
extern Stage *stage_alpha_101_stream__1134198af3a9d5546_dep[1];
Stage **stage_alpha_101_stream__alpha025_dep = nullptr;
extern Stage *stage_alpha_101_stream__d7e8c3e6f7f6a559_dep[1];
extern Stage *stage_alpha_101_stream__13197e06412cf906c_dep[1];
extern Stage *stage_alpha_101_stream__c223205aa1dfceba_dep[1];
extern Stage *stage_alpha_101_stream__9361c0593542cca8_dep[1];
extern Stage *stage_alpha_101_stream__alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_dep[7];
extern Stage *stage_alpha_101_stream__0c3587e4cc9fb312_dep[1];
extern Stage *stage_alpha_101_stream__11edbf4250d427421_dep[1];
extern Stage *stage_alpha_101_stream__1ccb8076ab4602658_dep[1];
extern Stage *stage_alpha_101_stream__6df3b3f2c4401862_dep[1];
extern Stage *stage_alpha_101_stream__alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_dep[3];
extern Stage *stage_alpha_101_stream__d1aea8e58de31174_dep[1];
extern Stage *stage_alpha_101_stream__1d85621e17bd8a9d6_dep[1];
extern Stage *stage_alpha_101_stream__16c47e4b623012393_dep[1];
extern Stage *stage_alpha_101_stream__1fecb90f5ea6b7a42_dep[1];
extern Stage *stage_alpha_101_stream__alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_dep[4];
Stage **stage_alpha_101_stream__alpha034_dep = nullptr;
Stage **stage_alpha_101_stream__alpha001_dep = nullptr;
extern Stage *stage_alpha_101_stream__alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_dep[2];
extern Stage *stage_alpha_101_stream__104b7bca3b25ad8b3_dep[1];
extern Stage *stage_alpha_101_stream__alpha040_1668c43c265a85276_dep[1];
Stage **stage_alpha_101_stream__alpha062_dep = nullptr;
}


static auto stage_alpha_101_stream__1f0f01f1c83584d6f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__19cc09703572f7b58 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__120d49c2693eb0b8f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__17c512b9c71d3d940 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1f769b559feed1b47 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1cb80bd72b1338485 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__17237c9420bba7634 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__60b7aa4774c90f7a = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__16123614810853bf0 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__39cd69e7f6d85328 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1cac888714ed9f6a4 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__2edba89328237aeb = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__ade4363da9807ef1 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__156ece8c9e5c0c4da = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1c1ae2fca6456a911 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__11b9e2ab19c5cf07f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__54a7c757d1b2deb5 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__0f32950657c256a9 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__26a1c5dfd27db7bb = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1bec543c83b13b08c = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1bcc1d690ec58df7f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__835a27cc9bdeb830 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__12e33a12fe6cb8a9 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__50636661b8037409 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__84f75b98293a92f1 = ScaleStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__16f2a300b18e43c49 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__b8d3ad670e02e188 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__42fef27a0c3f65f3 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__17186aeb7dd951aaf = ScaleStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__152b4b6c739dec9b7 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__8f66f09e9d504a8c = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1eae54ac4d54608f9 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__909092e48dafead6 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1d2731cd6670fbbb9 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__1a9308070e053bb70 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__869f38882be45286 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__b80797652a77217e = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__14ce42d338ddcd7bc = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__b096a6b338e59bd1 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__62f1b56c1f91b803 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__alpha033 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__1ad82bd2893556b95 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__400531d71d423d49 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__6d2930339face684 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__93385477fefd8c17 = ScaleStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__148e235db294e7525 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__c8a42a3708a91898 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__7d709a995a29d77f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__6f87169c15697aec = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__e714ed386b6c8ec3 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1a4e2f3ba89f42951 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__17230fa8d70cb536c = ScaleStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__12d3f9faf4df7c584 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1e7f2de38b65af18f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__78e4f6e7cee909a8 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__381bba73519aac77 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1555fc2ac5b1f47bd = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__a88237a38c9d018f = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__1d4b7681a80713990 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1a231b2e05e8601d3 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__db7aef550751a1cd = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__4b88f8befc88e458 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__562cec0cb33847c8 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__15b2fff9c11057958 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1500a66d04da7a0f4 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__d45ec672ff6c56cb = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__13a8169589968006c = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__96cdc5fc00ecf5ae = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__507411a9986abb68 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__52b6ff208174d149 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__11c9b76e737d91c1b = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1e5ad191dd30293be = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1d08e7dc84e257dab = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__eb6ce72b67e4317e = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__e07e2753279851fe = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__226e640ee8e31e3d = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__e05c149cd44339a3 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__186e7dd3a2080800a = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__85cedbf1ff9283ca = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__9fda089d0982e762 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1cbce9dc8fd160ab0 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1acaac361d6d31bdf = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__1cfbffed3e23f4455 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1eb68af50dcd04d42 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1722e93023ecc26d8 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__593d8be97d887c4e = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__18c68193caf707703 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__19e3239463a2eeb42 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1502ee23db6b90d7c = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1c760288c6fcebd4 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__alpha028 = ScaleStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__180ac7118f5030ca1 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__c13f2073feaded25 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__132137a7499bdd6c5 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__17852424e0c297f0e = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__1134198af3a9d5546 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__alpha025 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__d7e8c3e6f7f6a559 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__13197e06412cf906c = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__c223205aa1dfceba = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__9361c0593542cca8 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__0c3587e4cc9fb312 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__11edbf4250d427421 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1ccb8076ab4602658 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__6df3b3f2c4401862 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__d1aea8e58de31174 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1d85621e17bd8a9d6 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__16c47e4b623012393 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__1fecb90f5ea6b7a42 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__alpha034 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;
static auto stage_alpha_101_stream__alpha001 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static auto stage_alpha_101_stream__104b7bca3b25ad8b3 = RankStocks<MapperSTREAM<float, 8>, MapperSTREAM<float, 8>>;

static Stage __stages_alpha_101_stream[] = {
    {/*f*/ stage_alpha_101_stream__1f0f01f1c83584d6f, /*dependers*/ stage_alpha_101_stream__1f0f01f1c83584d6f_dep, /*num_dependers*/ 3,
     /*in_buffers*/ stage_1f0f01f1c83584d6f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1f0f01f1c83584d6f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 0,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 0},
    {/*f*/ stage_alpha_101_stream__19cc09703572f7b58, /*dependers*/ stage_alpha_101_stream__19cc09703572f7b58_dep, /*num_dependers*/ 2,
     /*in_buffers*/ stage_19cc09703572f7b58_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_19cc09703572f7b58_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 0,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 1},
    {/*f*/ stage_alpha_101_stream__120d49c2693eb0b8f, /*dependers*/ stage_alpha_101_stream__120d49c2693eb0b8f_dep, /*num_dependers*/ 3,
     /*in_buffers*/ stage_120d49c2693eb0b8f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_120d49c2693eb0b8f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 0,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 2},
    {/*f*/ stage_alpha_101_stream__17c512b9c71d3d940, /*dependers*/ stage_alpha_101_stream__17c512b9c71d3d940_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_17c512b9c71d3d940_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_17c512b9c71d3d940_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 3},
    {/*f*/ stage_alpha_101_stream__1f769b559feed1b47, /*dependers*/ stage_alpha_101_stream__1f769b559feed1b47_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1f769b559feed1b47_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1f769b559feed1b47_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 4},
    {/*f*/ stage_alpha_101_stream__1cb80bd72b1338485, /*dependers*/ stage_alpha_101_stream__1cb80bd72b1338485_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1cb80bd72b1338485_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1cb80bd72b1338485_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 5},
    {/*f*/ stage_alpha_101_stream__17237c9420bba7634, /*dependers*/ stage_alpha_101_stream__17237c9420bba7634_dep, /*num_dependers*/ 2,
     /*in_buffers*/ stage_17237c9420bba7634_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_17237c9420bba7634_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 0,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 6},
    {/*f*/ stage_alpha_101_stream__60b7aa4774c90f7a, /*dependers*/ stage_alpha_101_stream__60b7aa4774c90f7a_dep, /*num_dependers*/ 2,
     /*in_buffers*/ stage_60b7aa4774c90f7a_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_60b7aa4774c90f7a_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 0,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 7},
    {/*f*/ stage_alpha_101_stream__16123614810853bf0, /*dependers*/ stage_alpha_101_stream__16123614810853bf0_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_16123614810853bf0_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_16123614810853bf0_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 8},
    {/*f*/ stage_alpha_101_stream__39cd69e7f6d85328, /*dependers*/ stage_alpha_101_stream__39cd69e7f6d85328_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_39cd69e7f6d85328_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_39cd69e7f6d85328_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 9},
    {/*f*/ stage_alpha_101_stream__1cac888714ed9f6a4, /*dependers*/ stage_alpha_101_stream__1cac888714ed9f6a4_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1cac888714ed9f6a4_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1cac888714ed9f6a4_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 10},
    {/*f*/ stage_alpha_101_stream__18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423, /*dependers*/ stage_alpha_101_stream__18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_dep, /*num_dependers*/ 17,
     /*in_buffers*/ stage_18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_in_buf, /*num_in_buffers*/ 6,
     /*out_buffers*/ stage_18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_out_buf, /*num_out_buffers*/ 26, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 11},
    {/*f*/ stage_alpha_101_stream__2edba89328237aeb, /*dependers*/ stage_alpha_101_stream__2edba89328237aeb_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_2edba89328237aeb_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_2edba89328237aeb_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 12},
    {/*f*/ stage_alpha_101_stream__ade4363da9807ef1, /*dependers*/ stage_alpha_101_stream__ade4363da9807ef1_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_ade4363da9807ef1_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_ade4363da9807ef1_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 13},
    {/*f*/ stage_alpha_101_stream__156ece8c9e5c0c4da, /*dependers*/ stage_alpha_101_stream__156ece8c9e5c0c4da_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_156ece8c9e5c0c4da_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_156ece8c9e5c0c4da_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 14},
    {/*f*/ stage_alpha_101_stream__1c1ae2fca6456a911, /*dependers*/ stage_alpha_101_stream__1c1ae2fca6456a911_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1c1ae2fca6456a911_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1c1ae2fca6456a911_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 15},
    {/*f*/ stage_alpha_101_stream__11b9e2ab19c5cf07f, /*dependers*/ stage_alpha_101_stream__11b9e2ab19c5cf07f_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_11b9e2ab19c5cf07f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_11b9e2ab19c5cf07f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 16},
    {/*f*/ stage_alpha_101_stream__54a7c757d1b2deb5, /*dependers*/ stage_alpha_101_stream__54a7c757d1b2deb5_dep, /*num_dependers*/ 2,
     /*in_buffers*/ stage_54a7c757d1b2deb5_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_54a7c757d1b2deb5_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 17},
    {/*f*/ stage_alpha_101_stream__0f32950657c256a9, /*dependers*/ stage_alpha_101_stream__0f32950657c256a9_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_0f32950657c256a9_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_0f32950657c256a9_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 18},
    {/*f*/ stage_alpha_101_stream__alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684, /*dependers*/ stage_alpha_101_stream__alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_dep, /*num_dependers*/ 13,
     /*in_buffers*/ stage_alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_in_buf, /*num_in_buffers*/ 5,
     /*out_buffers*/ stage_alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_out_buf, /*num_out_buffers*/ 30, /*pending_out*/ 2,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 19},
    {/*f*/ stage_alpha_101_stream__26a1c5dfd27db7bb, /*dependers*/ stage_alpha_101_stream__26a1c5dfd27db7bb_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_26a1c5dfd27db7bb_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_26a1c5dfd27db7bb_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 20},
    {/*f*/ stage_alpha_101_stream__1bec543c83b13b08c, /*dependers*/ stage_alpha_101_stream__1bec543c83b13b08c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1bec543c83b13b08c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1bec543c83b13b08c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 21},
    {/*f*/ stage_alpha_101_stream__1bcc1d690ec58df7f, /*dependers*/ stage_alpha_101_stream__1bcc1d690ec58df7f_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1bcc1d690ec58df7f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1bcc1d690ec58df7f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 22},
    {/*f*/ stage_alpha_101_stream__835a27cc9bdeb830, /*dependers*/ stage_alpha_101_stream__835a27cc9bdeb830_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_835a27cc9bdeb830_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_835a27cc9bdeb830_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 23},
    {/*f*/ stage_alpha_101_stream__12e33a12fe6cb8a9, /*dependers*/ stage_alpha_101_stream__12e33a12fe6cb8a9_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_12e33a12fe6cb8a9_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_12e33a12fe6cb8a9_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 24},
    {/*f*/ stage_alpha_101_stream__50636661b8037409, /*dependers*/ stage_alpha_101_stream__50636661b8037409_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_50636661b8037409_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_50636661b8037409_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 25},
    {/*f*/ stage_alpha_101_stream__84f75b98293a92f1, /*dependers*/ stage_alpha_101_stream__84f75b98293a92f1_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_84f75b98293a92f1_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_84f75b98293a92f1_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 26},
    {/*f*/ stage_alpha_101_stream__16f2a300b18e43c49, /*dependers*/ stage_alpha_101_stream__16f2a300b18e43c49_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_16f2a300b18e43c49_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_16f2a300b18e43c49_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 27},
    {/*f*/ stage_alpha_101_stream__493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357, /*dependers*/ stage_alpha_101_stream__493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_dep, /*num_dependers*/ 20,
     /*in_buffers*/ stage_493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_in_buf, /*num_in_buffers*/ 12,
     /*out_buffers*/ stage_493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_out_buf, /*num_out_buffers*/ 31, /*pending_out*/ 4,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 28},
    {/*f*/ stage_alpha_101_stream__b8d3ad670e02e188, /*dependers*/ stage_alpha_101_stream__b8d3ad670e02e188_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_b8d3ad670e02e188_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_b8d3ad670e02e188_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 29},
    {/*f*/ stage_alpha_101_stream__42fef27a0c3f65f3, /*dependers*/ stage_alpha_101_stream__42fef27a0c3f65f3_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_42fef27a0c3f65f3_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_42fef27a0c3f65f3_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 30},
    {/*f*/ stage_alpha_101_stream__17186aeb7dd951aaf, /*dependers*/ stage_alpha_101_stream__17186aeb7dd951aaf_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_17186aeb7dd951aaf_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_17186aeb7dd951aaf_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 31},
    {/*f*/ stage_alpha_101_stream__152b4b6c739dec9b7, /*dependers*/ stage_alpha_101_stream__152b4b6c739dec9b7_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_152b4b6c739dec9b7_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_152b4b6c739dec9b7_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 32},
    {/*f*/ stage_alpha_101_stream__8f66f09e9d504a8c, /*dependers*/ stage_alpha_101_stream__8f66f09e9d504a8c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_8f66f09e9d504a8c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_8f66f09e9d504a8c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 33},
    {/*f*/ stage_alpha_101_stream__1eae54ac4d54608f9, /*dependers*/ stage_alpha_101_stream__1eae54ac4d54608f9_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1eae54ac4d54608f9_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1eae54ac4d54608f9_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 34},
    {/*f*/ stage_alpha_101_stream__909092e48dafead6, /*dependers*/ stage_alpha_101_stream__909092e48dafead6_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_909092e48dafead6_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_909092e48dafead6_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 35},
    {/*f*/ stage_alpha_101_stream__1d2731cd6670fbbb9, /*dependers*/ stage_alpha_101_stream__1d2731cd6670fbbb9_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1d2731cd6670fbbb9_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1d2731cd6670fbbb9_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 36},
    {/*f*/ stage_alpha_101_stream__alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5, /*dependers*/ stage_alpha_101_stream__alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_dep, /*num_dependers*/ 11,
     /*in_buffers*/ stage_alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_in_buf, /*num_in_buffers*/ 6,
     /*out_buffers*/ stage_alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_out_buf, /*num_out_buffers*/ 11, /*pending_out*/ 4,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 37},
    {/*f*/ stage_alpha_101_stream__1a9308070e053bb70, /*dependers*/ stage_alpha_101_stream__1a9308070e053bb70_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1a9308070e053bb70_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1a9308070e053bb70_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 38},
    {/*f*/ stage_alpha_101_stream__869f38882be45286, /*dependers*/ stage_alpha_101_stream__869f38882be45286_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_869f38882be45286_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_869f38882be45286_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 39},
    {/*f*/ stage_alpha_101_stream__b80797652a77217e, /*dependers*/ stage_alpha_101_stream__b80797652a77217e_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_b80797652a77217e_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_b80797652a77217e_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 40},
    {/*f*/ stage_alpha_101_stream__14ce42d338ddcd7bc, /*dependers*/ stage_alpha_101_stream__14ce42d338ddcd7bc_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_14ce42d338ddcd7bc_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_14ce42d338ddcd7bc_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 41},
    {/*f*/ stage_alpha_101_stream__b096a6b338e59bd1, /*dependers*/ stage_alpha_101_stream__b096a6b338e59bd1_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_b096a6b338e59bd1_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_b096a6b338e59bd1_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 42},
    {/*f*/ stage_alpha_101_stream__62f1b56c1f91b803, /*dependers*/ stage_alpha_101_stream__62f1b56c1f91b803_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_62f1b56c1f91b803_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_62f1b56c1f91b803_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 43},
    {/*f*/ stage_alpha_101_stream__alpha033, /*dependers*/ stage_alpha_101_stream__alpha033_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha033_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_alpha033_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 44},
    {/*f*/ stage_alpha_101_stream__alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4, /*dependers*/ stage_alpha_101_stream__alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_dep, /*num_dependers*/ 9,
     /*in_buffers*/ stage_alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_in_buf, /*num_in_buffers*/ 11,
     /*out_buffers*/ stage_alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_out_buf, /*num_out_buffers*/ 11, /*pending_out*/ 7,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 45},
    {/*f*/ stage_alpha_101_stream__1ad82bd2893556b95, /*dependers*/ stage_alpha_101_stream__1ad82bd2893556b95_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1ad82bd2893556b95_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1ad82bd2893556b95_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 46},
    {/*f*/ stage_alpha_101_stream__400531d71d423d49, /*dependers*/ stage_alpha_101_stream__400531d71d423d49_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_400531d71d423d49_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_400531d71d423d49_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 47},
    {/*f*/ stage_alpha_101_stream__6d2930339face684, /*dependers*/ stage_alpha_101_stream__6d2930339face684_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_6d2930339face684_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_6d2930339face684_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 48},
    {/*f*/ stage_alpha_101_stream__93385477fefd8c17, /*dependers*/ stage_alpha_101_stream__93385477fefd8c17_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_93385477fefd8c17_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_93385477fefd8c17_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 49},
    {/*f*/ stage_alpha_101_stream__148e235db294e7525, /*dependers*/ stage_alpha_101_stream__148e235db294e7525_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_148e235db294e7525_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_148e235db294e7525_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 50},
    {/*f*/ stage_alpha_101_stream__c8a42a3708a91898, /*dependers*/ stage_alpha_101_stream__c8a42a3708a91898_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_c8a42a3708a91898_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_c8a42a3708a91898_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 51},
    {/*f*/ stage_alpha_101_stream__7d709a995a29d77f, /*dependers*/ stage_alpha_101_stream__7d709a995a29d77f_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_7d709a995a29d77f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_7d709a995a29d77f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 52},
    {/*f*/ stage_alpha_101_stream__f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07, /*dependers*/ stage_alpha_101_stream__f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_dep, /*num_dependers*/ 12,
     /*in_buffers*/ stage_f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_in_buf, /*num_in_buffers*/ 16,
     /*out_buffers*/ stage_f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_out_buf, /*num_out_buffers*/ 16, /*pending_out*/ 7,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 53},
    {/*f*/ stage_alpha_101_stream__6f87169c15697aec, /*dependers*/ stage_alpha_101_stream__6f87169c15697aec_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_6f87169c15697aec_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_6f87169c15697aec_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 54},
    {/*f*/ stage_alpha_101_stream__e714ed386b6c8ec3, /*dependers*/ stage_alpha_101_stream__e714ed386b6c8ec3_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_e714ed386b6c8ec3_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_e714ed386b6c8ec3_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 55},
    {/*f*/ stage_alpha_101_stream__1a4e2f3ba89f42951, /*dependers*/ stage_alpha_101_stream__1a4e2f3ba89f42951_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1a4e2f3ba89f42951_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1a4e2f3ba89f42951_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 56},
    {/*f*/ stage_alpha_101_stream__17230fa8d70cb536c, /*dependers*/ stage_alpha_101_stream__17230fa8d70cb536c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_17230fa8d70cb536c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_17230fa8d70cb536c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 57},
    {/*f*/ stage_alpha_101_stream__12d3f9faf4df7c584, /*dependers*/ stage_alpha_101_stream__12d3f9faf4df7c584_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_12d3f9faf4df7c584_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_12d3f9faf4df7c584_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 58},
    {/*f*/ stage_alpha_101_stream__1e7f2de38b65af18f, /*dependers*/ stage_alpha_101_stream__1e7f2de38b65af18f_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1e7f2de38b65af18f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1e7f2de38b65af18f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 59},
    {/*f*/ stage_alpha_101_stream__78e4f6e7cee909a8, /*dependers*/ stage_alpha_101_stream__78e4f6e7cee909a8_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_78e4f6e7cee909a8_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_78e4f6e7cee909a8_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 60},
    {/*f*/ stage_alpha_101_stream__381bba73519aac77, /*dependers*/ stage_alpha_101_stream__381bba73519aac77_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_381bba73519aac77_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_381bba73519aac77_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 61},
    {/*f*/ stage_alpha_101_stream__1555fc2ac5b1f47bd, /*dependers*/ stage_alpha_101_stream__1555fc2ac5b1f47bd_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1555fc2ac5b1f47bd_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1555fc2ac5b1f47bd_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 62},
    {/*f*/ stage_alpha_101_stream__a88237a38c9d018f, /*dependers*/ stage_alpha_101_stream__a88237a38c9d018f_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_a88237a38c9d018f_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_a88237a38c9d018f_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 63},
    {/*f*/ stage_alpha_101_stream__1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628, /*dependers*/ stage_alpha_101_stream__1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_dep, /*num_dependers*/ 16,
     /*in_buffers*/ stage_1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_in_buf, /*num_in_buffers*/ 11,
     /*out_buffers*/ stage_1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_out_buf, /*num_out_buffers*/ 25, /*pending_out*/ 4,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 64},
    {/*f*/ stage_alpha_101_stream__1d4b7681a80713990, /*dependers*/ stage_alpha_101_stream__1d4b7681a80713990_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1d4b7681a80713990_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1d4b7681a80713990_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 65},
    {/*f*/ stage_alpha_101_stream__1a231b2e05e8601d3, /*dependers*/ stage_alpha_101_stream__1a231b2e05e8601d3_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1a231b2e05e8601d3_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1a231b2e05e8601d3_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 66},
    {/*f*/ stage_alpha_101_stream__db7aef550751a1cd, /*dependers*/ stage_alpha_101_stream__db7aef550751a1cd_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_db7aef550751a1cd_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_db7aef550751a1cd_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 67},
    {/*f*/ stage_alpha_101_stream__4b88f8befc88e458, /*dependers*/ stage_alpha_101_stream__4b88f8befc88e458_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_4b88f8befc88e458_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_4b88f8befc88e458_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 68},
    {/*f*/ stage_alpha_101_stream__562cec0cb33847c8, /*dependers*/ stage_alpha_101_stream__562cec0cb33847c8_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_562cec0cb33847c8_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_562cec0cb33847c8_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 69},
    {/*f*/ stage_alpha_101_stream__15b2fff9c11057958, /*dependers*/ stage_alpha_101_stream__15b2fff9c11057958_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_15b2fff9c11057958_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_15b2fff9c11057958_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 70},
    {/*f*/ stage_alpha_101_stream__1500a66d04da7a0f4, /*dependers*/ stage_alpha_101_stream__1500a66d04da7a0f4_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1500a66d04da7a0f4_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1500a66d04da7a0f4_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 71},
    {/*f*/ stage_alpha_101_stream__d45ec672ff6c56cb, /*dependers*/ stage_alpha_101_stream__d45ec672ff6c56cb_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_d45ec672ff6c56cb_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_d45ec672ff6c56cb_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 72},
    {/*f*/ stage_alpha_101_stream__13a8169589968006c, /*dependers*/ stage_alpha_101_stream__13a8169589968006c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_13a8169589968006c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_13a8169589968006c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 73},
    {/*f*/ stage_alpha_101_stream__49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c, /*dependers*/ stage_alpha_101_stream__49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_dep, /*num_dependers*/ 11,
     /*in_buffers*/ stage_49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_in_buf, /*num_in_buffers*/ 9,
     /*out_buffers*/ stage_49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_out_buf, /*num_out_buffers*/ 12, /*pending_out*/ 3,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 74},
    {/*f*/ stage_alpha_101_stream__96cdc5fc00ecf5ae, /*dependers*/ stage_alpha_101_stream__96cdc5fc00ecf5ae_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_96cdc5fc00ecf5ae_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_96cdc5fc00ecf5ae_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 75},
    {/*f*/ stage_alpha_101_stream__507411a9986abb68, /*dependers*/ stage_alpha_101_stream__507411a9986abb68_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_507411a9986abb68_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_507411a9986abb68_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 76},
    {/*f*/ stage_alpha_101_stream__alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98, /*dependers*/ stage_alpha_101_stream__alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_dep, /*num_dependers*/ 3,
     /*in_buffers*/ stage_alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_in_buf, /*num_in_buffers*/ 15,
     /*out_buffers*/ stage_alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_out_buf, /*num_out_buffers*/ 11, /*pending_out*/ 13,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 77},
    {/*f*/ stage_alpha_101_stream__52b6ff208174d149, /*dependers*/ stage_alpha_101_stream__52b6ff208174d149_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_52b6ff208174d149_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_52b6ff208174d149_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 78},
    {/*f*/ stage_alpha_101_stream__11c9b76e737d91c1b, /*dependers*/ stage_alpha_101_stream__11c9b76e737d91c1b_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_11c9b76e737d91c1b_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_11c9b76e737d91c1b_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 79},
    {/*f*/ stage_alpha_101_stream__1e5ad191dd30293be, /*dependers*/ stage_alpha_101_stream__1e5ad191dd30293be_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1e5ad191dd30293be_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1e5ad191dd30293be_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 80},
    {/*f*/ stage_alpha_101_stream__1d08e7dc84e257dab, /*dependers*/ stage_alpha_101_stream__1d08e7dc84e257dab_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1d08e7dc84e257dab_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1d08e7dc84e257dab_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 81},
    {/*f*/ stage_alpha_101_stream__eb6ce72b67e4317e, /*dependers*/ stage_alpha_101_stream__eb6ce72b67e4317e_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_eb6ce72b67e4317e_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_eb6ce72b67e4317e_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 82},
    {/*f*/ stage_alpha_101_stream__e07e2753279851fe, /*dependers*/ stage_alpha_101_stream__e07e2753279851fe_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_e07e2753279851fe_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_e07e2753279851fe_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 83},
    {/*f*/ stage_alpha_101_stream__226e640ee8e31e3d, /*dependers*/ stage_alpha_101_stream__226e640ee8e31e3d_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_226e640ee8e31e3d_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_226e640ee8e31e3d_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 84},
    {/*f*/ stage_alpha_101_stream__e05c149cd44339a3, /*dependers*/ stage_alpha_101_stream__e05c149cd44339a3_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_e05c149cd44339a3_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_e05c149cd44339a3_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 85},
    {/*f*/ stage_alpha_101_stream__5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d, /*dependers*/ stage_alpha_101_stream__5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_dep, /*num_dependers*/ 11,
     /*in_buffers*/ stage_5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_in_buf, /*num_in_buffers*/ 19,
     /*out_buffers*/ stage_5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_out_buf, /*num_out_buffers*/ 15, /*pending_out*/ 7,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 86},
    {/*f*/ stage_alpha_101_stream__186e7dd3a2080800a, /*dependers*/ stage_alpha_101_stream__186e7dd3a2080800a_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_186e7dd3a2080800a_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_186e7dd3a2080800a_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 87},
    {/*f*/ stage_alpha_101_stream__85cedbf1ff9283ca, /*dependers*/ stage_alpha_101_stream__85cedbf1ff9283ca_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_85cedbf1ff9283ca_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_85cedbf1ff9283ca_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 88},
    {/*f*/ stage_alpha_101_stream__9fda089d0982e762, /*dependers*/ stage_alpha_101_stream__9fda089d0982e762_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_9fda089d0982e762_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_9fda089d0982e762_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 89},
    {/*f*/ stage_alpha_101_stream__1cbce9dc8fd160ab0, /*dependers*/ stage_alpha_101_stream__1cbce9dc8fd160ab0_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1cbce9dc8fd160ab0_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1cbce9dc8fd160ab0_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 90},
    {/*f*/ stage_alpha_101_stream__1acaac361d6d31bdf, /*dependers*/ stage_alpha_101_stream__1acaac361d6d31bdf_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1acaac361d6d31bdf_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1acaac361d6d31bdf_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 91},
    {/*f*/ stage_alpha_101_stream__alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3, /*dependers*/ stage_alpha_101_stream__alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_dep, /*num_dependers*/ 7,
     /*in_buffers*/ stage_alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_in_buf, /*num_in_buffers*/ 18,
     /*out_buffers*/ stage_alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_out_buf, /*num_out_buffers*/ 24, /*pending_out*/ 13,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 92},
    {/*f*/ stage_alpha_101_stream__1cfbffed3e23f4455, /*dependers*/ stage_alpha_101_stream__1cfbffed3e23f4455_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1cfbffed3e23f4455_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1cfbffed3e23f4455_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 93},
    {/*f*/ stage_alpha_101_stream__1eb68af50dcd04d42, /*dependers*/ stage_alpha_101_stream__1eb68af50dcd04d42_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1eb68af50dcd04d42_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1eb68af50dcd04d42_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 94},
    {/*f*/ stage_alpha_101_stream__1722e93023ecc26d8, /*dependers*/ stage_alpha_101_stream__1722e93023ecc26d8_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1722e93023ecc26d8_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1722e93023ecc26d8_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 95},
    {/*f*/ stage_alpha_101_stream__593d8be97d887c4e, /*dependers*/ stage_alpha_101_stream__593d8be97d887c4e_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_593d8be97d887c4e_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_593d8be97d887c4e_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 96},
    {/*f*/ stage_alpha_101_stream__18c68193caf707703, /*dependers*/ stage_alpha_101_stream__18c68193caf707703_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_18c68193caf707703_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_18c68193caf707703_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 97},
    {/*f*/ stage_alpha_101_stream__alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096, /*dependers*/ stage_alpha_101_stream__alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_dep, /*num_dependers*/ 4,
     /*in_buffers*/ stage_alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_in_buf, /*num_in_buffers*/ 17,
     /*out_buffers*/ stage_alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 6,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 98},
    {/*f*/ stage_alpha_101_stream__19e3239463a2eeb42, /*dependers*/ stage_alpha_101_stream__19e3239463a2eeb42_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_19e3239463a2eeb42_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_19e3239463a2eeb42_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 99},
    {/*f*/ stage_alpha_101_stream__1502ee23db6b90d7c, /*dependers*/ stage_alpha_101_stream__1502ee23db6b90d7c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1502ee23db6b90d7c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1502ee23db6b90d7c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 100},
    {/*f*/ stage_alpha_101_stream__1c760288c6fcebd4, /*dependers*/ stage_alpha_101_stream__1c760288c6fcebd4_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1c760288c6fcebd4_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1c760288c6fcebd4_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 101},
    {/*f*/ stage_alpha_101_stream__alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6, /*dependers*/ stage_alpha_101_stream__alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_dep, /*num_dependers*/ 4,
     /*in_buffers*/ stage_alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_in_buf, /*num_in_buffers*/ 17,
     /*out_buffers*/ stage_alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 11,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 102},
    {/*f*/ stage_alpha_101_stream__alpha028, /*dependers*/ stage_alpha_101_stream__alpha028_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha028_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_alpha028_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 103},
    {/*f*/ stage_alpha_101_stream__180ac7118f5030ca1, /*dependers*/ stage_alpha_101_stream__180ac7118f5030ca1_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_180ac7118f5030ca1_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_180ac7118f5030ca1_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 104},
    {/*f*/ stage_alpha_101_stream__c13f2073feaded25, /*dependers*/ stage_alpha_101_stream__c13f2073feaded25_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_c13f2073feaded25_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_c13f2073feaded25_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 105},
    {/*f*/ stage_alpha_101_stream__132137a7499bdd6c5, /*dependers*/ stage_alpha_101_stream__132137a7499bdd6c5_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_132137a7499bdd6c5_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_132137a7499bdd6c5_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 106},
    {/*f*/ stage_alpha_101_stream__alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2, /*dependers*/ stage_alpha_101_stream__alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_dep, /*num_dependers*/ 4,
     /*in_buffers*/ stage_alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_in_buf, /*num_in_buffers*/ 22,
     /*out_buffers*/ stage_alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_out_buf, /*num_out_buffers*/ 10, /*pending_out*/ 19,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 107},
    {/*f*/ stage_alpha_101_stream__17852424e0c297f0e, /*dependers*/ stage_alpha_101_stream__17852424e0c297f0e_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_17852424e0c297f0e_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_17852424e0c297f0e_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 108},
    {/*f*/ stage_alpha_101_stream__alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032, /*dependers*/ stage_alpha_101_stream__alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_in_buf, /*num_in_buffers*/ 18,
     /*out_buffers*/ stage_alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 17,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 109},
    {/*f*/ stage_alpha_101_stream__1134198af3a9d5546, /*dependers*/ stage_alpha_101_stream__1134198af3a9d5546_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1134198af3a9d5546_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1134198af3a9d5546_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 110},
    {/*f*/ stage_alpha_101_stream__alpha025, /*dependers*/ stage_alpha_101_stream__alpha025_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha025_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_alpha025_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 111},
    {/*f*/ stage_alpha_101_stream__d7e8c3e6f7f6a559, /*dependers*/ stage_alpha_101_stream__d7e8c3e6f7f6a559_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_d7e8c3e6f7f6a559_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_d7e8c3e6f7f6a559_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 112},
    {/*f*/ stage_alpha_101_stream__13197e06412cf906c, /*dependers*/ stage_alpha_101_stream__13197e06412cf906c_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_13197e06412cf906c_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_13197e06412cf906c_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 113},
    {/*f*/ stage_alpha_101_stream__c223205aa1dfceba, /*dependers*/ stage_alpha_101_stream__c223205aa1dfceba_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_c223205aa1dfceba_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_c223205aa1dfceba_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 114},
    {/*f*/ stage_alpha_101_stream__9361c0593542cca8, /*dependers*/ stage_alpha_101_stream__9361c0593542cca8_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_9361c0593542cca8_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_9361c0593542cca8_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 115},
    {/*f*/ stage_alpha_101_stream__alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf, /*dependers*/ stage_alpha_101_stream__alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_dep, /*num_dependers*/ 7,
     /*in_buffers*/ stage_alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_in_buf, /*num_in_buffers*/ 15,
     /*out_buffers*/ stage_alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 7,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 116},
    {/*f*/ stage_alpha_101_stream__0c3587e4cc9fb312, /*dependers*/ stage_alpha_101_stream__0c3587e4cc9fb312_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_0c3587e4cc9fb312_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_0c3587e4cc9fb312_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 117},
    {/*f*/ stage_alpha_101_stream__11edbf4250d427421, /*dependers*/ stage_alpha_101_stream__11edbf4250d427421_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_11edbf4250d427421_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_11edbf4250d427421_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 118},
    {/*f*/ stage_alpha_101_stream__1ccb8076ab4602658, /*dependers*/ stage_alpha_101_stream__1ccb8076ab4602658_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1ccb8076ab4602658_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1ccb8076ab4602658_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 119},
    {/*f*/ stage_alpha_101_stream__6df3b3f2c4401862, /*dependers*/ stage_alpha_101_stream__6df3b3f2c4401862_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_6df3b3f2c4401862_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_6df3b3f2c4401862_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 120},
    {/*f*/ stage_alpha_101_stream__alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037, /*dependers*/ stage_alpha_101_stream__alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_dep, /*num_dependers*/ 3,
     /*in_buffers*/ stage_alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_in_buf, /*num_in_buffers*/ 19,
     /*out_buffers*/ stage_alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 14,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 121},
    {/*f*/ stage_alpha_101_stream__d1aea8e58de31174, /*dependers*/ stage_alpha_101_stream__d1aea8e58de31174_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_d1aea8e58de31174_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_d1aea8e58de31174_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 122},
    {/*f*/ stage_alpha_101_stream__1d85621e17bd8a9d6, /*dependers*/ stage_alpha_101_stream__1d85621e17bd8a9d6_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1d85621e17bd8a9d6_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1d85621e17bd8a9d6_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 123},
    {/*f*/ stage_alpha_101_stream__16c47e4b623012393, /*dependers*/ stage_alpha_101_stream__16c47e4b623012393_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_16c47e4b623012393_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_16c47e4b623012393_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 124},
    {/*f*/ stage_alpha_101_stream__1fecb90f5ea6b7a42, /*dependers*/ stage_alpha_101_stream__1fecb90f5ea6b7a42_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_1fecb90f5ea6b7a42_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_1fecb90f5ea6b7a42_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 125},
    {/*f*/ stage_alpha_101_stream__alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043, /*dependers*/ stage_alpha_101_stream__alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_dep, /*num_dependers*/ 4,
     /*in_buffers*/ stage_alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_in_buf, /*num_in_buffers*/ 19,
     /*out_buffers*/ stage_alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 10,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 126},
    {/*f*/ stage_alpha_101_stream__alpha034, /*dependers*/ stage_alpha_101_stream__alpha034_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha034_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_alpha034_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 127},
    {/*f*/ stage_alpha_101_stream__alpha001, /*dependers*/ stage_alpha_101_stream__alpha001_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha001_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_alpha001_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 128},
    {/*f*/ stage_alpha_101_stream__alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054, /*dependers*/ stage_alpha_101_stream__alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_dep, /*num_dependers*/ 2,
     /*in_buffers*/ stage_alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_in_buf, /*num_in_buffers*/ 17,
     /*out_buffers*/ stage_alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_out_buf, /*num_out_buffers*/ 9, /*pending_out*/ 13,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 129},
    {/*f*/ stage_alpha_101_stream__104b7bca3b25ad8b3, /*dependers*/ stage_alpha_101_stream__104b7bca3b25ad8b3_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_104b7bca3b25ad8b3_in_buf, /*num_in_buffers*/ 1,
     /*out_buffers*/ stage_104b7bca3b25ad8b3_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 1,
     /*num_tasks*/ TaskExecKind::SLICE_BY_TIME, /*id*/ 130},
    {/*f*/ stage_alpha_101_stream__alpha040_1668c43c265a85276, /*dependers*/ stage_alpha_101_stream__alpha040_1668c43c265a85276_dep, /*num_dependers*/ 1,
     /*in_buffers*/ stage_alpha040_1668c43c265a85276_in_buf, /*num_in_buffers*/ 6,
     /*out_buffers*/ stage_alpha040_1668c43c265a85276_out_buf, /*num_out_buffers*/ 2, /*pending_out*/ 4,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 131},
    {/*f*/ stage_alpha_101_stream__alpha062, /*dependers*/ stage_alpha_101_stream__alpha062_dep, /*num_dependers*/ 0,
     /*in_buffers*/ stage_alpha062_in_buf, /*num_in_buffers*/ 2,
     /*out_buffers*/ stage_alpha062_out_buf, /*num_out_buffers*/ 1, /*pending_out*/ 2,
     /*num_tasks*/ TaskExecKind::SLICE_BY_STOCK, /*id*/ 132}
};

namespace {
Stage *stage_alpha_101_stream__1f0f01f1c83584d6f_dep[] = {&__stages_alpha_101_stream[11], &__stages_alpha_101_stream[92], &__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__19cc09703572f7b58_dep[] = {&__stages_alpha_101_stream[77], &__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__120d49c2693eb0b8f_dep[] = {&__stages_alpha_101_stream[92], &__stages_alpha_101_stream[107], &__stages_alpha_101_stream[131]};
Stage *stage_alpha_101_stream__17c512b9c71d3d940_dep[] = {&__stages_alpha_101_stream[131]};
Stage *stage_alpha_101_stream__1f769b559feed1b47_dep[] = {&__stages_alpha_101_stream[53]};
Stage *stage_alpha_101_stream__1cb80bd72b1338485_dep[] = {&__stages_alpha_101_stream[131]};
Stage *stage_alpha_101_stream__17237c9420bba7634_dep[] = {&__stages_alpha_101_stream[92], &__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__60b7aa4774c90f7a_dep[] = {&__stages_alpha_101_stream[92], &__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__16123614810853bf0_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__39cd69e7f6d85328_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__1cac888714ed9f6a4_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__18fdad286b28e4b7b_alpha023_1d5bb7cf49a1cb4a0_10c16d9a20e0387b3_alpha006_156e62fbc370dca38_14969b303c48d510f_10156c5755f433df0_16e18489215dc7531_f4055f7b15338dfe_160b80f2567e1055c_161e738cf1eba2c95_e2c0a8e1c6ba083a_82b65763b5b2c7e4_11883856b1555ef59_1eda8e378b6c06d1a_14a4506c73ffa04a3_1a81d3f6084e29607_93dc794f9443fc2e_19b7389e0c453d803_2c53c8eaac8635f5_15d5de9a420044862_00adb6936a973911_19f751d58c7a6b64_1d97c2770b5a6165f_1ba3ef6f8151bb423_dep[] = {&__stages_alpha_101_stream[3], &__stages_alpha_101_stream[4], &__stages_alpha_101_stream[5], &__stages_alpha_101_stream[8], &__stages_alpha_101_stream[9], &__stages_alpha_101_stream[10], &__stages_alpha_101_stream[12], &__stages_alpha_101_stream[19], &__stages_alpha_101_stream[28], &__stages_alpha_101_stream[45], &__stages_alpha_101_stream[64], &__stages_alpha_101_stream[86], &__stages_alpha_101_stream[116], &__stages_alpha_101_stream[121], &__stages_alpha_101_stream[126], &__stages_alpha_101_stream[129], &__stages_alpha_101_stream[131]};
Stage *stage_alpha_101_stream__2edba89328237aeb_dep[] = {&__stages_alpha_101_stream[19]};
Stage *stage_alpha_101_stream__ade4363da9807ef1_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__156ece8c9e5c0c4da_dep[] = {&__stages_alpha_101_stream[53]};
Stage *stage_alpha_101_stream__1c1ae2fca6456a911_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__11b9e2ab19c5cf07f_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__54a7c757d1b2deb5_dep[] = {&__stages_alpha_101_stream[28], &__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__0f32950657c256a9_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__alpha019_be958f2e6e459f60_14ab5e930c67d541b_9d962035ba5f1ea4_077b734a024fd813_alpha012_106499afd0abdce0c_16fd514be19f48a38_16aad41bb0d085fc8_alpha007_alpha021_alpha010_alpha046_alpha049_alpha051_alpha009_eca4f6b3acf43b21_118ca723e22053d82_bf8995d8792205e7_f4bf06972d9be22e_a16e007555996c6e_b1ef8918ee2f42d9_2b4119829ffbe8ca_68d8962d72605382_1da3294008ba11157_1b968497d4dea7e45_1821f4158341eaadd_1dcccc562c26e18cc_78d7937a0020cd3f_e2149fed4461a684_dep[] = {&__stages_alpha_101_stream[13], &__stages_alpha_101_stream[14], &__stages_alpha_101_stream[15], &__stages_alpha_101_stream[16], &__stages_alpha_101_stream[17], &__stages_alpha_101_stream[18], &__stages_alpha_101_stream[20], &__stages_alpha_101_stream[28], &__stages_alpha_101_stream[45], &__stages_alpha_101_stream[53], &__stages_alpha_101_stream[116], &__stages_alpha_101_stream[126], &__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__26a1c5dfd27db7bb_dep[] = {&__stages_alpha_101_stream[28]};
Stage *stage_alpha_101_stream__1bec543c83b13b08c_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__1bcc1d690ec58df7f_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__835a27cc9bdeb830_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__12e33a12fe6cb8a9_dep[] = {&__stages_alpha_101_stream[37]};
Stage *stage_alpha_101_stream__50636661b8037409_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__84f75b98293a92f1_dep[] = {&__stages_alpha_101_stream[37]};
Stage *stage_alpha_101_stream__16f2a300b18e43c49_dep[] = {&__stages_alpha_101_stream[37]};
Stage *stage_alpha_101_stream__493c5c7c2e79a37f_e0e3079c4a901fa8_10bf2e96da60b4371_1f3d21de30288a87_14aa873133ad7c9f4_fa408c94db08823f_alpha084_ae449937b099b289_54c72aeffc3e3508_7bba8c2207236de9_3eda254c3f952022_1b087cce3aa0b937a_1dc772caca00e1584_cd0c872219e5d6d0_89deb5a8070c6bf4_82dce2338e7ee89f_1b38965bcd2746ceb_c062f9bb82def253_4f68c121edd4b69a_18644381e8646210a_12db32b8edf3abd9c_114be7640278a0027_a998c0027e40f4c6_662df1dc40577e1e_90a31d273592c40c_e8ef8c4a12962781_75e672355a4f049c_1ac920ea885d1ed2b_16599f4652129dcaa_6722cd923f64335c_196f4b404382c7357_dep[] = {&__stages_alpha_101_stream[21], &__stages_alpha_101_stream[22], &__stages_alpha_101_stream[23], &__stages_alpha_101_stream[24], &__stages_alpha_101_stream[25], &__stages_alpha_101_stream[26], &__stages_alpha_101_stream[27], &__stages_alpha_101_stream[29], &__stages_alpha_101_stream[37], &__stages_alpha_101_stream[45], &__stages_alpha_101_stream[53], &__stages_alpha_101_stream[64], &__stages_alpha_101_stream[74], &__stages_alpha_101_stream[77], &__stages_alpha_101_stream[86], &__stages_alpha_101_stream[92], &__stages_alpha_101_stream[102], &__stages_alpha_101_stream[107], &__stages_alpha_101_stream[116], &__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__b8d3ad670e02e188_dep[] = {&__stages_alpha_101_stream[116]};
Stage *stage_alpha_101_stream__42fef27a0c3f65f3_dep[] = {&__stages_alpha_101_stream[116]};
Stage *stage_alpha_101_stream__17186aeb7dd951aaf_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__152b4b6c739dec9b7_dep[] = {&__stages_alpha_101_stream[45]};
Stage *stage_alpha_101_stream__8f66f09e9d504a8c_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__1eae54ac4d54608f9_dep[] = {&__stages_alpha_101_stream[45]};
Stage *stage_alpha_101_stream__909092e48dafead6_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__1d2731cd6670fbbb9_dep[] = {&__stages_alpha_101_stream[53]};
Stage *stage_alpha_101_stream__alpha086_4599ae1c53681419_d6c2cd9d0a42eb53_122a8870451b838e7_19bcab12c0b751190_e5741f7564c392ac_350c605085b2027e_1f2d443115bbe19b8_122a8a80286874cc8_1209b15d3b8ee8a84_9dd1ca3b1e0306d5_dep[] = {&__stages_alpha_101_stream[30], &__stages_alpha_101_stream[31], &__stages_alpha_101_stream[32], &__stages_alpha_101_stream[33], &__stages_alpha_101_stream[34], &__stages_alpha_101_stream[35], &__stages_alpha_101_stream[36], &__stages_alpha_101_stream[38], &__stages_alpha_101_stream[45], &__stages_alpha_101_stream[53], &__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__1a9308070e053bb70_dep[] = {&__stages_alpha_101_stream[45]};
Stage *stage_alpha_101_stream__869f38882be45286_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__b80797652a77217e_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__14ce42d338ddcd7bc_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__b096a6b338e59bd1_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__62f1b56c1f91b803_dep[] = {&__stages_alpha_101_stream[53]};
Stage *stage_alpha_101_stream__alpha042_a9c228e0b9dafee8_a9c228c71b191482_e28b6a66388aca96_18e6ff5693a16658f_92a82f927e12be36_106498830afb12b2f_alpha024_db1ec42f638754c4_19448648e73a0e59a_46fde329d2fadda4_dep[] = {&__stages_alpha_101_stream[39], &__stages_alpha_101_stream[40], &__stages_alpha_101_stream[41], &__stages_alpha_101_stream[42], &__stages_alpha_101_stream[43], &__stages_alpha_101_stream[44], &__stages_alpha_101_stream[46], &__stages_alpha_101_stream[109], &__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__1ad82bd2893556b95_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__400531d71d423d49_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__6d2930339face684_dep[] = {&__stages_alpha_101_stream[49]};
Stage *stage_alpha_101_stream__93385477fefd8c17_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__148e235db294e7525_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__c8a42a3708a91898_dep[] = {&__stages_alpha_101_stream[64]};
Stage *stage_alpha_101_stream__7d709a995a29d77f_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__f6f97214486a2cc1_alpha053_17feca80f99bb159b_alpha101_c484102cd55ad1b1_53d006c904f6f37f_alpha022_817836889c535887_a7019f9a5c963187_d46a09aff428b211_1af234ead49ceac5a_1163db3251b923ec7_1015175dbdfa9818a_1c125ece16202104b_34585a48fb808f83_ab239b5b61f82d07_dep[] = {&__stages_alpha_101_stream[47], &__stages_alpha_101_stream[48], &__stages_alpha_101_stream[50], &__stages_alpha_101_stream[51], &__stages_alpha_101_stream[52], &__stages_alpha_101_stream[54], &__stages_alpha_101_stream[64], &__stages_alpha_101_stream[86], &__stages_alpha_101_stream[92], &__stages_alpha_101_stream[107], &__stages_alpha_101_stream[109], &__stages_alpha_101_stream[116]};
Stage *stage_alpha_101_stream__6f87169c15697aec_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__e714ed386b6c8ec3_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__1a4e2f3ba89f42951_dep[] = {&__stages_alpha_101_stream[57]};
Stage *stage_alpha_101_stream__17230fa8d70cb536c_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__12d3f9faf4df7c584_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__1e7f2de38b65af18f_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__78e4f6e7cee909a8_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__381bba73519aac77_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__1555fc2ac5b1f47bd_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__a88237a38c9d018f_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__1fb795dcf1c9ec054_07afa02613eebb6d_197046de60901aa41_1a12cbc7ebe63cfcc_1457ce2c155a61fd4_76fe21605013c9a7_20e24891e9d62d6b_e88f9cd0d1d87daa_718b6e819250274f_149416df8d2c4dac_1d423b99fe89ce6c8_ac8b438bbe1cf240_ca86c5db4dae4c75_1d4580520b32d9d74_c7f03cfe4a9a0496_11ba1c0582ed7958d_7e2c3205f536c762_e083a5768021835f_168fddb21a03fc027_15c36dacf75e527a0_1f724efa04733d837_16ba41787258d0ac2_711b2797a20851f9_1d0d5bd22659e1cc3_13990b65aba0b7628_dep[] = {&__stages_alpha_101_stream[55], &__stages_alpha_101_stream[56], &__stages_alpha_101_stream[58], &__stages_alpha_101_stream[59], &__stages_alpha_101_stream[60], &__stages_alpha_101_stream[61], &__stages_alpha_101_stream[62], &__stages_alpha_101_stream[63], &__stages_alpha_101_stream[65], &__stages_alpha_101_stream[74], &__stages_alpha_101_stream[77], &__stages_alpha_101_stream[86], &__stages_alpha_101_stream[102], &__stages_alpha_101_stream[107], &__stages_alpha_101_stream[121], &__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__1d4b7681a80713990_dep[] = {&__stages_alpha_101_stream[74]};
Stage *stage_alpha_101_stream__1a231b2e05e8601d3_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__db7aef550751a1cd_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__4b88f8befc88e458_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__562cec0cb33847c8_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__15b2fff9c11057958_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__1500a66d04da7a0f4_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__d45ec672ff6c56cb_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__13a8169589968006c_dep[] = {&__stages_alpha_101_stream[77]};
Stage *stage_alpha_101_stream__49be5d9c39d10d72_351f7bdaf8ced412_0189b1479ac96a49_1d55ca7c2bb592f6b_1bf81dfeb065cab7e_d782b67ea7f219d8_13ceb33893cac5614_1637895cfc77c386d_1cf1f6a8d90ea2e78_14c03c015491e6f07_1859886e85061c544_123e2c4ec6491301c_dep[] = {&__stages_alpha_101_stream[66], &__stages_alpha_101_stream[67], &__stages_alpha_101_stream[68], &__stages_alpha_101_stream[69], &__stages_alpha_101_stream[70], &__stages_alpha_101_stream[71], &__stages_alpha_101_stream[72], &__stages_alpha_101_stream[73], &__stages_alpha_101_stream[75], &__stages_alpha_101_stream[77], &__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__96cdc5fc00ecf5ae_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__507411a9986abb68_dep[] = {&__stages_alpha_101_stream[86]};
Stage *stage_alpha_101_stream__alpha095_alpha065_alpha002_alpha066_alpha038_ad74e3f90fab1c5b_alpha004_alpha092_cd7e62b0652a2be4_1f45d88fcb5643470_1b87bba46739d9a98_dep[] = {&__stages_alpha_101_stream[76], &__stages_alpha_101_stream[78], &__stages_alpha_101_stream[86]};
Stage *stage_alpha_101_stream__52b6ff208174d149_dep[] = {&__stages_alpha_101_stream[86]};
Stage *stage_alpha_101_stream__11c9b76e737d91c1b_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__1e5ad191dd30293be_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__1d08e7dc84e257dab_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__eb6ce72b67e4317e_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__e07e2753279851fe_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__226e640ee8e31e3d_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__e05c149cd44339a3_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__5bb02c19c3dd6729_1107e617187eb74dc_1a07b0431ada8801a_17428b32875256d19_115ae56c1acc5da93_alpha026_c6a5b1cc4b64300a_15264260244c74405_cbfb1d0ffb904289_12c2c23e763e94135_fc74c311397bb045_16ebd20a45d3e92fe_165fc323207ecacad_64394211e6651f69_3af437316f85598d_dep[] = {&__stages_alpha_101_stream[79], &__stages_alpha_101_stream[80], &__stages_alpha_101_stream[81], &__stages_alpha_101_stream[82], &__stages_alpha_101_stream[83], &__stages_alpha_101_stream[84], &__stages_alpha_101_stream[85], &__stages_alpha_101_stream[87], &__stages_alpha_101_stream[92], &__stages_alpha_101_stream[98], &__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__186e7dd3a2080800a_dep[] = {&__stages_alpha_101_stream[92]};
Stage *stage_alpha_101_stream__85cedbf1ff9283ca_dep[] = {&__stages_alpha_101_stream[98]};
Stage *stage_alpha_101_stream__9fda089d0982e762_dep[] = {&__stages_alpha_101_stream[98]};
Stage *stage_alpha_101_stream__1cbce9dc8fd160ab0_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__1acaac361d6d31bdf_dep[] = {&__stages_alpha_101_stream[98]};
Stage *stage_alpha_101_stream__alpha094_alpha052_e0868dec9669359f_alpha055_fc30d8436cd2e6f0_alpha003_15d7d8ba49746a937_1fa7018abf0f3a453_alpha068_17bd17f7435f12bee_1693aa3aaeb129a60_dad364f59a26ac50_dfe1845a1a8fb41d_164447fd516042841_10bf852bbded84010_140f4347a2e0bf204_cfbdcaaf4ede80b1_1406245cbf0a5c46f_137906673daae4438_17832f3ff2a94f427_1d43b462e5d69ba5d_2b4deadd228e75a9_13f9e1b099bf55621_95b7f4bc0dee6ab3_dep[] = {&__stages_alpha_101_stream[88], &__stages_alpha_101_stream[89], &__stages_alpha_101_stream[90], &__stages_alpha_101_stream[91], &__stages_alpha_101_stream[93], &__stages_alpha_101_stream[98], &__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__1cfbffed3e23f4455_dep[] = {&__stages_alpha_101_stream[98]};
Stage *stage_alpha_101_stream__1eb68af50dcd04d42_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__1722e93023ecc26d8_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__593d8be97d887c4e_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__18c68193caf707703_dep[] = {&__stages_alpha_101_stream[102]};
Stage *stage_alpha_101_stream__alpha016_alpha050_1494da145191886fa_alpha013_1c983e545d91c9866_0785974bf2709530_aaf0b20fc619bbb2_alpha044_alpha096_dep[] = {&__stages_alpha_101_stream[94], &__stages_alpha_101_stream[95], &__stages_alpha_101_stream[96], &__stages_alpha_101_stream[97]};
Stage *stage_alpha_101_stream__19e3239463a2eeb42_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__1502ee23db6b90d7c_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__1c760288c6fcebd4_dep[] = {&__stages_alpha_101_stream[107]};
Stage *stage_alpha_101_stream__alpha074_alpha027_alpha015_577d72745758f4ff_alpha078_alpha072_3c0324e6b050f678_1c1e02ad578fee8b1_9df22aa19187c2f6_dep[] = {&__stages_alpha_101_stream[99], &__stages_alpha_101_stream[100], &__stages_alpha_101_stream[101], &__stages_alpha_101_stream[103]};
Stage *stage_alpha_101_stream__180ac7118f5030ca1_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__c13f2073feaded25_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__132137a7499bdd6c5_dep[] = {&__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__alpha099_alpha077_alpha098_1082a9d10e478141d_1e5e0d408d12d9522_alpha071_alpha085_30778bd3fe6f9ee4_alpha083_dcfa7dc0411b83e2_dep[] = {&__stages_alpha_101_stream[104], &__stages_alpha_101_stream[105], &__stages_alpha_101_stream[106], &__stages_alpha_101_stream[109]};
Stage *stage_alpha_101_stream__17852424e0c297f0e_dep[] = {&__stages_alpha_101_stream[116]};
Stage *stage_alpha_101_stream__alpha064_1856762a1003c5303_alpha088_alpha060_alpha020_alpha005_alpha011_alpha057_alpha032_dep[] = {&__stages_alpha_101_stream[108]};
Stage *stage_alpha_101_stream__1134198af3a9d5546_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__d7e8c3e6f7f6a559_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__13197e06412cf906c_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__c223205aa1dfceba_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__9361c0593542cca8_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__alpha029_bc280680bb3448f5_1a409bd1b3fb41908_alpha031_e5cadef0dfe4e951_1e2fe07181b3a06db_1f1a2321d133ec98c_3cef9bf07276a7bd_ac3647e8d5b400bf_dep[] = {&__stages_alpha_101_stream[110], &__stages_alpha_101_stream[111], &__stages_alpha_101_stream[112], &__stages_alpha_101_stream[113], &__stages_alpha_101_stream[114], &__stages_alpha_101_stream[115], &__stages_alpha_101_stream[117]};
Stage *stage_alpha_101_stream__0c3587e4cc9fb312_dep[] = {&__stages_alpha_101_stream[121]};
Stage *stage_alpha_101_stream__11edbf4250d427421_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__1ccb8076ab4602658_dep[] = {&__stages_alpha_101_stream[132]};
Stage *stage_alpha_101_stream__6df3b3f2c4401862_dep[] = {&__stages_alpha_101_stream[126]};
Stage *stage_alpha_101_stream__alpha075_alpha073_alpha047_ad098f0979977549_alpha036_4ee2dea1b7e3190e_11eb8919f2723bf62_alpha041_alpha037_dep[] = {&__stages_alpha_101_stream[118], &__stages_alpha_101_stream[119], &__stages_alpha_101_stream[120]};
Stage *stage_alpha_101_stream__d1aea8e58de31174_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__1d85621e17bd8a9d6_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__16c47e4b623012393_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__1fecb90f5ea6b7a42_dep[] = {&__stages_alpha_101_stream[129]};
Stage *stage_alpha_101_stream__alpha081_alpha061_1f6a94fd48767fcd5_alpha030_48714155402cad1d_alpha045_0723b75820a79324_009da94a34e00581_alpha043_dep[] = {&__stages_alpha_101_stream[122], &__stages_alpha_101_stream[123], &__stages_alpha_101_stream[124], &__stages_alpha_101_stream[125]};
Stage *stage_alpha_101_stream__alpha039_alpha017_alpha018_9b92ce11ac49056c_alpha008_alpha014_alpha035_1295929018b7108e8_alpha054_dep[] = {&__stages_alpha_101_stream[127], &__stages_alpha_101_stream[128]};
Stage *stage_alpha_101_stream__104b7bca3b25ad8b3_dep[] = {&__stages_alpha_101_stream[132]};
Stage *stage_alpha_101_stream__alpha040_1668c43c265a85276_dep[] = {&__stages_alpha_101_stream[130]};
}


KUN_EXPORT Module alpha_101_stream{
    0x64100003,
    133,
    __stages_alpha_101_stream,
    495,
    __buffers_alpha_101_stream,
    MemoryLayout::STREAM,
    MemoryLayout::STREAM,
    8,
    Datatype::Float,
    1
};