{"cells": [{"cell_type": "code", "execution_count": 35, "id": "69966ff5", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "import torch.nn.functional as F\n", "import polars as pl\n", "import xarray as xr"]}, {"cell_type": "code", "execution_count": 2, "id": "6f75e359", "metadata": {}, "outputs": [], "source": ["from dataset.spot import SpotKlineDataset\n", "from factor.alpha158 import Alpha158SpotKline\n", "import config"]}, {"cell_type": "code", "execution_count": 3, "id": "d1f76046", "metadata": {}, "outputs": [], "source": ["start_date = \"2024-01-01\"\n", "end_date = \"2025-01-01\""]}, {"cell_type": "code", "execution_count": 4, "id": "bd9a6cef", "metadata": {}, "outputs": [], "source": ["ds = SpotKlineDataset(config.spot_kline_config(start_date, end_date))"]}, {"cell_type": "code", "execution_count": 5, "id": "137e9862", "metadata": {}, "outputs": [], "source": ["factor = Alpha158SpotKline(config.alpha158_config(start_date, end_date))"]}, {"cell_type": "code", "execution_count": 16, "id": "8d346957", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:35:57.671 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'vanilla-dachshund'</span> - Beginning flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'vanilla-dachshund'</span> for flow<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\"> 'get'</span>\n", "</pre>\n"], "text/plain": ["22:35:57.671 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'vanilla-dachshund'\u001b[0m - Beginning flow run\u001b[35m 'vanilla-dachshund'\u001b[0m for flow\u001b[1;35m 'get'\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:36:03.727 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Task run 'read-9eb' - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:36:03.727 | \u001b[36mINFO\u001b[0m    | Task run 'read-9eb' - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:36:03.817 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Task run 'read-136' - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:36:03.817 | \u001b[36mINFO\u001b[0m    | Task run 'read-136' - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:36:03.839 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'vanilla-dachshund'</span> - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:36:03.839 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'vanilla-dachshund'\u001b[0m - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = factor.get()  # type: ignore"]}, {"cell_type": "code", "execution_count": 10, "id": "c9f910ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:35:01.549 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'adamant-bittern'</span> - Beginning flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'adamant-bittern'</span> for flow<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\"> 'get'</span>\n", "</pre>\n"], "text/plain": ["22:35:01.549 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'adamant-bittern'\u001b[0m - Beginning flow run\u001b[35m 'adamant-bittern'\u001b[0m for flow\u001b[1;35m 'get'\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:35:01.587 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Task run 'read-f7f' - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:35:01.587 | \u001b[36mINFO\u001b[0m    | Task run 'read-f7f' - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">22:35:01.617 | <span style=\"color: #008080; text-decoration-color: #008080\">INFO</span>    | Flow run<span style=\"color: #800080; text-decoration-color: #800080\"> 'adamant-bittern'</span> - Finished in state <span style=\"color: #008000; text-decoration-color: #008000\">Completed</span>()\n", "</pre>\n"], "text/plain": ["22:35:01.617 | \u001b[36mINFO\u001b[0m    | Flow run\u001b[35m 'adamant-bittern'\u001b[0m - Finished in state \u001b[32mCompleted\u001b[0m()\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y = ds.get().collect()  # type: ignore"]}, {"cell_type": "code", "execution_count": 11, "id": "f049d317", "metadata": {}, "outputs": [], "source": ["y = y.with_columns(\n", "    pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))\n", "    .then(1)\n", "    .otherwise(0)\n", "    .alias('label')\n", "    .fill_null(0)\n", ")\n", "y = y.rename({'Open time': 'timestamp'})"]}, {"cell_type": "code", "execution_count": 12, "id": "da0d3e48", "metadata": {}, "outputs": [], "source": ["y = y.select(['symbol', 'timestamp', 'label'])"]}, {"cell_type": "code", "execution_count": 17, "id": "410165aa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_689_287, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>symbol</th><th>timestamp</th><th>label</th></tr><tr><td>str</td><td>datetime[μs]</td><td>i32</td></tr></thead><tbody><tr><td>&quot;BNBUSDT&quot;</td><td>2024-01-01 00:00:00</td><td>0</td></tr><tr><td>&quot;BNBUSDT&quot;</td><td>2024-01-01 00:01:00</td><td>1</td></tr><tr><td>&quot;BNBUSDT&quot;</td><td>2024-01-01 00:02:00</td><td>1</td></tr><tr><td>&quot;BNBUSDT&quot;</td><td>2024-01-01 00:03:00</td><td>1</td></tr><tr><td>&quot;BNBUSDT&quot;</td><td>2024-01-01 00:04:00</td><td>1</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;XRPUSDT&quot;</td><td>2024-09-30 23:56:00</td><td>1</td></tr><tr><td>&quot;XRPUSDT&quot;</td><td>2024-09-30 23:57:00</td><td>0</td></tr><tr><td>&quot;XRPUSDT&quot;</td><td>2024-09-30 23:58:00</td><td>1</td></tr><tr><td>&quot;XRPUSDT&quot;</td><td>2024-09-30 23:59:00</td><td>0</td></tr><tr><td>&quot;XRPUSDT&quot;</td><td>2025-01-01 00:00:00</td><td>1</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_689_287, 3)\n", "┌─────────┬─────────────────────┬───────┐\n", "│ symbol  ┆ timestamp           ┆ label │\n", "│ ---     ┆ ---                 ┆ ---   │\n", "│ str     ┆ datetime[μs]        ┆ i32   │\n", "╞═════════╪═════════════════════╪═══════╡\n", "│ BNBUSDT ┆ 2024-01-01 00:00:00 ┆ 0     │\n", "│ BNBUSDT ┆ 2024-01-01 00:01:00 ┆ 1     │\n", "│ BNBUSDT ┆ 2024-01-01 00:02:00 ┆ 1     │\n", "│ BNBUSDT ┆ 2024-01-01 00:03:00 ┆ 1     │\n", "│ BNBUSDT ┆ 2024-01-01 00:04:00 ┆ 1     │\n", "│ …       ┆ …                   ┆ …     │\n", "│ XRPUSDT ┆ 2024-09-30 23:56:00 ┆ 1     │\n", "│ XRPUSDT ┆ 2024-09-30 23:57:00 ┆ 0     │\n", "│ XRPUSDT ┆ 2024-09-30 23:58:00 ┆ 1     │\n", "│ XRPUSDT ┆ 2024-09-30 23:59:00 ┆ 0     │\n", "│ XRPUSDT ┆ 2025-01-01 00:00:00 ┆ 1     │\n", "└─────────┴─────────────────────┴───────┘"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 23, "id": "a8e5d4b9", "metadata": {}, "outputs": [], "source": ["xa = y.to_pandas().set_index(['symbol', 'timestamp']).to_xarray()"]}, {"cell_type": "code", "execution_count": 32, "id": "dc19d487", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 19MB\n", "Dimensions:    (symbol: 7, timestamp: 527041)\n", "Coordinates:\n", "  * symbol     (symbol) object 56B &#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;TRXUSDT&#x27; &#x27;XRPUSDT&#x27;\n", "  * timestamp  (timestamp) datetime64[us] 4MB 2024-01-01 ... 2025-01-01\n", "Data variables:\n", "    label      (symbol, timestamp) int32 15MB 0 1 1 1 1 1 1 0 ... 1 1 0 0 0 0 1</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-8c6170f0-6750-4269-ab7c-5cf6811ce59a' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-8c6170f0-6750-4269-ab7c-5cf6811ce59a' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>symbol</span>: 7</li><li><span class='xr-has-index'>timestamp</span>: 527041</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-e100b70e-d747-40a7-ae00-89be33ae0cff' class='xr-section-summary-in' type='checkbox'  checked><label for='section-e100b70e-d747-40a7-ae00-89be33ae0cff' class='xr-section-summary' >Coordinates: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>symbol</span></div><div class='xr-var-dims'>(symbol)</div><div class='xr-var-dtype'>object</div><div class='xr-var-preview xr-preview'>&#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;XRPUSDT&#x27;</div><input id='attrs-9f4c00c6-f1d6-4fe5-adfc-c74f74e3f89e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9f4c00c6-f1d6-4fe5-adfc-c74f74e3f89e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-85c68695-06c7-4973-b317-42acd52dba08' class='xr-var-data-in' type='checkbox'><label for='data-85c68695-06c7-4973-b317-42acd52dba08' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;], dtype=object)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>timestamp</span></div><div class='xr-var-dims'>(timestamp)</div><div class='xr-var-dtype'>datetime64[us]</div><div class='xr-var-preview xr-preview'>2024-01-01 ... 2025-01-01</div><input id='attrs-2224b487-3cb0-4cf7-b090-4195db8f9989' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2224b487-3cb0-4cf7-b090-4195db8f9989' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-eff7f82f-499b-4862-ba4f-451388d4c469' class='xr-var-data-in' type='checkbox'><label for='data-eff7f82f-499b-4862-ba4f-451388d4c469' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2024-01-01T00:00:00.000000&#x27;, &#x27;2024-01-01T00:01:00.000000&#x27;,\n", "       &#x27;2024-01-01T00:02:00.000000&#x27;, ..., &#x27;2024-12-31T23:58:00.000000&#x27;,\n", "       &#x27;2024-12-31T23:59:00.000000&#x27;, &#x27;2025-01-01T00:00:00.000000&#x27;],\n", "      shape=(527041,), dtype=&#x27;datetime64[us]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-10f53943-645b-4765-8902-11666f3b2f62' class='xr-section-summary-in' type='checkbox'  checked><label for='section-10f53943-645b-4765-8902-11666f3b2f62' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>label</span></div><div class='xr-var-dims'>(symbol, timestamp)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>0 1 1 1 1 1 1 0 ... 0 1 1 0 0 0 0 1</div><input id='attrs-dd5dfbc2-3572-4650-8eff-0e45ec652b73' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-dd5dfbc2-3572-4650-8eff-0e45ec652b73' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-660371ab-799d-46f0-8afa-d5ba5c72e35f' class='xr-var-data-in' type='checkbox'><label for='data-660371ab-799d-46f0-8afa-d5ba5c72e35f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[0, 1, 1, ..., 0, 1, 1],\n", "       [0, 1, 1, ..., 0, 0, 1],\n", "       [0, 1, 0, ..., 1, 0, 1],\n", "       ...,\n", "       [0, 1, 1, ..., 0, 0, 1],\n", "       [0, 1, 0, ..., 0, 1, 1],\n", "       [0, 1, 1, ..., 0, 0, 1]], shape=(7, 527041), dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-fc267d7e-8600-43a5-856b-7cb6181e30a0' class='xr-section-summary-in' type='checkbox'  ><label for='section-fc267d7e-8600-43a5-856b-7cb6181e30a0' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>symbol</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-56824c58-9b49-4f94-a3cb-4cec3b87ed90' class='xr-index-data-in' type='checkbox'/><label for='index-56824c58-9b49-4f94-a3cb-4cec3b87ed90' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;symbol&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>timestamp</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-a4b550f7-133d-4ba1-aa75-ce6784f7c19c' class='xr-index-data-in' type='checkbox'/><label for='index-a4b550f7-133d-4ba1-aa75-ce6784f7c19c' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2024-01-01 00:00:00&#x27;, &#x27;2024-01-01 00:01:00&#x27;,\n", "               &#x27;2024-01-01 00:02:00&#x27;, &#x27;2024-01-01 00:03:00&#x27;,\n", "               &#x27;2024-01-01 00:04:00&#x27;, &#x27;2024-01-01 00:05:00&#x27;,\n", "               &#x27;2024-01-01 00:06:00&#x27;, &#x27;2024-01-01 00:07:00&#x27;,\n", "               &#x27;2024-01-01 00:08:00&#x27;, &#x27;2024-01-01 00:09:00&#x27;,\n", "               ...\n", "               &#x27;2024-12-31 23:51:00&#x27;, &#x27;2024-12-31 23:52:00&#x27;,\n", "               &#x27;2024-12-31 23:53:00&#x27;, &#x27;2024-12-31 23:54:00&#x27;,\n", "               &#x27;2024-12-31 23:55:00&#x27;, &#x27;2024-12-31 23:56:00&#x27;,\n", "               &#x27;2024-12-31 23:57:00&#x27;, &#x27;2024-12-31 23:58:00&#x27;,\n", "               &#x27;2024-12-31 23:59:00&#x27;, &#x27;2025-01-01 00:00:00&#x27;],\n", "              dtype=&#x27;datetime64[us]&#x27;, name=&#x27;timestamp&#x27;, length=527041, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-86eec63c-9d05-4976-aa2a-f12223de2bdd' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-86eec63c-9d05-4976-aa2a-f12223de2bdd' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 19MB\n", "Dimensions:    (symbol: 7, timestamp: 527041)\n", "Coordinates:\n", "  * symbol     (symbol) object 56B 'BNBUSDT' 'BTCUSDT' ... 'TRXUSDT' 'XRPUSDT'\n", "  * timestamp  (timestamp) datetime64[us] 4MB 2024-01-01 ... 2025-01-01\n", "Data variables:\n", "    label      (symbol, timestamp) int32 15MB 0 1 1 1 1 1 1 0 ... 1 1 0 0 0 0 1"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["xa"]}, {"cell_type": "code", "execution_count": 34, "id": "2bbd5650", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 3GB\n", "Dimensions:    (timestamp: 528480, symbol: 7)\n", "Coordinates:\n", "  * timestamp  (timestamp) datetime64[ns] 4MB 2024-01-01 ... 2025-01-01T23:59:00\n", "  * symbol     (symbol) object 56B &#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;TRXUSDT&#x27; &#x27;XRPUSDT&#x27;\n", "Data variables: (12/184)\n", "    KMID       (timestamp, symbol) float32 15MB ...\n", "    KLEN       (timestamp, symbol) float32 15MB ...\n", "    KMID2      (timestamp, symbol) float32 15MB ...\n", "    KUP        (timestamp, symbol) float32 15MB ...\n", "    KUP2       (timestamp, symbol) float32 15MB ...\n", "    KLOW       (timestamp, symbol) float32 15MB ...\n", "    ...         ...\n", "    VSUMN60    (timestamp, symbol) float32 15MB ...\n", "    VSUMD5     (timestamp, symbol) float32 15MB ...\n", "    VSUMD10    (timestamp, symbol) float32 15MB ...\n", "    VSUMD20    (timestamp, symbol) float32 15MB ...\n", "    VSUMD30    (timestamp, symbol) float32 15MB ...\n", "    VSUMD60    (timestamp, symbol) float32 15MB ...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-c3196ef7-180e-406d-8365-04588a498c2e' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-c3196ef7-180e-406d-8365-04588a498c2e' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>timestamp</span>: 528480</li><li><span class='xr-has-index'>symbol</span>: 7</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-02eaf1d3-6d67-4d85-b5bb-5b8230d5bf0b' class='xr-section-summary-in' type='checkbox'  checked><label for='section-02eaf1d3-6d67-4d85-b5bb-5b8230d5bf0b' class='xr-section-summary' >Coordinates: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>timestamp</span></div><div class='xr-var-dims'>(timestamp)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2024-01-01 ... 2025-01-01T23:59:00</div><input id='attrs-0b0e0c9e-1c92-48ac-b305-20ff2bcaf51b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0b0e0c9e-1c92-48ac-b305-20ff2bcaf51b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7c1ad892-b3c6-40b8-9b7d-b2290dc21db7' class='xr-var-data-in' type='checkbox'><label for='data-7c1ad892-b3c6-40b8-9b7d-b2290dc21db7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2024-01-01T00:00:00.000000000&#x27;, &#x27;2024-01-01T00:01:00.000000000&#x27;,\n", "       &#x27;2024-01-01T00:02:00.000000000&#x27;, ..., &#x27;2025-01-01T23:57:00.000000000&#x27;,\n", "       &#x27;2025-01-01T23:58:00.000000000&#x27;, &#x27;2025-01-01T23:59:00.000000000&#x27;],\n", "      shape=(528480,), dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>symbol</span></div><div class='xr-var-dims'>(symbol)</div><div class='xr-var-dtype'>object</div><div class='xr-var-preview xr-preview'>&#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;XRPUSDT&#x27;</div><input id='attrs-1002df43-36bc-4ffd-988d-3df0f6a209c5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1002df43-36bc-4ffd-988d-3df0f6a209c5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-234033d4-3bf4-4f7f-9ddd-de4918548156' class='xr-var-data-in' type='checkbox'><label for='data-234033d4-3bf4-4f7f-9ddd-de4918548156' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;], dtype=object)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-a44503ba-f397-4e2c-bf43-6792cbf74ef0' class='xr-section-summary-in' type='checkbox'  ><label for='section-a44503ba-f397-4e2c-bf43-6792cbf74ef0' class='xr-section-summary' >Data variables: <span>(184)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>KMID</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-000e6185-7405-4535-9c4c-2c68ab3ab67a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-000e6185-7405-4535-9c4c-2c68ab3ab67a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0ea1e4fe-bff8-47ab-8007-965cb3c7f70e' class='xr-var-data-in' type='checkbox'><label for='data-0ea1e4fe-bff8-47ab-8007-965cb3c7f70e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KLEN</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-5ccf3b24-5ac3-4a97-9f61-727c0bb30f9a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5ccf3b24-5ac3-4a97-9f61-727c0bb30f9a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-07db622d-c831-48e8-b162-9b08768e6020' class='xr-var-data-in' type='checkbox'><label for='data-07db622d-c831-48e8-b162-9b08768e6020' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KMID2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0fb71990-c340-4f74-93ec-2a73c1e626e2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0fb71990-c340-4f74-93ec-2a73c1e626e2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b41e7406-6f6e-4d1f-9826-7453f41c5260' class='xr-var-data-in' type='checkbox'><label for='data-b41e7406-6f6e-4d1f-9826-7453f41c5260' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KUP</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cc73539f-6e1d-49d3-a0a1-6d2b4250378a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cc73539f-6e1d-49d3-a0a1-6d2b4250378a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ddd19e07-e000-4baa-83df-31a28f040d24' class='xr-var-data-in' type='checkbox'><label for='data-ddd19e07-e000-4baa-83df-31a28f040d24' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KUP2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2f9cda31-8f06-4dbf-8c56-18ddcb6986fe' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2f9cda31-8f06-4dbf-8c56-18ddcb6986fe' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-efd71b10-14f7-48f2-a460-8dfc6cd802ca' class='xr-var-data-in' type='checkbox'><label for='data-efd71b10-14f7-48f2-a460-8dfc6cd802ca' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KLOW</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-b0ce8f04-0e5b-4497-b297-53bdebab39be' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b0ce8f04-0e5b-4497-b297-53bdebab39be' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-89b80e7a-b059-46bf-a665-be1ffebb91df' class='xr-var-data-in' type='checkbox'><label for='data-89b80e7a-b059-46bf-a665-be1ffebb91df' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KLOW2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3a56ce20-05e9-4259-a5d2-588507d58b58' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3a56ce20-05e9-4259-a5d2-588507d58b58' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-88b24cac-5bc1-4a08-a0ca-7cb9da4403c1' class='xr-var-data-in' type='checkbox'><label for='data-88b24cac-5bc1-4a08-a0ca-7cb9da4403c1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KSFT</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-da78b3c1-a695-4e18-b540-3749c56a4021' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-da78b3c1-a695-4e18-b540-3749c56a4021' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-155ba304-7e7e-4beb-80ac-76c9b21447b1' class='xr-var-data-in' type='checkbox'><label for='data-155ba304-7e7e-4beb-80ac-76c9b21447b1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>KSFT2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3834330a-bc1b-4a4c-b76f-aa4e47ac4eb0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3834330a-bc1b-4a4c-b76f-aa4e47ac4eb0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e706491f-b521-409d-9d9b-83fd344db8db' class='xr-var-data-in' type='checkbox'><label for='data-e706491f-b521-409d-9d9b-83fd344db8db' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>OPEN0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-afd8647f-5d15-4f48-b545-2c539d803992' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-afd8647f-5d15-4f48-b545-2c539d803992' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7788cb1c-9b69-4eca-9a13-e8b567c1e80a' class='xr-var-data-in' type='checkbox'><label for='data-7788cb1c-9b69-4eca-9a13-e8b567c1e80a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>OPEN1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-5fc12cd0-bec4-44c6-81e6-22739e8d8da4' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5fc12cd0-bec4-44c6-81e6-22739e8d8da4' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9ad9fea3-6b4a-438f-a44f-833a50c513ef' class='xr-var-data-in' type='checkbox'><label for='data-9ad9fea3-6b4a-438f-a44f-833a50c513ef' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>OPEN2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d7943c1f-edb5-4174-a133-4f841a9e4724' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d7943c1f-edb5-4174-a133-4f841a9e4724' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5b2a8b63-8e5b-439e-bdf4-f32c47da18fa' class='xr-var-data-in' type='checkbox'><label for='data-5b2a8b63-8e5b-439e-bdf4-f32c47da18fa' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>OPEN3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-b657468e-ded5-43b4-9beb-4785d0fd5b15' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b657468e-ded5-43b4-9beb-4785d0fd5b15' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8e611aec-6d59-4a9e-b015-e5173b864bba' class='xr-var-data-in' type='checkbox'><label for='data-8e611aec-6d59-4a9e-b015-e5173b864bba' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>OPEN4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3642090f-f9d5-404e-acfd-e6cf143e98c0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3642090f-f9d5-404e-acfd-e6cf143e98c0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7507de2d-ebc2-4454-b3f5-adf4725fbb78' class='xr-var-data-in' type='checkbox'><label for='data-7507de2d-ebc2-4454-b3f5-adf4725fbb78' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>HIGH0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-81f9f8e3-c3d7-49cb-a8b6-e5201f244d54' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-81f9f8e3-c3d7-49cb-a8b6-e5201f244d54' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-fe779a35-5d0a-43ff-80e7-4cb3f89aeb99' class='xr-var-data-in' type='checkbox'><label for='data-fe779a35-5d0a-43ff-80e7-4cb3f89aeb99' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>HIGH1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6b5729b8-84e3-4d18-beef-effeb36bfc17' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6b5729b8-84e3-4d18-beef-effeb36bfc17' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b36eb51c-549f-47e1-ada6-d90d9bd4e9ec' class='xr-var-data-in' type='checkbox'><label for='data-b36eb51c-549f-47e1-ada6-d90d9bd4e9ec' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>HIGH2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2fdcb1bf-bf86-4250-929a-24fd3f5f988a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2fdcb1bf-bf86-4250-929a-24fd3f5f988a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-dd0ca07e-1f71-4826-ae2d-066e7b487538' class='xr-var-data-in' type='checkbox'><label for='data-dd0ca07e-1f71-4826-ae2d-066e7b487538' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>HIGH3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6c02ab04-7392-47a1-9bea-d823d647b3a2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6c02ab04-7392-47a1-9bea-d823d647b3a2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a0e2541b-ca33-4528-a6f0-4c64e50e4f99' class='xr-var-data-in' type='checkbox'><label for='data-a0e2541b-ca33-4528-a6f0-4c64e50e4f99' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>HIGH4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2a483da5-7954-4fd3-a959-154f08cab84e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2a483da5-7954-4fd3-a959-154f08cab84e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-14eb1e7b-0abb-47c5-96ce-7feda915a410' class='xr-var-data-in' type='checkbox'><label for='data-14eb1e7b-0abb-47c5-96ce-7feda915a410' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>LOW0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0f51f7f7-e451-4223-a17a-25330b487ce8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0f51f7f7-e451-4223-a17a-25330b487ce8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d4c7acce-571b-4609-94cc-a573c8779314' class='xr-var-data-in' type='checkbox'><label for='data-d4c7acce-571b-4609-94cc-a573c8779314' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>LOW1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-42b16f0f-a9eb-4798-be3f-575ab610ddff' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-42b16f0f-a9eb-4798-be3f-575ab610ddff' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-673bd263-7838-4fd2-a8a1-ae078655759a' class='xr-var-data-in' type='checkbox'><label for='data-673bd263-7838-4fd2-a8a1-ae078655759a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>LOW2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c7928c26-5cbd-4960-abc1-ff9e25db06ae' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c7928c26-5cbd-4960-abc1-ff9e25db06ae' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-03ab242a-d7f9-48c0-b94b-bb662cd6e4c6' class='xr-var-data-in' type='checkbox'><label for='data-03ab242a-d7f9-48c0-b94b-bb662cd6e4c6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>LOW3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c58f5244-aea0-4213-9b74-a8f0901bb0a6' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c58f5244-aea0-4213-9b74-a8f0901bb0a6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-af7617ce-5bd2-4c9b-93bf-c1c72b123d51' class='xr-var-data-in' type='checkbox'><label for='data-af7617ce-5bd2-4c9b-93bf-c1c72b123d51' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>LOW4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-993d2a88-f197-4191-8db1-73f3dca520ca' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-993d2a88-f197-4191-8db1-73f3dca520ca' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5b4e862c-c7e9-49de-964c-f1620c71ab86' class='xr-var-data-in' type='checkbox'><label for='data-5b4e862c-c7e9-49de-964c-f1620c71ab86' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CLOSE0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-bac4da3e-436c-4950-b44e-c1b370eb5413' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-bac4da3e-436c-4950-b44e-c1b370eb5413' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-11b79b74-34a3-43e1-a7fb-379916c11e24' class='xr-var-data-in' type='checkbox'><label for='data-11b79b74-34a3-43e1-a7fb-379916c11e24' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CLOSE1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-fea390a4-ac10-425e-a293-e65ceb74f2ad' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-fea390a4-ac10-425e-a293-e65ceb74f2ad' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a2c56c0d-8189-4070-b008-3469eef34d5e' class='xr-var-data-in' type='checkbox'><label for='data-a2c56c0d-8189-4070-b008-3469eef34d5e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CLOSE2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6c9ef326-1b28-4496-962a-50cd7fcf6659' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6c9ef326-1b28-4496-962a-50cd7fcf6659' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-619b1480-35b4-4083-9a8e-9cee377d109f' class='xr-var-data-in' type='checkbox'><label for='data-619b1480-35b4-4083-9a8e-9cee377d109f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CLOSE3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-7afb5265-b24c-469b-bab0-f99cd8ebef7a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7afb5265-b24c-469b-bab0-f99cd8ebef7a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0b32700e-24e3-4bb6-a0c3-f0692a38abfa' class='xr-var-data-in' type='checkbox'><label for='data-0b32700e-24e3-4bb6-a0c3-f0692a38abfa' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CLOSE4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-20ef9fb8-9f85-4a73-bd85-a5520481cb37' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-20ef9fb8-9f85-4a73-bd85-a5520481cb37' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4d3d4865-9138-4ad9-89b9-fa99575d4e77' class='xr-var-data-in' type='checkbox'><label for='data-4d3d4865-9138-4ad9-89b9-fa99575d4e77' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VWAP0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-73537029-6a91-43e0-9524-3b5ddd34b3b1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-73537029-6a91-43e0-9524-3b5ddd34b3b1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-582b64c0-51cf-42c1-9583-8ffdf4440aa7' class='xr-var-data-in' type='checkbox'><label for='data-582b64c0-51cf-42c1-9583-8ffdf4440aa7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VWAP1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ed532b50-f933-4634-9a73-7ebcd192cd51' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ed532b50-f933-4634-9a73-7ebcd192cd51' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3fbff71a-3d01-4d13-b069-b18cfcedac74' class='xr-var-data-in' type='checkbox'><label for='data-3fbff71a-3d01-4d13-b069-b18cfcedac74' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VWAP2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-09b25e4d-9c64-4e82-b859-dcf531d32861' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-09b25e4d-9c64-4e82-b859-dcf531d32861' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-913c901f-ed18-49bf-9bf8-0abd79e89b8f' class='xr-var-data-in' type='checkbox'><label for='data-913c901f-ed18-49bf-9bf8-0abd79e89b8f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VWAP3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-71d65a0b-f1ce-4cc3-80e2-62c290a19e08' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-71d65a0b-f1ce-4cc3-80e2-62c290a19e08' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6ec345e5-1d79-43c4-ad77-cd0d3e291e71' class='xr-var-data-in' type='checkbox'><label for='data-6ec345e5-1d79-43c4-ad77-cd0d3e291e71' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VWAP4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-5fea77cd-2068-46b0-86f7-91dbf211ad04' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5fea77cd-2068-46b0-86f7-91dbf211ad04' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-aed605ac-da9f-4baa-85c3-1784a8116b86' class='xr-var-data-in' type='checkbox'><label for='data-aed605ac-da9f-4baa-85c3-1784a8116b86' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VOLUME0</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-723ba2a2-d543-4abe-a9fb-4d8ac3e0f4e0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-723ba2a2-d543-4abe-a9fb-4d8ac3e0f4e0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e1941ebc-e062-4d61-a192-233e368daccb' class='xr-var-data-in' type='checkbox'><label for='data-e1941ebc-e062-4d61-a192-233e368daccb' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VOLUME1</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c3e57feb-f1bc-48d5-9a81-856e613904b2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c3e57feb-f1bc-48d5-9a81-856e613904b2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8b8d7dd7-7cb8-4e8b-bdda-e838798801a0' class='xr-var-data-in' type='checkbox'><label for='data-8b8d7dd7-7cb8-4e8b-bdda-e838798801a0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VOLUME2</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-001aa488-1e92-400c-9419-e75dca441f35' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-001aa488-1e92-400c-9419-e75dca441f35' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b68e6aea-6646-4897-a90b-5ce339d4b188' class='xr-var-data-in' type='checkbox'><label for='data-b68e6aea-6646-4897-a90b-5ce339d4b188' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VOLUME3</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ef81f24b-fa9d-4267-a2aa-02e2edf7cbed' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ef81f24b-fa9d-4267-a2aa-02e2edf7cbed' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-36af19cf-32de-41c6-b27c-b4ee724ba2f1' class='xr-var-data-in' type='checkbox'><label for='data-36af19cf-32de-41c6-b27c-b4ee724ba2f1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VOLUME4</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-df79111a-ffff-43b8-9163-4ed100f047ed' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-df79111a-ffff-43b8-9163-4ed100f047ed' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-fc811f96-3347-44f8-8329-d5231a6399f7' class='xr-var-data-in' type='checkbox'><label for='data-fc811f96-3347-44f8-8329-d5231a6399f7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>ROC5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-25500148-eba5-4390-9e9f-cd517fa9decb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-25500148-eba5-4390-9e9f-cd517fa9decb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-77f336b3-3196-4fc8-8326-862cdabba1c6' class='xr-var-data-in' type='checkbox'><label for='data-77f336b3-3196-4fc8-8326-862cdabba1c6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>ROC10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-7f4db890-c23c-4fdb-8d9a-d87e61e6b1db' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7f4db890-c23c-4fdb-8d9a-d87e61e6b1db' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d46bdc69-d325-4e34-8ed5-e2d2f384b9cd' class='xr-var-data-in' type='checkbox'><label for='data-d46bdc69-d325-4e34-8ed5-e2d2f384b9cd' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>ROC20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-22146c93-b549-4250-937a-c3db0633f42e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-22146c93-b549-4250-937a-c3db0633f42e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1dcf75d8-ce71-4795-91fd-7187b8e7ddc4' class='xr-var-data-in' type='checkbox'><label for='data-1dcf75d8-ce71-4795-91fd-7187b8e7ddc4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>ROC30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-902d8656-a587-4b7e-8ef9-febd8124c44d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-902d8656-a587-4b7e-8ef9-febd8124c44d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c6b50911-5ad3-461d-9dbc-fe88035b0dd4' class='xr-var-data-in' type='checkbox'><label for='data-c6b50911-5ad3-461d-9dbc-fe88035b0dd4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>ROC60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-f2aa25a7-f353-4904-b06e-8c8661d33f9b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f2aa25a7-f353-4904-b06e-8c8661d33f9b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-17c71c6a-b2fc-42cd-8da5-84d661d085c1' class='xr-var-data-in' type='checkbox'><label for='data-17c71c6a-b2fc-42cd-8da5-84d661d085c1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MA5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-acc97846-868c-4810-b6b7-154d370837ca' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-acc97846-868c-4810-b6b7-154d370837ca' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e2c6176e-85fc-4021-ad5e-52139d93ffed' class='xr-var-data-in' type='checkbox'><label for='data-e2c6176e-85fc-4021-ad5e-52139d93ffed' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MA10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-be1a4e51-79b4-42a6-bc7f-295f416b65af' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-be1a4e51-79b4-42a6-bc7f-295f416b65af' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-eeef1da7-2493-4886-9155-718b31aedb5f' class='xr-var-data-in' type='checkbox'><label for='data-eeef1da7-2493-4886-9155-718b31aedb5f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MA20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-436f9cf5-605f-4fed-a6a3-36f761ca4c07' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-436f9cf5-605f-4fed-a6a3-36f761ca4c07' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1df22c4a-a87c-44eb-b833-58140367c4e1' class='xr-var-data-in' type='checkbox'><label for='data-1df22c4a-a87c-44eb-b833-58140367c4e1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MA30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3ff43d9a-5e09-47cc-94b6-f54f1a505295' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3ff43d9a-5e09-47cc-94b6-f54f1a505295' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-270b06d1-616c-4ddb-88c6-2b2159fde40e' class='xr-var-data-in' type='checkbox'><label for='data-270b06d1-616c-4ddb-88c6-2b2159fde40e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MA60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-20a540da-67f2-4661-ba6f-db983fa332ac' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-20a540da-67f2-4661-ba6f-db983fa332ac' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9d54f0f1-7646-4333-8d03-c7ef2674e1ee' class='xr-var-data-in' type='checkbox'><label for='data-9d54f0f1-7646-4333-8d03-c7ef2674e1ee' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>STD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cd8e8b69-2941-42ca-aae4-c7c00c598ac8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cd8e8b69-2941-42ca-aae4-c7c00c598ac8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-dfe6632e-e51e-4611-b96b-290b8f921374' class='xr-var-data-in' type='checkbox'><label for='data-dfe6632e-e51e-4611-b96b-290b8f921374' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>STD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ef745fc0-bd4e-4532-a7f1-837ed8eed7be' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ef745fc0-bd4e-4532-a7f1-837ed8eed7be' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3a191033-6842-47b7-ac12-940b21baf0f4' class='xr-var-data-in' type='checkbox'><label for='data-3a191033-6842-47b7-ac12-940b21baf0f4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>STD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e654d04a-b923-4195-a35f-a020a8af999b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e654d04a-b923-4195-a35f-a020a8af999b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d79ae331-9d2e-4ad9-a4e2-5afe94dc8355' class='xr-var-data-in' type='checkbox'><label for='data-d79ae331-9d2e-4ad9-a4e2-5afe94dc8355' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>STD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-75d97c5d-0ddc-4978-8378-f115c837b365' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-75d97c5d-0ddc-4978-8378-f115c837b365' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-22e32214-215b-4f37-9bdd-49a8fe4efd10' class='xr-var-data-in' type='checkbox'><label for='data-22e32214-215b-4f37-9bdd-49a8fe4efd10' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>STD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-1bd26208-1260-47d8-b549-2df4e7aa1484' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1bd26208-1260-47d8-b549-2df4e7aa1484' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2a9e4c4b-8bd8-41db-82fa-7ab9562c83bb' class='xr-var-data-in' type='checkbox'><label for='data-2a9e4c4b-8bd8-41db-82fa-7ab9562c83bb' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>BETA5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2efa880e-b2e0-45af-88b7-4fbbd53fee46' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2efa880e-b2e0-45af-88b7-4fbbd53fee46' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-08ed612a-a140-4aae-8a16-fe91955bc69e' class='xr-var-data-in' type='checkbox'><label for='data-08ed612a-a140-4aae-8a16-fe91955bc69e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>BETA10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-7c8c91ab-6063-42c3-a9ac-ba639783452b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7c8c91ab-6063-42c3-a9ac-ba639783452b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-15b69138-5539-41ef-a563-d9802444837d' class='xr-var-data-in' type='checkbox'><label for='data-15b69138-5539-41ef-a563-d9802444837d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>BETA20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3ba77ca3-59a1-4816-8968-6ef740b8aaa0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3ba77ca3-59a1-4816-8968-6ef740b8aaa0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2c6fe294-b728-4166-8fdb-0585de53d27f' class='xr-var-data-in' type='checkbox'><label for='data-2c6fe294-b728-4166-8fdb-0585de53d27f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>BETA30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e998c960-cef4-4be7-a0aa-c8bbb804ee0a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e998c960-cef4-4be7-a0aa-c8bbb804ee0a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6764f183-9478-4fdc-8b24-9dc2035ff716' class='xr-var-data-in' type='checkbox'><label for='data-6764f183-9478-4fdc-8b24-9dc2035ff716' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>BETA60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d30503fe-fe1b-46ee-a7f3-0ad45b8de570' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d30503fe-fe1b-46ee-a7f3-0ad45b8de570' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e41c7915-a9b1-4915-9bd9-df9bfa7fb693' class='xr-var-data-in' type='checkbox'><label for='data-e41c7915-a9b1-4915-9bd9-df9bfa7fb693' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSQR5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-8942a881-8e3d-4b04-80c7-0a38679ac7f0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8942a881-8e3d-4b04-80c7-0a38679ac7f0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7d904991-d19f-4d4d-b555-b4aeb2e79602' class='xr-var-data-in' type='checkbox'><label for='data-7d904991-d19f-4d4d-b555-b4aeb2e79602' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSQR10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a81d9c71-b37c-43ec-ae04-052187332c98' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a81d9c71-b37c-43ec-ae04-052187332c98' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-33a75f4c-9f68-4b31-b814-8edd292f227b' class='xr-var-data-in' type='checkbox'><label for='data-33a75f4c-9f68-4b31-b814-8edd292f227b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSQR20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d0665909-b99d-4c36-9ff6-e47e7da67537' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d0665909-b99d-4c36-9ff6-e47e7da67537' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7af152f9-c203-44c7-a647-85da41bcc5e7' class='xr-var-data-in' type='checkbox'><label for='data-7af152f9-c203-44c7-a647-85da41bcc5e7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSQR30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d9ab740a-e726-49e2-bbb9-72c5b04a5d7b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d9ab740a-e726-49e2-bbb9-72c5b04a5d7b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-add4b6a7-9f68-41da-848a-fc8cd6c7642d' class='xr-var-data-in' type='checkbox'><label for='data-add4b6a7-9f68-41da-848a-fc8cd6c7642d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSQR60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-36c0b2aa-67f5-4c95-bf69-e211dee4c9de' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-36c0b2aa-67f5-4c95-bf69-e211dee4c9de' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1e5baaf0-bed1-4dee-8164-e23ea27912c5' class='xr-var-data-in' type='checkbox'><label for='data-1e5baaf0-bed1-4dee-8164-e23ea27912c5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RESI5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-222a2c36-5187-4897-8bd8-1925e51862a1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-222a2c36-5187-4897-8bd8-1925e51862a1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-cb1bbe49-6971-4d05-82c5-00dc608aaee1' class='xr-var-data-in' type='checkbox'><label for='data-cb1bbe49-6971-4d05-82c5-00dc608aaee1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RESI10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-62af243a-834d-4797-8848-a58874dc478b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-62af243a-834d-4797-8848-a58874dc478b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3e93cf8b-37c0-4ba7-906c-7f72e031a95a' class='xr-var-data-in' type='checkbox'><label for='data-3e93cf8b-37c0-4ba7-906c-7f72e031a95a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RESI20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-31cb8e14-6d23-4dee-b685-ef67fb8287c7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-31cb8e14-6d23-4dee-b685-ef67fb8287c7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b6aa7023-7e2f-4265-89d3-ec6883ac12b5' class='xr-var-data-in' type='checkbox'><label for='data-b6aa7023-7e2f-4265-89d3-ec6883ac12b5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RESI30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ab237a4f-c30f-474e-aba7-0a11cf5f7592' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ab237a4f-c30f-474e-aba7-0a11cf5f7592' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3b0e3344-35ff-429d-8bf4-34e9276ca479' class='xr-var-data-in' type='checkbox'><label for='data-3b0e3344-35ff-429d-8bf4-34e9276ca479' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RESI60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-373ffc8a-7f65-478b-9116-03da185bfdf2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-373ffc8a-7f65-478b-9116-03da185bfdf2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5890dffc-fa08-489c-9cde-a8931c79c9d5' class='xr-var-data-in' type='checkbox'><label for='data-5890dffc-fa08-489c-9cde-a8931c79c9d5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MAX5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a04b1ae9-c972-441f-bd74-2837e1ce463f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a04b1ae9-c972-441f-bd74-2837e1ce463f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2247ea7f-e2c5-4dc2-bdb5-7fecec794c16' class='xr-var-data-in' type='checkbox'><label for='data-2247ea7f-e2c5-4dc2-bdb5-7fecec794c16' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MAX10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c068eec5-3dbb-4aaf-94f8-639b6e5306ce' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c068eec5-3dbb-4aaf-94f8-639b6e5306ce' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1f8f3708-2047-4dc2-b710-1c87a18b2602' class='xr-var-data-in' type='checkbox'><label for='data-1f8f3708-2047-4dc2-b710-1c87a18b2602' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MAX20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-af5c57ce-b208-4d1a-ba23-520e118c1849' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-af5c57ce-b208-4d1a-ba23-520e118c1849' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ca14ba9c-71ce-48ff-a2a0-5b78a0da4f74' class='xr-var-data-in' type='checkbox'><label for='data-ca14ba9c-71ce-48ff-a2a0-5b78a0da4f74' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MAX30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-acca53c4-bdf4-41f7-bb6f-eb6247bab939' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-acca53c4-bdf4-41f7-bb6f-eb6247bab939' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f1fd9689-cb28-45dc-8893-dfa78cb0c3f9' class='xr-var-data-in' type='checkbox'><label for='data-f1fd9689-cb28-45dc-8893-dfa78cb0c3f9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MAX60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a7e7c1d3-7dbf-4dfc-8e6e-7734f70dc6b9' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a7e7c1d3-7dbf-4dfc-8e6e-7734f70dc6b9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-266e4c11-80b9-4de5-92a4-528f3c936fcf' class='xr-var-data-in' type='checkbox'><label for='data-266e4c11-80b9-4de5-92a4-528f3c936fcf' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MIN5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-b1ee84b2-ed22-46f8-8ee1-c8d81a9c9bbb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b1ee84b2-ed22-46f8-8ee1-c8d81a9c9bbb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-50d9855d-b606-4ea3-9500-58182ce5e3dd' class='xr-var-data-in' type='checkbox'><label for='data-50d9855d-b606-4ea3-9500-58182ce5e3dd' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MIN10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-5b9d2a90-d292-43c8-88a6-8ce96ef9b3b7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5b9d2a90-d292-43c8-88a6-8ce96ef9b3b7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9d216a5a-913c-4b24-8509-dc89f7420476' class='xr-var-data-in' type='checkbox'><label for='data-9d216a5a-913c-4b24-8509-dc89f7420476' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MIN20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9ef80cec-d019-46e0-b9b9-451c9e6cb193' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9ef80cec-d019-46e0-b9b9-451c9e6cb193' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c9d8e4ff-52f4-4638-8d1f-bfcc4ae0233c' class='xr-var-data-in' type='checkbox'><label for='data-c9d8e4ff-52f4-4638-8d1f-bfcc4ae0233c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MIN30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a4f1a2dd-6b2e-4e54-bc2d-b93842509544' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a4f1a2dd-6b2e-4e54-bc2d-b93842509544' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-591a0410-186a-4ce1-9e2c-55270b2fd281' class='xr-var-data-in' type='checkbox'><label for='data-591a0410-186a-4ce1-9e2c-55270b2fd281' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>MIN60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-1493e1c2-5fcc-4926-84b3-76314aa34ee5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1493e1c2-5fcc-4926-84b3-76314aa34ee5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a17b56ed-b156-48f2-b50e-f950d650de6d' class='xr-var-data-in' type='checkbox'><label for='data-a17b56ed-b156-48f2-b50e-f950d650de6d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLU5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-30dad342-6aed-4e6e-9db8-234c4c5f57c8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-30dad342-6aed-4e6e-9db8-234c4c5f57c8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-df6cf294-eb67-4920-b9fb-184992223df6' class='xr-var-data-in' type='checkbox'><label for='data-df6cf294-eb67-4920-b9fb-184992223df6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLU10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c2f4209b-1666-4f16-98a4-9e2747e630be' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c2f4209b-1666-4f16-98a4-9e2747e630be' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c774199a-8f46-42db-9b73-c90496ece933' class='xr-var-data-in' type='checkbox'><label for='data-c774199a-8f46-42db-9b73-c90496ece933' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLU20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-97a99f33-0b32-4175-8623-033d6cd3fa10' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-97a99f33-0b32-4175-8623-033d6cd3fa10' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5db9a2c6-fadf-4df6-9140-ecf816c53d04' class='xr-var-data-in' type='checkbox'><label for='data-5db9a2c6-fadf-4df6-9140-ecf816c53d04' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLU30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-7943b893-c658-42d8-8816-abd0181d79fa' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7943b893-c658-42d8-8816-abd0181d79fa' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a041c6d5-7e37-4333-ad5e-ebca607776a8' class='xr-var-data-in' type='checkbox'><label for='data-a041c6d5-7e37-4333-ad5e-ebca607776a8' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLU60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-69ac1fcb-ac44-42d0-ab63-867c01899437' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-69ac1fcb-ac44-42d0-ab63-867c01899437' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-78b8d5fc-8bf1-4dbe-99e9-d4e49a2651ed' class='xr-var-data-in' type='checkbox'><label for='data-78b8d5fc-8bf1-4dbe-99e9-d4e49a2651ed' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-666ab0bc-705c-4bbf-b6ef-4ec3957e78b8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-666ab0bc-705c-4bbf-b6ef-4ec3957e78b8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2fb7fe97-dd1d-4901-994e-f55bbb2fb4e2' class='xr-var-data-in' type='checkbox'><label for='data-2fb7fe97-dd1d-4901-994e-f55bbb2fb4e2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-60a29417-87a1-4bd6-90a6-fbb701f0b1f5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-60a29417-87a1-4bd6-90a6-fbb701f0b1f5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-676e29fc-0c34-44ea-b85d-2483f846db63' class='xr-var-data-in' type='checkbox'><label for='data-676e29fc-0c34-44ea-b85d-2483f846db63' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0dbffe90-f940-4feb-8a1c-a8c7bca9e41d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0dbffe90-f940-4feb-8a1c-a8c7bca9e41d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-08ecce09-1e95-4a11-816e-df899209586c' class='xr-var-data-in' type='checkbox'><label for='data-08ecce09-1e95-4a11-816e-df899209586c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e82a4f9c-c191-4bee-a8bf-5297b9af4795' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e82a4f9c-c191-4bee-a8bf-5297b9af4795' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b55b535b-b03c-4c6f-901e-0e067d037d04' class='xr-var-data-in' type='checkbox'><label for='data-b55b535b-b03c-4c6f-901e-0e067d037d04' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>QTLD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-53a03a63-12cc-416b-b2d2-84d122a5ee30' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-53a03a63-12cc-416b-b2d2-84d122a5ee30' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-993c7018-c704-4ec1-a3a9-0ebadf3ec91f' class='xr-var-data-in' type='checkbox'><label for='data-993c7018-c704-4ec1-a3a9-0ebadf3ec91f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RANK5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6e2b072a-f448-427c-98cf-3ce1d8aa0f92' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6e2b072a-f448-427c-98cf-3ce1d8aa0f92' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-05a56662-2162-4fe8-8b61-85325866020d' class='xr-var-data-in' type='checkbox'><label for='data-05a56662-2162-4fe8-8b61-85325866020d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RANK10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-abc65b19-3123-427f-8043-e63dc5481be7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-abc65b19-3123-427f-8043-e63dc5481be7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4859cf27-c2d5-4976-aef3-7deac1b08436' class='xr-var-data-in' type='checkbox'><label for='data-4859cf27-c2d5-4976-aef3-7deac1b08436' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RANK20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-4af6d5fa-2c34-431b-a776-6745f7385724' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4af6d5fa-2c34-431b-a776-6745f7385724' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-75c82292-5b28-4ef5-86d1-ae2618e4a21c' class='xr-var-data-in' type='checkbox'><label for='data-75c82292-5b28-4ef5-86d1-ae2618e4a21c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RANK30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-37f23136-5945-457b-b08a-f580fefc7ff8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-37f23136-5945-457b-b08a-f580fefc7ff8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-35eebe62-91fc-4f24-91a6-7787dca3f54c' class='xr-var-data-in' type='checkbox'><label for='data-35eebe62-91fc-4f24-91a6-7787dca3f54c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RANK60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0829d78b-b4e0-4ada-852b-5836aa4a749e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0829d78b-b4e0-4ada-852b-5836aa4a749e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a4b82918-e959-44d5-ac9c-dcea120d0180' class='xr-var-data-in' type='checkbox'><label for='data-a4b82918-e959-44d5-ac9c-dcea120d0180' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSV5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2ffea066-1020-4971-9a07-8dc3e7d94c9b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2ffea066-1020-4971-9a07-8dc3e7d94c9b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c86b0814-7a81-40c0-94dd-6a578b247c69' class='xr-var-data-in' type='checkbox'><label for='data-c86b0814-7a81-40c0-94dd-6a578b247c69' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSV10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-41a7a98e-86d4-40b5-bf87-84d04d4e052a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-41a7a98e-86d4-40b5-bf87-84d04d4e052a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ed463d5a-52da-4ba5-be8c-02c506def864' class='xr-var-data-in' type='checkbox'><label for='data-ed463d5a-52da-4ba5-be8c-02c506def864' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSV20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-addca75e-dbd3-4026-b3af-816a6570bbc9' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-addca75e-dbd3-4026-b3af-816a6570bbc9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-435b2231-49f6-4aab-a0cf-9fd21dcad40d' class='xr-var-data-in' type='checkbox'><label for='data-435b2231-49f6-4aab-a0cf-9fd21dcad40d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSV30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c92693c7-445b-4e35-9d0f-96c1580a4e2d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c92693c7-445b-4e35-9d0f-96c1580a4e2d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ff162ad3-125f-436d-98ea-6a0dcebc3a90' class='xr-var-data-in' type='checkbox'><label for='data-ff162ad3-125f-436d-98ea-6a0dcebc3a90' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>RSV60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-024c566d-aaf6-466f-816b-8e9d0eb1493c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-024c566d-aaf6-466f-816b-8e9d0eb1493c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c9903bb8-a5f5-4aeb-ab38-0449b8cee32c' class='xr-var-data-in' type='checkbox'><label for='data-c9903bb8-a5f5-4aeb-ab38-0449b8cee32c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMAX5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-399a0e2b-a9d5-46f5-b8c7-71c54c463dec' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-399a0e2b-a9d5-46f5-b8c7-71c54c463dec' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e7043482-6c2e-4aff-8909-9cbfe83926c2' class='xr-var-data-in' type='checkbox'><label for='data-e7043482-6c2e-4aff-8909-9cbfe83926c2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMAX10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-109e433a-9531-4421-82b6-8771d88b2297' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-109e433a-9531-4421-82b6-8771d88b2297' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9ac3d2df-c6ef-4c18-aecd-083a12f89173' class='xr-var-data-in' type='checkbox'><label for='data-9ac3d2df-c6ef-4c18-aecd-083a12f89173' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMAX20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-061cc7cb-ff65-400d-8a4f-31007f055d9d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-061cc7cb-ff65-400d-8a4f-31007f055d9d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-729a33e4-111f-455e-9ec1-c8a7551474fc' class='xr-var-data-in' type='checkbox'><label for='data-729a33e4-111f-455e-9ec1-c8a7551474fc' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMAX30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9febed99-9e48-4ff2-a389-5ea77ef88937' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9febed99-9e48-4ff2-a389-5ea77ef88937' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-94cfa20f-1bd5-493c-b0c8-e85d6411ffd3' class='xr-var-data-in' type='checkbox'><label for='data-94cfa20f-1bd5-493c-b0c8-e85d6411ffd3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMAX60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-f2a628a9-57e3-450e-889a-8b0db5fc6648' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f2a628a9-57e3-450e-889a-8b0db5fc6648' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8489137a-20f9-45eb-850f-460582f88636' class='xr-var-data-in' type='checkbox'><label for='data-8489137a-20f9-45eb-850f-460582f88636' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMIN5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-90e22f46-02b6-4a02-910b-842dc4c8b057' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-90e22f46-02b6-4a02-910b-842dc4c8b057' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4efd5292-2443-488c-9db4-baea9467dd5f' class='xr-var-data-in' type='checkbox'><label for='data-4efd5292-2443-488c-9db4-baea9467dd5f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMIN10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c6fd7df4-46d3-493c-a730-7dcbd00b9c0c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c6fd7df4-46d3-493c-a730-7dcbd00b9c0c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-733af0b8-4cfb-4fd1-b0f7-4059481ed0ec' class='xr-var-data-in' type='checkbox'><label for='data-733af0b8-4cfb-4fd1-b0f7-4059481ed0ec' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMIN20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d655f734-4d4d-44d3-be57-0b59ab25a6f8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d655f734-4d4d-44d3-be57-0b59ab25a6f8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3784bfd2-5f32-47fd-82d2-249e2d34642f' class='xr-var-data-in' type='checkbox'><label for='data-3784bfd2-5f32-47fd-82d2-249e2d34642f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMIN30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a8be4bca-ce8b-4a5d-8e0b-c84f04278955' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a8be4bca-ce8b-4a5d-8e0b-c84f04278955' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6a08c739-5160-40ca-8d2e-79452fe25ebf' class='xr-var-data-in' type='checkbox'><label for='data-6a08c739-5160-40ca-8d2e-79452fe25ebf' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMIN60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2c29bf80-25dd-4bb2-b221-9ae0015f40a7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2c29bf80-25dd-4bb2-b221-9ae0015f40a7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5da225c5-355c-4c3b-b691-cf636327d481' class='xr-var-data-in' type='checkbox'><label for='data-5da225c5-355c-4c3b-b691-cf636327d481' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMXD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-1bfe5359-4339-4c7b-ac36-973e33f250d6' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1bfe5359-4339-4c7b-ac36-973e33f250d6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-608c8ac0-8ee5-4901-837f-74333619c73d' class='xr-var-data-in' type='checkbox'><label for='data-608c8ac0-8ee5-4901-837f-74333619c73d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMXD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-eb684a33-00e8-41cf-9592-92c51d4d7b59' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-eb684a33-00e8-41cf-9592-92c51d4d7b59' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-648d24d0-a72a-491e-86c1-e93f2cc2166b' class='xr-var-data-in' type='checkbox'><label for='data-648d24d0-a72a-491e-86c1-e93f2cc2166b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMXD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-30c1c03b-e4a1-4142-ad63-d5f515e4d3da' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-30c1c03b-e4a1-4142-ad63-d5f515e4d3da' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ee4aa854-63f5-4482-8254-eb0da6c3305c' class='xr-var-data-in' type='checkbox'><label for='data-ee4aa854-63f5-4482-8254-eb0da6c3305c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMXD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-f58ccee7-c9f2-45f6-8bb7-9229b59d5b32' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f58ccee7-c9f2-45f6-8bb7-9229b59d5b32' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ff4992da-27ea-46a7-8ad0-5964b3af2c27' class='xr-var-data-in' type='checkbox'><label for='data-ff4992da-27ea-46a7-8ad0-5964b3af2c27' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>IMXD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-fae9e89c-e387-47df-b1b2-f867b598cd74' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-fae9e89c-e387-47df-b1b2-f867b598cd74' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f8756bff-5b9d-4a61-a22e-9fea7a919213' class='xr-var-data-in' type='checkbox'><label for='data-f8756bff-5b9d-4a61-a22e-9fea7a919213' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORR5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e5a8810d-ba83-48d6-8834-57ed31d39e22' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e5a8810d-ba83-48d6-8834-57ed31d39e22' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-50254bca-2e86-491f-a769-a79e56ced574' class='xr-var-data-in' type='checkbox'><label for='data-50254bca-2e86-491f-a769-a79e56ced574' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORR10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-79f14643-0898-46fc-bbdb-d69f31734bdc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-79f14643-0898-46fc-bbdb-d69f31734bdc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ef318fd5-f31d-4b85-8711-d9cac20bb3be' class='xr-var-data-in' type='checkbox'><label for='data-ef318fd5-f31d-4b85-8711-d9cac20bb3be' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORR20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-94b13a63-f75d-447d-9f43-627fdb32793d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-94b13a63-f75d-447d-9f43-627fdb32793d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7196e5ec-b5d2-4954-9fe3-b0fbb386422a' class='xr-var-data-in' type='checkbox'><label for='data-7196e5ec-b5d2-4954-9fe3-b0fbb386422a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORR30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-35926995-5178-4ecd-ae61-40c137096ca5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-35926995-5178-4ecd-ae61-40c137096ca5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-83b7ec54-0894-47b9-aa68-1bf8af8853ec' class='xr-var-data-in' type='checkbox'><label for='data-83b7ec54-0894-47b9-aa68-1bf8af8853ec' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORR60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d7ef31db-813c-49c9-afaa-5f10474963dc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d7ef31db-813c-49c9-afaa-5f10474963dc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b7f680a1-181f-444f-bcff-4ba374ae9341' class='xr-var-data-in' type='checkbox'><label for='data-b7f680a1-181f-444f-bcff-4ba374ae9341' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ff19d2df-7531-4905-a853-22cb8be90421' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ff19d2df-7531-4905-a853-22cb8be90421' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-37b957fd-213e-4bcb-aeee-b8bb5a823236' class='xr-var-data-in' type='checkbox'><label for='data-37b957fd-213e-4bcb-aeee-b8bb5a823236' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-4779dd3d-0c3b-42a8-b32e-f8d09b19942a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4779dd3d-0c3b-42a8-b32e-f8d09b19942a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0e0b77fa-ecf9-4969-878b-cea1fe19fa80' class='xr-var-data-in' type='checkbox'><label for='data-0e0b77fa-ecf9-4969-878b-cea1fe19fa80' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e615df00-3cf7-4516-a6c7-ed6cad38e152' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e615df00-3cf7-4516-a6c7-ed6cad38e152' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c1726641-7e09-46a8-b807-15acbc205d12' class='xr-var-data-in' type='checkbox'><label for='data-c1726641-7e09-46a8-b807-15acbc205d12' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-617188a8-e73f-41f1-a2e1-efa73f02ee66' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-617188a8-e73f-41f1-a2e1-efa73f02ee66' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8f9dd27b-1e76-460a-8b15-c2b7279bcdb6' class='xr-var-data-in' type='checkbox'><label for='data-8f9dd27b-1e76-460a-8b15-c2b7279bcdb6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CORD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-f4fc8387-1313-43b0-96ba-91e73e6195b9' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f4fc8387-1313-43b0-96ba-91e73e6195b9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c165eb89-9183-40cd-9758-bf4689d7e834' class='xr-var-data-in' type='checkbox'><label for='data-c165eb89-9183-40cd-9758-bf4689d7e834' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTP5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-537582b8-982a-4f00-a7c1-9142e2a44d94' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-537582b8-982a-4f00-a7c1-9142e2a44d94' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-43f90c74-ceba-4772-8399-aef0fb20ca3c' class='xr-var-data-in' type='checkbox'><label for='data-43f90c74-ceba-4772-8399-aef0fb20ca3c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTP10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-db3ba8cd-f352-418e-8f57-7e2d647ecd25' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-db3ba8cd-f352-418e-8f57-7e2d647ecd25' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ba0425d0-06a3-46f5-94d9-edcce3b36d63' class='xr-var-data-in' type='checkbox'><label for='data-ba0425d0-06a3-46f5-94d9-edcce3b36d63' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTP20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ada68218-d28f-4a6f-a5f5-dc4c72cec165' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ada68218-d28f-4a6f-a5f5-dc4c72cec165' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f0dcac5d-cd2e-4a82-bfb1-da5e52587a83' class='xr-var-data-in' type='checkbox'><label for='data-f0dcac5d-cd2e-4a82-bfb1-da5e52587a83' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTP30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9c2713bd-24f6-4931-9a1d-1a0fea075fff' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9c2713bd-24f6-4931-9a1d-1a0fea075fff' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4301d61c-5106-484f-9d12-a1553c9232d2' class='xr-var-data-in' type='checkbox'><label for='data-4301d61c-5106-484f-9d12-a1553c9232d2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTP60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-795b02b5-b9d5-4166-9120-4cbba9470c4d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-795b02b5-b9d5-4166-9120-4cbba9470c4d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c6290dd3-3231-4c3d-8e82-e48974feaff0' class='xr-var-data-in' type='checkbox'><label for='data-c6290dd3-3231-4c3d-8e82-e48974feaff0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTN5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cea8e8d5-9431-4c84-9cb7-31664f4dd889' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cea8e8d5-9431-4c84-9cb7-31664f4dd889' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-024f3714-3699-48d5-bf5d-fc77661c9eeb' class='xr-var-data-in' type='checkbox'><label for='data-024f3714-3699-48d5-bf5d-fc77661c9eeb' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTN10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-047d08a6-50ce-4ee7-8bb7-f5e6c6f7ce09' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-047d08a6-50ce-4ee7-8bb7-f5e6c6f7ce09' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-fa87e5f5-18b3-4b72-bb87-667ae40e0097' class='xr-var-data-in' type='checkbox'><label for='data-fa87e5f5-18b3-4b72-bb87-667ae40e0097' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTN20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-643b2393-47ff-4daf-9b9b-be37d565c786' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-643b2393-47ff-4daf-9b9b-be37d565c786' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ac76daf1-218f-4045-9d23-9db3dde7f245' class='xr-var-data-in' type='checkbox'><label for='data-ac76daf1-218f-4045-9d23-9db3dde7f245' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTN30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ee4b60b2-08d7-4658-a6ca-d952a4a05133' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ee4b60b2-08d7-4658-a6ca-d952a4a05133' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f83d79bb-eaca-4e2c-800d-32c0a24b12aa' class='xr-var-data-in' type='checkbox'><label for='data-f83d79bb-eaca-4e2c-800d-32c0a24b12aa' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTN60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-709c5ab2-2834-4daa-843b-86f5c3d6ed39' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-709c5ab2-2834-4daa-843b-86f5c3d6ed39' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c8d158c9-e538-4e31-9581-75d27d096452' class='xr-var-data-in' type='checkbox'><label for='data-c8d158c9-e538-4e31-9581-75d27d096452' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-4d8fc58c-dcde-478a-9686-c36aa814f8f0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4d8fc58c-dcde-478a-9686-c36aa814f8f0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-237e2500-978f-43b4-a366-346481a5b83e' class='xr-var-data-in' type='checkbox'><label for='data-237e2500-978f-43b4-a366-346481a5b83e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-34f708cd-ee7a-4332-9c17-f3d117d14442' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-34f708cd-ee7a-4332-9c17-f3d117d14442' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-69b4662a-6480-4d28-a51a-682d4c73bcc0' class='xr-var-data-in' type='checkbox'><label for='data-69b4662a-6480-4d28-a51a-682d4c73bcc0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0578d09b-a67a-4b26-932d-60aa910ccb8c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0578d09b-a67a-4b26-932d-60aa910ccb8c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8acb4d47-802e-4ff6-8470-5edde6d02d57' class='xr-var-data-in' type='checkbox'><label for='data-8acb4d47-802e-4ff6-8470-5edde6d02d57' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cd195a53-2dda-4d7a-b5a5-baf9bee23bb5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cd195a53-2dda-4d7a-b5a5-baf9bee23bb5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-bb503a3a-8ede-429e-9496-21670cee0fc3' class='xr-var-data-in' type='checkbox'><label for='data-bb503a3a-8ede-429e-9496-21670cee0fc3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>CNTD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-1a51e71f-2b3f-49bd-bbd4-7d517a49b4c8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1a51e71f-2b3f-49bd-bbd4-7d517a49b4c8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-359814ae-ac4f-4998-9d7e-44ea8b038b5d' class='xr-var-data-in' type='checkbox'><label for='data-359814ae-ac4f-4998-9d7e-44ea8b038b5d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMP5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d11cc618-d1e2-41d7-9fef-1b7dde60df60' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d11cc618-d1e2-41d7-9fef-1b7dde60df60' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d4cc353e-5399-4bc8-b947-96817271158b' class='xr-var-data-in' type='checkbox'><label for='data-d4cc353e-5399-4bc8-b947-96817271158b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMP10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-8c49e8e0-583c-4110-b5b9-81ec1f9ec6b0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8c49e8e0-583c-4110-b5b9-81ec1f9ec6b0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e6658604-2eaa-49d0-9231-034caf313ec9' class='xr-var-data-in' type='checkbox'><label for='data-e6658604-2eaa-49d0-9231-034caf313ec9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMP20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0d25b7b5-2fcf-42eb-ba52-4419153f32c7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0d25b7b5-2fcf-42eb-ba52-4419153f32c7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d20b896d-906f-45fb-8864-bedc5f985b77' class='xr-var-data-in' type='checkbox'><label for='data-d20b896d-906f-45fb-8864-bedc5f985b77' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMP30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-8f0b3349-da26-4a88-b8f1-7db96750ef5d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8f0b3349-da26-4a88-b8f1-7db96750ef5d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e400adf3-95bb-42c2-aefc-866b2c88267d' class='xr-var-data-in' type='checkbox'><label for='data-e400adf3-95bb-42c2-aefc-866b2c88267d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMP60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d3cbeb50-88de-459a-ba72-e0b6c3f3db08' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d3cbeb50-88de-459a-ba72-e0b6c3f3db08' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c68a220f-75e1-48e0-99c8-f707e0395c2a' class='xr-var-data-in' type='checkbox'><label for='data-c68a220f-75e1-48e0-99c8-f707e0395c2a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMN5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9332fb2f-6027-4cff-8319-5284122bf12c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9332fb2f-6027-4cff-8319-5284122bf12c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f34a2bca-966c-4e6d-8ba6-940995e49b57' class='xr-var-data-in' type='checkbox'><label for='data-f34a2bca-966c-4e6d-8ba6-940995e49b57' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMN10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a4590cc8-9c7f-4e6b-b169-b85ef53d85c6' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a4590cc8-9c7f-4e6b-b169-b85ef53d85c6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c6fbcaff-fe32-4220-9a5b-098463e4c89c' class='xr-var-data-in' type='checkbox'><label for='data-c6fbcaff-fe32-4220-9a5b-098463e4c89c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMN20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-b74a1367-12e3-4833-a60a-e4c217aa711e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b74a1367-12e3-4833-a60a-e4c217aa711e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d073a5d7-4799-4cb0-8010-af2f1527c84f' class='xr-var-data-in' type='checkbox'><label for='data-d073a5d7-4799-4cb0-8010-af2f1527c84f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMN30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6a5a6eaf-0480-421d-89a4-1312bfb65996' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6a5a6eaf-0480-421d-89a4-1312bfb65996' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ccb886a8-4416-4487-8efd-b97c76e75fef' class='xr-var-data-in' type='checkbox'><label for='data-ccb886a8-4416-4487-8efd-b97c76e75fef' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMN60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-89f23fde-48b2-481b-952f-d2ed59f5f3e1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-89f23fde-48b2-481b-952f-d2ed59f5f3e1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-51b83588-df52-40bf-bb5c-f2d86312e979' class='xr-var-data-in' type='checkbox'><label for='data-51b83588-df52-40bf-bb5c-f2d86312e979' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9c7a0230-4b07-430a-bf95-f658cc3bf359' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9c7a0230-4b07-430a-bf95-f658cc3bf359' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-44980749-afe4-4daa-8548-0e6aff2b925f' class='xr-var-data-in' type='checkbox'><label for='data-44980749-afe4-4daa-8548-0e6aff2b925f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-a95aad45-1528-4552-950d-57fa42fb7ee1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a95aad45-1528-4552-950d-57fa42fb7ee1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c6621ed4-8054-4308-ab48-df07dbca3458' class='xr-var-data-in' type='checkbox'><label for='data-c6621ed4-8054-4308-ab48-df07dbca3458' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cb633cc0-e16a-43e7-ad6f-69b4711e83f6' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cb633cc0-e16a-43e7-ad6f-69b4711e83f6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ae282e54-5eb3-48db-8741-91a8a5c360ce' class='xr-var-data-in' type='checkbox'><label for='data-ae282e54-5eb3-48db-8741-91a8a5c360ce' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-56ead9c7-616f-4509-833d-aa6547646174' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-56ead9c7-616f-4509-833d-aa6547646174' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b15934a4-b780-4702-8199-511aa564641f' class='xr-var-data-in' type='checkbox'><label for='data-b15934a4-b780-4702-8199-511aa564641f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>SUMD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-9a3c7a1a-1228-4a24-9ad5-2b4ffb4831cd' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9a3c7a1a-1228-4a24-9ad5-2b4ffb4831cd' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-46aaef75-fd0d-43da-829d-d1c70e1b9bc5' class='xr-var-data-in' type='checkbox'><label for='data-46aaef75-fd0d-43da-829d-d1c70e1b9bc5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VMA5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-63ad8e4f-13ec-4c95-a2ad-ae443480d6b6' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-63ad8e4f-13ec-4c95-a2ad-ae443480d6b6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-56a07fe5-c0d4-4e5d-a4eb-408ac9e421b1' class='xr-var-data-in' type='checkbox'><label for='data-56a07fe5-c0d4-4e5d-a4eb-408ac9e421b1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VMA10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-21499a1a-7100-4b40-a59a-217a7b9f5175' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-21499a1a-7100-4b40-a59a-217a7b9f5175' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f77f6473-a73a-41f3-901d-097a11203b0d' class='xr-var-data-in' type='checkbox'><label for='data-f77f6473-a73a-41f3-901d-097a11203b0d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VMA20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-025761c2-85a2-4a26-9bf9-2fdb2db38d93' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-025761c2-85a2-4a26-9bf9-2fdb2db38d93' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-13f0771b-b320-457e-b9e9-a3adf62cea02' class='xr-var-data-in' type='checkbox'><label for='data-13f0771b-b320-457e-b9e9-a3adf62cea02' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VMA30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-d132d636-7b21-40e0-af11-b48a8a364e6f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d132d636-7b21-40e0-af11-b48a8a364e6f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-34c26dcf-616c-47ea-afe0-6a6b634759ff' class='xr-var-data-in' type='checkbox'><label for='data-34c26dcf-616c-47ea-afe0-6a6b634759ff' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VMA60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-137cebea-7a1b-4f85-bb47-8aaa4bb01140' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-137cebea-7a1b-4f85-bb47-8aaa4bb01140' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7ab6b0f0-bf69-48f2-bddf-940f6aea7281' class='xr-var-data-in' type='checkbox'><label for='data-7ab6b0f0-bf69-48f2-bddf-940f6aea7281' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSTD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-7d40adc0-f1e8-459c-adf4-d4407b7c8b7b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7d40adc0-f1e8-459c-adf4-d4407b7c8b7b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a0e82d3b-af90-4646-a131-2884e7459434' class='xr-var-data-in' type='checkbox'><label for='data-a0e82d3b-af90-4646-a131-2884e7459434' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSTD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6a00c65c-9990-418d-8053-44837b14b53a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6a00c65c-9990-418d-8053-44837b14b53a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e885ecbb-ea1c-4afa-8b74-4d18974935a7' class='xr-var-data-in' type='checkbox'><label for='data-e885ecbb-ea1c-4afa-8b74-4d18974935a7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSTD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3308da95-fb3e-43b6-ab20-f9273a1879b4' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3308da95-fb3e-43b6-ab20-f9273a1879b4' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5f69e8fa-9c0c-4467-975c-11031bbd0241' class='xr-var-data-in' type='checkbox'><label for='data-5f69e8fa-9c0c-4467-975c-11031bbd0241' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSTD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e4847c32-1003-4be2-80ee-d77fec917dbb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e4847c32-1003-4be2-80ee-d77fec917dbb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ca74a32d-f3b9-4d48-bbe0-e29c14680bd5' class='xr-var-data-in' type='checkbox'><label for='data-ca74a32d-f3b9-4d48-bbe0-e29c14680bd5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSTD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-ea85d6b2-40e8-4e4b-8130-cbf19e7e6105' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ea85d6b2-40e8-4e4b-8130-cbf19e7e6105' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-48c69894-bece-4bbf-8210-813648908fe2' class='xr-var-data-in' type='checkbox'><label for='data-48c69894-bece-4bbf-8210-813648908fe2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>WVMA5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-13bd3ec4-2099-4ec5-bbdf-77a8e7b8e79d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-13bd3ec4-2099-4ec5-bbdf-77a8e7b8e79d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b0df2a1d-c4f7-4ffb-93d1-c359d7147536' class='xr-var-data-in' type='checkbox'><label for='data-b0df2a1d-c4f7-4ffb-93d1-c359d7147536' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>WVMA10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3871ca2d-f20a-4b78-a99f-4366866fc23b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3871ca2d-f20a-4b78-a99f-4366866fc23b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b2b349c9-c3c7-482a-9984-38c1a857037d' class='xr-var-data-in' type='checkbox'><label for='data-b2b349c9-c3c7-482a-9984-38c1a857037d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>WVMA20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-919606ad-965f-4b08-b61d-f4b8652219f1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-919606ad-965f-4b08-b61d-f4b8652219f1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ffb0013f-7e39-444b-a9da-9418f21e7912' class='xr-var-data-in' type='checkbox'><label for='data-ffb0013f-7e39-444b-a9da-9418f21e7912' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>WVMA30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-df32b29a-3d33-483e-86c0-7a65f431e42e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-df32b29a-3d33-483e-86c0-7a65f431e42e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-03b4e679-2f6e-464d-b5d4-291b47ddd195' class='xr-var-data-in' type='checkbox'><label for='data-03b4e679-2f6e-464d-b5d4-291b47ddd195' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>WVMA60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-c783893b-c6e9-4c40-8ce5-c6694eca6382' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c783893b-c6e9-4c40-8ce5-c6694eca6382' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c518e4c2-5738-4ed6-ad82-59ee462f6822' class='xr-var-data-in' type='checkbox'><label for='data-c518e4c2-5738-4ed6-ad82-59ee462f6822' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMP5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-3392794b-ff97-42e5-bd11-88e26a9997bc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3392794b-ff97-42e5-bd11-88e26a9997bc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-24abc463-8ecd-481d-ba05-26cc79b97e49' class='xr-var-data-in' type='checkbox'><label for='data-24abc463-8ecd-481d-ba05-26cc79b97e49' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMP10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-cd57de45-6747-4ff4-8dbf-09a05a5972ab' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cd57de45-6747-4ff4-8dbf-09a05a5972ab' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-728d6fee-9cad-4b2e-abaf-5fb0f14135ab' class='xr-var-data-in' type='checkbox'><label for='data-728d6fee-9cad-4b2e-abaf-5fb0f14135ab' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMP20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-f7687a47-4636-4bf0-abcd-d5f81bfef2fb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f7687a47-4636-4bf0-abcd-d5f81bfef2fb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-987761ca-681f-428d-8d4b-5084a63513df' class='xr-var-data-in' type='checkbox'><label for='data-987761ca-681f-428d-8d4b-5084a63513df' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMP30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-1909d997-cb37-4e88-a0ed-c7e17d9d770d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1909d997-cb37-4e88-a0ed-c7e17d9d770d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8a4f3905-b134-4411-a8e4-a6390fe4b8be' class='xr-var-data-in' type='checkbox'><label for='data-8a4f3905-b134-4411-a8e4-a6390fe4b8be' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMP60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-6d2a01a2-677a-4de6-a008-1fb62f3ab857' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6d2a01a2-677a-4de6-a008-1fb62f3ab857' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-aca456a0-8134-4afa-84e8-9c5bba492537' class='xr-var-data-in' type='checkbox'><label for='data-aca456a0-8134-4afa-84e8-9c5bba492537' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMN5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-b9ebeaca-8031-4a71-9ad8-78840cebc53f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b9ebeaca-8031-4a71-9ad8-78840cebc53f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ec3f5cb0-035e-4c01-b04a-30fdc9209734' class='xr-var-data-in' type='checkbox'><label for='data-ec3f5cb0-035e-4c01-b04a-30fdc9209734' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMN10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0f90cd9c-fabb-4b46-9017-4f6fd3cbb7da' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0f90cd9c-fabb-4b46-9017-4f6fd3cbb7da' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-35a80a39-a781-4bff-82bc-016b9db14519' class='xr-var-data-in' type='checkbox'><label for='data-35a80a39-a781-4bff-82bc-016b9db14519' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMN20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-0ce09739-119e-44de-8466-891ebdeba447' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0ce09739-119e-44de-8466-891ebdeba447' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-99246e8c-e65c-4056-be69-98879aaee38f' class='xr-var-data-in' type='checkbox'><label for='data-99246e8c-e65c-4056-be69-98879aaee38f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMN30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-e9d8eb6f-bd37-423f-8360-4326ff92c58c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e9d8eb6f-bd37-423f-8360-4326ff92c58c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-60457119-7fb4-4b6e-ba44-9a7df126632f' class='xr-var-data-in' type='checkbox'><label for='data-60457119-7fb4-4b6e-ba44-9a7df126632f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMN60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-68d8951a-03d6-4dc6-9025-68921418564c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-68d8951a-03d6-4dc6-9025-68921418564c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-04b71c08-2da3-4db9-89a1-93b4c5085eff' class='xr-var-data-in' type='checkbox'><label for='data-04b71c08-2da3-4db9-89a1-93b4c5085eff' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMD5</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-8bfea705-67ee-4ed9-96e2-9e178c6ed426' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8bfea705-67ee-4ed9-96e2-9e178c6ed426' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e9c5a9ff-8caf-47af-bdd7-6b74e9968bbe' class='xr-var-data-in' type='checkbox'><label for='data-e9c5a9ff-8caf-47af-bdd7-6b74e9968bbe' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMD10</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-81b68342-0876-4ffd-818b-497791e3af30' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-81b68342-0876-4ffd-818b-497791e3af30' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-79e551be-208c-4d0c-a96d-b84db8ffbd74' class='xr-var-data-in' type='checkbox'><label for='data-79e551be-208c-4d0c-a96d-b84db8ffbd74' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMD20</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-82e8d2bf-ca3e-415f-9c8f-635356d7be65' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-82e8d2bf-ca3e-415f-9c8f-635356d7be65' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-932c950e-0102-4fe6-b335-5ddd22ef127e' class='xr-var-data-in' type='checkbox'><label for='data-932c950e-0102-4fe6-b335-5ddd22ef127e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMD30</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-2bc1e3cf-272a-46a2-934a-3792bc632321' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2bc1e3cf-272a-46a2-934a-3792bc632321' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7924964c-8480-4913-b58a-71bf6ae7c889' class='xr-var-data-in' type='checkbox'><label for='data-7924964c-8480-4913-b58a-71bf6ae7c889' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>VSUMD60</span></div><div class='xr-var-dims'>(timestamp, symbol)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-44aa6904-da52-4a3b-8cc5-d2ee44ba9ec2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-44aa6904-da52-4a3b-8cc5-d2ee44ba9ec2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-16ec86a1-a11b-4c38-835a-0b9cb194c7d5' class='xr-var-data-in' type='checkbox'><label for='data-16ec86a1-a11b-4c38-835a-0b9cb194c7d5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[3699360 values with dtype=float32]</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ff0a46f6-61a0-409a-94e8-bdc2582a740e' class='xr-section-summary-in' type='checkbox'  ><label for='section-ff0a46f6-61a0-409a-94e8-bdc2582a740e' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>timestamp</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-8c41647c-947c-425e-bb92-69e3c5caf4a8' class='xr-index-data-in' type='checkbox'/><label for='index-8c41647c-947c-425e-bb92-69e3c5caf4a8' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2024-01-01 00:00:00&#x27;, &#x27;2024-01-01 00:01:00&#x27;,\n", "               &#x27;2024-01-01 00:02:00&#x27;, &#x27;2024-01-01 00:03:00&#x27;,\n", "               &#x27;2024-01-01 00:04:00&#x27;, &#x27;2024-01-01 00:05:00&#x27;,\n", "               &#x27;2024-01-01 00:06:00&#x27;, &#x27;2024-01-01 00:07:00&#x27;,\n", "               &#x27;2024-01-01 00:08:00&#x27;, &#x27;2024-01-01 00:09:00&#x27;,\n", "               ...\n", "               &#x27;2025-01-01 23:50:00&#x27;, &#x27;2025-01-01 23:51:00&#x27;,\n", "               &#x27;2025-01-01 23:52:00&#x27;, &#x27;2025-01-01 23:53:00&#x27;,\n", "               &#x27;2025-01-01 23:54:00&#x27;, &#x27;2025-01-01 23:55:00&#x27;,\n", "               &#x27;2025-01-01 23:56:00&#x27;, &#x27;2025-01-01 23:57:00&#x27;,\n", "               &#x27;2025-01-01 23:58:00&#x27;, &#x27;2025-01-01 23:59:00&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;timestamp&#x27;, length=528480, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>symbol</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-ec705272-a593-4e74-9410-197fb9748dfd' class='xr-index-data-in' type='checkbox'/><label for='index-ec705272-a593-4e74-9410-197fb9748dfd' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;symbol&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ce9916e6-06a2-4bba-a004-9ff70914fff7' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-ce9916e6-06a2-4bba-a004-9ff70914fff7' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 3GB\n", "Dimensions:    (timestamp: 528480, symbol: 7)\n", "Coordinates:\n", "  * timestamp  (timestamp) datetime64[ns] 4MB 2024-01-01 ... 2025-01-01T23:59:00\n", "  * symbol     (symbol) object 56B 'BNBUSDT' 'BTCUSDT' ... 'TRXUSDT' 'XRPUSDT'\n", "Data variables: (12/184)\n", "    KMID       (timestamp, symbol) float32 15MB ...\n", "    KLEN       (timestamp, symbol) float32 15MB ...\n", "    KMID2      (timestamp, symbol) float32 15MB ...\n", "    KUP        (timestamp, symbol) float32 15MB ...\n", "    KUP2       (timestamp, symbol) float32 15MB ...\n", "    KLOW       (timestamp, symbol) float32 15MB ...\n", "    ...         ...\n", "    VSUMN60    (timestamp, symbol) float32 15MB ...\n", "    VSUMD5     (timestamp, symbol) float32 15MB ...\n", "    VSUMD10    (timestamp, symbol) float32 15MB ...\n", "    VSUMD20    (timestamp, symbol) float32 15MB ...\n", "    VSUMD30    (timestamp, symbol) float32 15MB ...\n", "    VSUMD60    (timestamp, symbol) float32 15MB ..."]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 41, "id": "c5afc9ea", "metadata": {}, "outputs": [], "source": ["data = xr.merge([x, xa])"]}, {"cell_type": "code", "execution_count": 42, "id": "6b5440ed", "metadata": {}, "outputs": [], "source": ["data = data.fillna(0)"]}, {"cell_type": "code", "execution_count": 46, "id": "7b9a9037", "metadata": {}, "outputs": [], "source": ["data = data.to_array()"]}, {"cell_type": "code", "execution_count": 60, "id": "5e7382ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.00032962, 0.0011291 , 0.0015966 , ..., 0.        , 0.        ,\n", "       0.        ], shape=(3699360,))"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["data.to_numpy().reshape(data.shape[1] * data.shape[2], -1)[:, -1]"]}, {"cell_type": "code", "execution_count": 62, "id": "15fe96c1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray (variable: 185, timestamp: 528480, symbol: 7)&gt; Size: 5GB\n", "array([[[ 8.45463946e-04,  1.42176566e-03,  4.85467026e-05, ...,\n", "          1.19129766e-03,  4.40730248e-04,  1.39464519e-03],\n", "        [-2.42145528e-04, -2.61916313e-04,  0.00000000e+00, ...,\n", "          3.90557660e-04,  3.34186858e-04, -9.69927874e-04],\n", "        [ 2.33704603e-04,  1.80850679e-04,  6.58093719e-04, ...,\n", "          4.65551944e-04,  3.89472727e-04, -6.49669309e-05],\n", "        ...,\n", "        [ 3.12955794e-03,  3.62918130e-03,  3.34220286e-03, ...,\n", "          4.17410210e-03,  2.98690842e-03,  3.58321983e-03],\n", "        [ 9.44843458e-04,  1.80380797e-04,  5.55379840e-04, ...,\n", "          1.47988880e-03,  0.00000000e+00,  3.21596774e-04],\n", "        [-1.04840640e-02, -1.18818060e-02,  3.41910636e-03, ...,\n", "          5.17815445e-03, -1.24502657e-02, -1.03149684e-02]],\n", "\n", "       [[ 1.41446514e-03,  1.68594718e-03,  2.91316397e-03, ...,\n", "          2.80314754e-03,  1.32219074e-03,  2.96905101e-03],\n", "        [ 4.84291057e-04,  8.70739692e-04,  1.45881390e-03, ...,\n", "          1.65988866e-03,  6.68311492e-04,  2.77119875e-03],\n", "        [ 2.62951129e-04,  5.97561128e-04,  1.55546190e-03, ...,\n", "          1.05806673e-03,  7.78829388e-04,  5.84624941e-04],\n", "...\n", "          2.50994992e-02,  4.56857681e-02,  2.84784436e-02],\n", "        [-8.66984855e-03, -5.33917639e-03, -2.73896214e-02, ...,\n", "         -4.85434057e-03, -8.17755517e-03, -1.79212149e-02],\n", "        [ 1.53366268e-01,  9.90028679e-02,  1.66214824e-01, ...,\n", "          8.79763067e-02,  1.78129196e-01,  1.49079844e-01]],\n", "\n", "       [[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  1.00000000e+00, ...,\n", "          1.00000000e+00,  1.00000000e+00,  1.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  0.00000000e+00, ...,\n", "          1.00000000e+00,  0.00000000e+00,  1.00000000e+00],\n", "        ...,\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00]]],\n", "      shape=(185, 528480, 7))\n", "Coordinates:\n", "  * timestamp  (timestamp) datetime64[ns] 4MB 2024-01-01 ... 2025-01-01T23:59:00\n", "  * symbol     (symbol) object 56B &#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;TRXUSDT&#x27; &#x27;XRPUSDT&#x27;\n", "  * variable   (variable) object 1kB &#x27;KMID&#x27; &#x27;KLEN&#x27; &#x27;KMID2&#x27; ... &#x27;VSUMD60&#x27; &#x27;label&#x27;</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'></div><ul class='xr-dim-list'><li><span class='xr-has-index'>variable</span>: 185</li><li><span class='xr-has-index'>timestamp</span>: 528480</li><li><span class='xr-has-index'>symbol</span>: 7</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-ed3535e6-fba2-430d-a762-91dda7e4c430' class='xr-array-in' type='checkbox' checked><label for='section-ed3535e6-fba2-430d-a762-91dda7e4c430' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>0.0008455 0.001422 4.855e-05 0.0005815 0.001191 ... 0.0 0.0 0.0 0.0</span></div><div class='xr-array-data'><pre>array([[[ 8.45463946e-04,  1.42176566e-03,  4.85467026e-05, ...,\n", "          1.19129766e-03,  4.40730248e-04,  1.39464519e-03],\n", "        [-2.42145528e-04, -2.61916313e-04,  0.00000000e+00, ...,\n", "          3.90557660e-04,  3.34186858e-04, -9.69927874e-04],\n", "        [ 2.33704603e-04,  1.80850679e-04,  6.58093719e-04, ...,\n", "          4.65551944e-04,  3.89472727e-04, -6.49669309e-05],\n", "        ...,\n", "        [ 3.12955794e-03,  3.62918130e-03,  3.34220286e-03, ...,\n", "          4.17410210e-03,  2.98690842e-03,  3.58321983e-03],\n", "        [ 9.44843458e-04,  1.80380797e-04,  5.55379840e-04, ...,\n", "          1.47988880e-03,  0.00000000e+00,  3.21596774e-04],\n", "        [-1.04840640e-02, -1.18818060e-02,  3.41910636e-03, ...,\n", "          5.17815445e-03, -1.24502657e-02, -1.03149684e-02]],\n", "\n", "       [[ 1.41446514e-03,  1.68594718e-03,  2.91316397e-03, ...,\n", "          2.80314754e-03,  1.32219074e-03,  2.96905101e-03],\n", "        [ 4.84291057e-04,  8.70739692e-04,  1.45881390e-03, ...,\n", "          1.65988866e-03,  6.68311492e-04,  2.77119875e-03],\n", "        [ 2.62951129e-04,  5.97561128e-04,  1.55546190e-03, ...,\n", "          1.05806673e-03,  7.78829388e-04,  5.84624941e-04],\n", "...\n", "          2.50994992e-02,  4.56857681e-02,  2.84784436e-02],\n", "        [-8.66984855e-03, -5.33917639e-03, -2.73896214e-02, ...,\n", "         -4.85434057e-03, -8.17755517e-03, -1.79212149e-02],\n", "        [ 1.53366268e-01,  9.90028679e-02,  1.66214824e-01, ...,\n", "          8.79763067e-02,  1.78129196e-01,  1.49079844e-01]],\n", "\n", "       [[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  1.00000000e+00, ...,\n", "          1.00000000e+00,  1.00000000e+00,  1.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  0.00000000e+00, ...,\n", "          1.00000000e+00,  0.00000000e+00,  1.00000000e+00],\n", "        ...,\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00]]],\n", "      shape=(185, 528480, 7))</pre></div></div></li><li class='xr-section-item'><input id='section-dec77032-813c-4f7a-bf81-23b56dba9816' class='xr-section-summary-in' type='checkbox'  checked><label for='section-dec77032-813c-4f7a-bf81-23b56dba9816' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>timestamp</span></div><div class='xr-var-dims'>(timestamp)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2024-01-01 ... 2025-01-01T23:59:00</div><input id='attrs-6ff7736a-d996-48b5-a8cc-faee240a92c1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6ff7736a-d996-48b5-a8cc-faee240a92c1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0386170d-e1bc-4f0a-9b03-9354ea8acdb3' class='xr-var-data-in' type='checkbox'><label for='data-0386170d-e1bc-4f0a-9b03-9354ea8acdb3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2024-01-01T00:00:00.000000000&#x27;, &#x27;2024-01-01T00:01:00.000000000&#x27;,\n", "       &#x27;2024-01-01T00:02:00.000000000&#x27;, ..., &#x27;2025-01-01T23:57:00.000000000&#x27;,\n", "       &#x27;2025-01-01T23:58:00.000000000&#x27;, &#x27;2025-01-01T23:59:00.000000000&#x27;],\n", "      shape=(528480,), dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>symbol</span></div><div class='xr-var-dims'>(symbol)</div><div class='xr-var-dtype'>object</div><div class='xr-var-preview xr-preview'>&#x27;BNBUSDT&#x27; &#x27;BTCUSDT&#x27; ... &#x27;XRPUSDT&#x27;</div><input id='attrs-6675949d-9879-4ba9-bee1-bfc6f481794b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6675949d-9879-4ba9-bee1-bfc6f481794b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d0052840-0da8-4073-9a16-74a436f10a84' class='xr-var-data-in' type='checkbox'><label for='data-d0052840-0da8-4073-9a16-74a436f10a84' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;], dtype=object)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>variable</span></div><div class='xr-var-dims'>(variable)</div><div class='xr-var-dtype'>object</div><div class='xr-var-preview xr-preview'>&#x27;KMID&#x27; &#x27;KLEN&#x27; ... &#x27;VSUMD60&#x27; &#x27;label&#x27;</div><input id='attrs-b717b9cc-12f9-4b4c-b848-1fff82e5af26' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b717b9cc-12f9-4b4c-b848-1fff82e5af26' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-975a6926-c1a0-4254-9c66-9fc560a35780' class='xr-var-data-in' type='checkbox'><label for='data-975a6926-c1a0-4254-9c66-9fc560a35780' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;KMID&#x27;, &#x27;KLEN&#x27;, &#x27;KMID2&#x27;, &#x27;KUP&#x27;, &#x27;KUP2&#x27;, &#x27;KLOW&#x27;, &#x27;KLOW2&#x27;, &#x27;KSFT&#x27;,\n", "       &#x27;KSFT2&#x27;, &#x27;OPEN0&#x27;, &#x27;OPEN1&#x27;, &#x27;OPEN2&#x27;, &#x27;OPEN3&#x27;, &#x27;OPEN4&#x27;, &#x27;HIGH0&#x27;, &#x27;HIGH1&#x27;,\n", "       &#x27;HIGH2&#x27;, &#x27;HIGH3&#x27;, &#x27;HIGH4&#x27;, &#x27;LOW0&#x27;, &#x27;LOW1&#x27;, &#x27;LOW2&#x27;, &#x27;LOW3&#x27;, &#x27;LOW4&#x27;,\n", "       &#x27;CLOSE0&#x27;, &#x27;CLOSE1&#x27;, &#x27;CLOSE2&#x27;, &#x27;CLOSE3&#x27;, &#x27;CLOSE4&#x27;, &#x27;VWAP0&#x27;, &#x27;VWAP1&#x27;,\n", "       &#x27;VWAP2&#x27;, &#x27;VWAP3&#x27;, &#x27;VWAP4&#x27;, &#x27;VOLUME0&#x27;, &#x27;VOLUME1&#x27;, &#x27;VOLUME2&#x27;, &#x27;VOLUME3&#x27;,\n", "       &#x27;VOLUME4&#x27;, &#x27;ROC5&#x27;, &#x27;ROC10&#x27;, &#x27;ROC20&#x27;, &#x27;ROC30&#x27;, &#x27;ROC60&#x27;, &#x27;MA5&#x27;, &#x27;MA10&#x27;,\n", "       &#x27;MA20&#x27;, &#x27;MA30&#x27;, &#x27;MA60&#x27;, &#x27;STD5&#x27;, &#x27;STD10&#x27;, &#x27;STD20&#x27;, &#x27;STD30&#x27;, &#x27;STD60&#x27;,\n", "       &#x27;BETA5&#x27;, &#x27;BETA10&#x27;, &#x27;BETA20&#x27;, &#x27;BETA30&#x27;, &#x27;BETA60&#x27;, &#x27;RSQR5&#x27;, &#x27;RSQR10&#x27;,\n", "       &#x27;RSQR20&#x27;, &#x27;RSQR30&#x27;, &#x27;RSQR60&#x27;, &#x27;RESI5&#x27;, &#x27;RESI10&#x27;, &#x27;RESI20&#x27;, &#x27;RESI30&#x27;,\n", "       &#x27;RESI60&#x27;, &#x27;MAX5&#x27;, &#x27;MAX10&#x27;, &#x27;MAX20&#x27;, &#x27;MAX30&#x27;, &#x27;MAX60&#x27;, &#x27;MIN5&#x27;, &#x27;MIN10&#x27;,\n", "       &#x27;MIN20&#x27;, &#x27;MIN30&#x27;, &#x27;MIN60&#x27;, &#x27;QTLU5&#x27;, &#x27;QTLU10&#x27;, &#x27;QTLU20&#x27;, &#x27;QTLU30&#x27;,\n", "       &#x27;QTLU60&#x27;, &#x27;QTLD5&#x27;, &#x27;QTLD10&#x27;, &#x27;QTLD20&#x27;, &#x27;QTLD30&#x27;, &#x27;QTLD60&#x27;, &#x27;RANK5&#x27;,\n", "       &#x27;RANK10&#x27;, &#x27;RANK20&#x27;, &#x27;RANK30&#x27;, &#x27;RANK60&#x27;, &#x27;RSV5&#x27;, &#x27;RSV10&#x27;, &#x27;RSV20&#x27;,\n", "       &#x27;RSV30&#x27;, &#x27;RSV60&#x27;, &#x27;IMAX5&#x27;, &#x27;IMAX10&#x27;, &#x27;IMAX20&#x27;, &#x27;IMAX30&#x27;, &#x27;IMAX60&#x27;,\n", "       &#x27;IMIN5&#x27;, &#x27;IMIN10&#x27;, &#x27;IMIN20&#x27;, &#x27;IMIN30&#x27;, &#x27;IMIN60&#x27;, &#x27;IMXD5&#x27;, &#x27;IMXD10&#x27;,\n", "       &#x27;IMXD20&#x27;, &#x27;IMXD30&#x27;, &#x27;IMXD60&#x27;, &#x27;CORR5&#x27;, &#x27;CORR10&#x27;, &#x27;CORR20&#x27;, &#x27;CORR30&#x27;,\n", "       &#x27;CORR60&#x27;, &#x27;CORD5&#x27;, &#x27;CORD10&#x27;, &#x27;CORD20&#x27;, &#x27;CORD30&#x27;, &#x27;CORD60&#x27;, &#x27;CNTP5&#x27;,\n", "       &#x27;CNTP10&#x27;, &#x27;CNTP20&#x27;, &#x27;CNTP30&#x27;, &#x27;CNTP60&#x27;, &#x27;CNTN5&#x27;, &#x27;CNTN10&#x27;, &#x27;CNTN20&#x27;,\n", "       &#x27;CNTN30&#x27;, &#x27;CNTN60&#x27;, &#x27;CNTD5&#x27;, &#x27;CNTD10&#x27;, &#x27;CNTD20&#x27;, &#x27;CNTD30&#x27;, &#x27;CNTD60&#x27;,\n", "       &#x27;SUMP5&#x27;, &#x27;SUMP10&#x27;, &#x27;SUMP20&#x27;, &#x27;SUMP30&#x27;, &#x27;SUMP60&#x27;, &#x27;SUMN5&#x27;, &#x27;SUMN10&#x27;,\n", "       &#x27;SUMN20&#x27;, &#x27;SUMN30&#x27;, &#x27;SUMN60&#x27;, &#x27;SUMD5&#x27;, &#x27;SUMD10&#x27;, &#x27;SUMD20&#x27;, &#x27;SUMD30&#x27;,\n", "       &#x27;SUMD60&#x27;, &#x27;VMA5&#x27;, &#x27;VMA10&#x27;, &#x27;VMA20&#x27;, &#x27;VMA30&#x27;, &#x27;VMA60&#x27;, &#x27;VSTD5&#x27;, &#x27;VSTD10&#x27;,\n", "       &#x27;VSTD20&#x27;, &#x27;VSTD30&#x27;, &#x27;VSTD60&#x27;, &#x27;WVMA5&#x27;, &#x27;WVMA10&#x27;, &#x27;WVMA20&#x27;, &#x27;WVMA30&#x27;,\n", "       &#x27;WVMA60&#x27;, &#x27;VSUMP5&#x27;, &#x27;VSUMP10&#x27;, &#x27;VSUMP20&#x27;, &#x27;VSUMP30&#x27;, &#x27;VSUMP60&#x27;,\n", "       &#x27;VSUMN5&#x27;, &#x27;VSUMN10&#x27;, &#x27;VSUMN20&#x27;, &#x27;VSUMN30&#x27;, &#x27;VSUMN60&#x27;, &#x27;VSUMD5&#x27;,\n", "       &#x27;VSUMD10&#x27;, &#x27;VSUMD20&#x27;, &#x27;VSUMD30&#x27;, &#x27;VSUMD60&#x27;, &#x27;label&#x27;], dtype=object)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-e6a207ef-eda2-47b6-8d09-0c6842fda5f6' class='xr-section-summary-in' type='checkbox'  ><label for='section-e6a207ef-eda2-47b6-8d09-0c6842fda5f6' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>timestamp</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-3bc5d93a-c2a8-47c0-9039-f900cfe35ae4' class='xr-index-data-in' type='checkbox'/><label for='index-3bc5d93a-c2a8-47c0-9039-f900cfe35ae4' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2024-01-01 00:00:00&#x27;, &#x27;2024-01-01 00:01:00&#x27;,\n", "               &#x27;2024-01-01 00:02:00&#x27;, &#x27;2024-01-01 00:03:00&#x27;,\n", "               &#x27;2024-01-01 00:04:00&#x27;, &#x27;2024-01-01 00:05:00&#x27;,\n", "               &#x27;2024-01-01 00:06:00&#x27;, &#x27;2024-01-01 00:07:00&#x27;,\n", "               &#x27;2024-01-01 00:08:00&#x27;, &#x27;2024-01-01 00:09:00&#x27;,\n", "               ...\n", "               &#x27;2025-01-01 23:50:00&#x27;, &#x27;2025-01-01 23:51:00&#x27;,\n", "               &#x27;2025-01-01 23:52:00&#x27;, &#x27;2025-01-01 23:53:00&#x27;,\n", "               &#x27;2025-01-01 23:54:00&#x27;, &#x27;2025-01-01 23:55:00&#x27;,\n", "               &#x27;2025-01-01 23:56:00&#x27;, &#x27;2025-01-01 23:57:00&#x27;,\n", "               &#x27;2025-01-01 23:58:00&#x27;, &#x27;2025-01-01 23:59:00&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;timestamp&#x27;, length=528480, freq=&#x27;min&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>symbol</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-13f6eb17-c067-4447-a3ca-1288435caf77' class='xr-index-data-in' type='checkbox'/><label for='index-13f6eb17-c067-4447-a3ca-1288435caf77' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;BNBUSDT&#x27;, &#x27;BTCUSDT&#x27;, &#x27;DOGEUSDT&#x27;, &#x27;ETHUSDT&#x27;, &#x27;SOLUSDT&#x27;, &#x27;TRXUSDT&#x27;,\n", "       &#x27;XRPUSDT&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;symbol&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>variable</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-4dc450b1-b326-4b9b-a162-f1f9a75f08f8' class='xr-index-data-in' type='checkbox'/><label for='index-4dc450b1-b326-4b9b-a162-f1f9a75f08f8' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;KMID&#x27;, &#x27;KLEN&#x27;, &#x27;KMID2&#x27;, &#x27;KUP&#x27;, &#x27;KUP2&#x27;, &#x27;KLOW&#x27;, &#x27;KLOW2&#x27;, &#x27;KSFT&#x27;,\n", "       &#x27;KSFT2&#x27;, &#x27;OPEN0&#x27;,\n", "       ...\n", "       &#x27;VSUMN10&#x27;, &#x27;VSUMN20&#x27;, &#x27;VSUMN30&#x27;, &#x27;VSUMN60&#x27;, &#x27;VSUMD5&#x27;, &#x27;VSUMD10&#x27;,\n", "       &#x27;VSUMD20&#x27;, &#x27;VSUMD30&#x27;, &#x27;VSUMD60&#x27;, &#x27;label&#x27;],\n", "      dtype=&#x27;object&#x27;, name=&#x27;variable&#x27;, length=185))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-fda09b20-8efe-463f-b5c1-5486b32dab29' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-fda09b20-8efe-463f-b5c1-5486b32dab29' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray (variable: 185, timestamp: 528480, symbol: 7)> Size: 5GB\n", "array([[[ 8.45463946e-04,  1.42176566e-03,  4.85467026e-05, ...,\n", "          1.19129766e-03,  4.40730248e-04,  1.39464519e-03],\n", "        [-2.42145528e-04, -2.61916313e-04,  0.00000000e+00, ...,\n", "          3.90557660e-04,  3.34186858e-04, -9.69927874e-04],\n", "        [ 2.33704603e-04,  1.80850679e-04,  6.58093719e-04, ...,\n", "          4.65551944e-04,  3.89472727e-04, -6.49669309e-05],\n", "        ...,\n", "        [ 3.12955794e-03,  3.62918130e-03,  3.34220286e-03, ...,\n", "          4.17410210e-03,  2.98690842e-03,  3.58321983e-03],\n", "        [ 9.44843458e-04,  1.80380797e-04,  5.55379840e-04, ...,\n", "          1.47988880e-03,  0.00000000e+00,  3.21596774e-04],\n", "        [-1.04840640e-02, -1.18818060e-02,  3.41910636e-03, ...,\n", "          5.17815445e-03, -1.24502657e-02, -1.03149684e-02]],\n", "\n", "       [[ 1.41446514e-03,  1.68594718e-03,  2.91316397e-03, ...,\n", "          2.80314754e-03,  1.32219074e-03,  2.96905101e-03],\n", "        [ 4.84291057e-04,  8.70739692e-04,  1.45881390e-03, ...,\n", "          1.65988866e-03,  6.68311492e-04,  2.77119875e-03],\n", "        [ 2.62951129e-04,  5.97561128e-04,  1.55546190e-03, ...,\n", "          1.05806673e-03,  7.78829388e-04,  5.84624941e-04],\n", "...\n", "          2.50994992e-02,  4.56857681e-02,  2.84784436e-02],\n", "        [-8.66984855e-03, -5.33917639e-03, -2.73896214e-02, ...,\n", "         -4.85434057e-03, -8.17755517e-03, -1.79212149e-02],\n", "        [ 1.53366268e-01,  9.90028679e-02,  1.66214824e-01, ...,\n", "          8.79763067e-02,  1.78129196e-01,  1.49079844e-01]],\n", "\n", "       [[ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  1.00000000e+00, ...,\n", "          1.00000000e+00,  1.00000000e+00,  1.00000000e+00],\n", "        [ 1.00000000e+00,  1.00000000e+00,  0.00000000e+00, ...,\n", "          1.00000000e+00,  0.00000000e+00,  1.00000000e+00],\n", "        ...,\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00],\n", "        [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, ...,\n", "          0.00000000e+00,  0.00000000e+00,  0.00000000e+00]]],\n", "      shape=(185, 528480, 7))\n", "Coordinates:\n", "  * timestamp  (timestamp) datetime64[ns] 4MB 2024-01-01 ... 2025-01-01T23:59:00\n", "  * symbol     (symbol) object 56B 'BNBUSDT' 'BTCUSDT' ... 'TRXUSDT' 'XRPUSDT'\n", "  * variable   (variable) object 1kB 'KMID' 'KLEN' 'KMID2' ... 'VSUMD60' 'label'"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 67, "id": "2b75e2c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(185, 528480, 7)"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c4ceca68", "metadata": {}, "outputs": [], "source": ["data = data.stack(z=(\"symbol\", \"timestamp\")).transpose(\"variable\", \"z\")"]}, {"cell_type": "code", "execution_count": 88, "id": "a28b16a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["(185, 3699360)"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": 90, "id": "bc15022e", "metadata": {}, "outputs": [], "source": ["y = data.sel(variable='label')"]}, {"cell_type": "code", "execution_count": 91, "id": "dd5b93e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3699360,)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["y.shape"]}, {"cell_type": "code", "execution_count": 98, "id": "4fc719d1", "metadata": {}, "outputs": [], "source": ["x = data.drop_sel(variable='label')"]}, {"cell_type": "code", "execution_count": 99, "id": "cce9ada9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(184, 3699360)"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape"]}, {"cell_type": "code", "execution_count": 114, "id": "9fca6273", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "# 替换或删除这些值\n", "x = np.nan_to_num(x, posinf=1e10, neginf=-1e10)  # 替换inf\n", "x = np.clip(x, -1e10, 1e10)  # 限制极大值范围"]}, {"cell_type": "code", "execution_count": 95, "id": "1fa58ff1", "metadata": {}, "outputs": [], "source": ["y = y.to_numpy()"]}, {"cell_type": "code", "execution_count": 100, "id": "5ca8db62", "metadata": {}, "outputs": [], "source": ["x = x.to_numpy()"]}, {"cell_type": "code", "execution_count": 105, "id": "c2b536b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(184, 3699360)"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape"]}, {"cell_type": "code", "execution_count": 107, "id": "62b14705", "metadata": {}, "outputs": [], "source": ["x = x.transpose(-1 , 0)"]}, {"cell_type": "code", "execution_count": 106, "id": "789e0d05", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3699360,)"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["y.shape"]}, {"cell_type": "code", "execution_count": 101, "id": "331ad97e", "metadata": {}, "outputs": [], "source": ["import xgboost as xgb"]}, {"cell_type": "code", "execution_count": 115, "id": "b629b31f", "metadata": {}, "outputs": [], "source": ["model = xgb.XGBClassifier(device='cuda')"]}, {"cell_type": "code", "execution_count": 117, "id": "2ffc26a3", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=&#x27;cuda&#x27;, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=None,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>XGBClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=&#x27;cuda&#x27;, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=None,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div>"], "text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device='cuda', early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=None,\n", "              n_jobs=None, num_parallel_tree=None, ...)"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(x, y)"]}, {"cell_type": "code", "execution_count": 119, "id": "fd4238b4", "metadata": {}, "outputs": [], "source": ["pred = model.predict(x)"]}, {"cell_type": "code", "execution_count": 120, "id": "00a4376c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3699360,)"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["pred.shape"]}, {"cell_type": "code", "execution_count": 121, "id": "99a9513c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3699360,)"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["y.shape"]}, {"cell_type": "code", "execution_count": 123, "id": "5197aac9", "metadata": {}, "outputs": [], "source": ["y_true = y\n", "y_pred = pred"]}, {"cell_type": "code", "execution_count": 124, "id": "c25b8476", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准确率(Accuracy): 0.5583\n", "精确率(Precision): 0.5689\n", "召回率(Recall): 0.0942\n", "F1分数: 0.1616\n"]}], "source": ["from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "\n", "# 假设 y_true 是真实值，y_pred 是预测值\n", "accuracy = accuracy_score(y_true, y_pred)\n", "precision = precision_score(y_true, y_pred)\n", "recall = recall_score(y_true, y_pred)\n", "f1 = f1_score(y_true, y_pred)\n", "\n", "print(f\"准确率(Accuracy): {accuracy:.4f}\")\n", "print(f\"精确率(Precision): {precision:.4f}\")\n", "print(f\"召回率(<PERSON>call): {recall:.4f}\")\n", "print(f\"F1分数: {f1:.4f}\")"]}, {"cell_type": "code", "execution_count": 125, "id": "4f38281c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "         0.0       0.56      0.94      0.70   2027245\n", "         1.0       0.57      0.09      0.16   1672115\n", "\n", "    accuracy                           0.56   3699360\n", "   macro avg       0.56      0.52      0.43   3699360\n", "weighted avg       0.56      0.56      0.46   3699360\n", "\n"]}], "source": ["from sklearn.metrics import classification_report\n", "\n", "print(classification_report(y_true, y_pred))"]}, {"cell_type": "code", "execution_count": null, "id": "0d9babd9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}