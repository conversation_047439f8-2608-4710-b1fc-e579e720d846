# XGBoost时间序列预测模型

基于Alpha158因子和SpotKlineDataset的XGBoost时间序列预测模型，支持CUDA加速和时间序列K折交叉验证。

## 功能特性

### 数据预处理 (`utils/preprocess.py`)
- **DataPreprocessor类**：专门处理xarray Dataset格式的因子数据
- **缺失值处理**：支持前向填充、后向填充、插值、常数填充、均值/中位数填充
- **异常值检测**：支持IQR、Z-score、MAD方法
- **特征标准化**：支持标准化、最小-最大归一化、鲁棒标准化
- **按symbol处理**：所有预处理操作都按symbol维度进行，确保不同资产的数据独立处理
- **特征EDA**：提供特征分布图、相关性矩阵等可视化功能

### XGBoost模型 (`models/xgboost_predictor.py`)
- **XGBoostPredictor类**：完整的XGBoost时间序列预测解决方案
- **CUDA加速**：支持GPU加速训练
- **时间序列交叉验证**：使用TimeSeriesSplit进行时间序列K折交叉验证
- **数据对齐**：自动对齐Alpha158因子数据和标签数据
- **模型评估**：提供准确率、精确率、召回率、F1分数、AUC等指标
- **特征重要性**：计算和可视化特征重要性
- **模型保存/加载**：支持模型的保存和加载

## 安装依赖

```bash
pip install xgboost scikit-learn matplotlib seaborn
```

如果要使用GPU加速，确保安装了CUDA版本的XGBoost：
```bash
pip install xgboost[gpu]
```

## 使用方法

### 1. 快速开始

运行示例脚本：
```bash
python example_xgboost_usage.py
```

### 2. 完整训练流程

运行完整的训练脚本：
```bash
python train_xgboost_predictor.py --start_date 2024-01-01 --end_date 2024-12-31 --use_gpu --n_splits 5
```

参数说明：
- `--start_date`: 开始日期 (YYYY-MM-DD)
- `--end_date`: 结束日期 (YYYY-MM-DD)
- `--use_gpu`: 是否使用GPU加速
- `--n_splits`: 交叉验证折数
- `--output_dir`: 结果输出目录
- `--preprocess_method`: 数据预处理方法 (standard/minmax/robust)

### 3. 自定义使用

```python
from utils.preprocess import DataPreprocessor
from models.xgboost_predictor import XGBoostPredictor
from dataset.spot import SpotKlineDataset
from factor.alpha158 import Alpha158SpotKline
import config

# 1. 加载数据
spot_dataset = SpotKlineDataset(config.spot_kline_config("2024-01-01", "2024-12-31"))
spot_data = spot_dataset.get()

alpha158_factor = Alpha158SpotKline(config.alpha158_config("2024-01-01", "2024-12-31"))
factor_data = alpha158_factor.get()

# 2. 数据预处理
preprocessor = DataPreprocessor(factor_data)
processed_data = (preprocessor
                 .handle_missing_values(method='forward_fill')
                 .remove_outliers(method='iqr', threshold=3.0)
                 .normalize_features(method='standard', per_symbol=True)
                 .get_processed_data())

# 3. 创建标签
labels = spot_data.with_columns(
    pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))
    .then(1)
    .otherwise(0)
    .alias('label')
    .fill_null(0)
).select(['symbol', 'Open time', 'label']).rename({'Open time': 'timestamp'}).collect()

# 4. 训练模型
predictor = XGBoostPredictor(use_gpu=True, n_splits=5)
X, y, timestamps, symbols = predictor.align_data(processed_data, labels)
feature_names = list(processed_data.data_vars)

results = predictor.train_with_cv(X, y, timestamps, feature_names)

# 5. 预测
predictions = predictor.predict(X_test, feature_names)
```

## 数据预处理选项

### 缺失值处理方法
- `forward_fill`: 前向填充
- `backward_fill`: 后向填充
- `interpolate`: 线性插值
- `constant`: 常数填充
- `mean`: 均值填充（按symbol）
- `median`: 中位数填充（按symbol）

### 异常值检测方法
- `iqr`: 四分位距方法
- `zscore`: Z分数方法
- `mad`: 中位数绝对偏差方法

### 标准化方法
- `standard`: Z-score标准化
- `minmax`: 最小-最大归一化
- `robust`: 鲁棒标准化（基于中位数和MAD）

## 输出文件

训练完成后会生成以下文件：
- `xgboost_model_*.json`: 训练好的XGBoost模型
- `feature_importance_*.csv`: 特征重要性排序
- `feature_importance_*.png`: 特征重要性可视化图
- `training_results_*.json`: 详细的训练结果
- `model_summary_*.json`: 模型摘要信息

## 性能优化建议

1. **GPU加速**: 如果有NVIDIA GPU，建议启用GPU加速
2. **内存优化**: 对于大数据集，可以考虑分批处理
3. **特征选择**: 使用特征重要性进行特征选择，减少计算量
4. **参数调优**: 根据具体数据调整XGBoost参数

## 注意事项

1. **时间序列特性**: 模型使用TimeSeriesSplit确保不会发生数据泄露
2. **按symbol处理**: 所有预处理都按symbol进行，确保不同资产的独立性
3. **数据对齐**: 自动处理因子数据和标签数据的时间戳对齐
4. **GPU内存**: 使用GPU时注意显存限制，必要时减少batch size

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多的特征工程方法
- 支持多分类问题
- 添加模型集成方法
- 实现在线学习功能
- 添加更多的评估指标
