# StreamContext Segmentation Fault Issue

## Problem Description

`kr.<PERSON><PERSON>ontext(executor, modu, 8)` returns a stream_context object that cannot be:

1. **Assigned to variables through function returns** - causes segmentation fault
2. **Stored as class attributes and accessed via `class_name.stream.pushData()`** - causes segmentation fault

Both scenarios result in `segmentation fault (core dumped)` when attempting to call methods on the StreamContext object.

## Environment

- **Library**: KunQuant.runner.KunRunner
- **Method**: `kr.StreamContext(executor, modu, buffer_size)`
- **Error**: Segmentation fault (core dumped)

## Expected Behavior

StreamContext objects should be able to be:
- Returned from functions and assigned to variables
- Stored as class attributes and accessed normally
- Used in the same way as directly created instances

## Actual Behavior

- **Direct creation**: Works fine
- **Function return assignment**: Segmentation fault on method calls
- **Class attribute storage**: Segmentation fault on method calls

## Minimal Reproducible Example

See `streamcontext_issue_example.py` for a complete reproduction script.

### Scenario 1: Direct Creation (WORKS)
```python
# This works fine
lib = kr.Library.load("path/to/alpha101_stream.so")
modu = lib.getModule("alpha101_stream")
executor = kr.createSingleThreadExecutor()
stream = kr.StreamContext(executor, modu, 8)

# No segfault
stream.pushData(buffer_id, data)
stream.run()
```

### Scenario 2: Function Return (SEGFAULT)
```python
def create_stream():
    lib = kr.Library.load("path/to/alpha101_stream.so")
    modu = lib.getModule("alpha101_stream")
    executor = kr.createSingleThreadExecutor()
    return kr.StreamContext(executor, modu, 8)

# This causes segfault when calling methods
stream = create_stream()
stream.pushData(buffer_id, data)  # SEGFAULT HERE
```

### Scenario 3: Class Attribute (SEGFAULT)
```python
class Test:
    def __init__(self):
        self.stream = None
    
    def create_stream(self):
        lib = kr.Library.load("path/to/alpha101_stream.so")
        modu = lib.getModule("alpha101_stream")
        executor = kr.createSingleThreadExecutor()
        self.stream = kr.StreamContext(executor, modu, 8)

test = Test()
test.create_stream()
test.stream.pushData(buffer_id, data)  # SEGFAULT HERE
```

## Code Reference

Based on working code in `tf.py` lines 67-75 (commented out working version):
```python
lib = kr.Library.load(
    str(
        "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
    )
)
modu = lib.getModule("alpha101_stream")
executor = kr.createMultiThreadExecutor(32)
stream = kr.StreamContext(executor, modu, 8)
```

And problematic code in `tf.py` lines 42-64:
```python
class Test:
    def __init__(self) -> None:
        self.stream = None

    def create_stream(self):
        lib = kr.Library.load(str("path/to/alpha101_stream.so"))
        modu = lib.getModule("alpha101_stream")
        executor = kr.createSingleThreadExecutor()
        stream = kr.StreamContext(executor, modu, 8)
        return stream

s = Test()
stream = s.create_stream()  # This assignment leads to segfault
```

## Reproduction Steps

1. Run `python streamcontext_issue_example.py`
2. Observe that direct creation works
3. Observe segmentation fault when using function return or class attribute approaches

## Possible Causes

This appears to be a memory management issue where:
- The underlying C++ objects (executor, module) may be getting destroyed when going out of scope
- The StreamContext may have dependencies on objects that are garbage collected
- There may be reference counting issues in the Python bindings

## Workaround

Currently, the only working approach is direct creation of StreamContext in the same scope where it will be used, without passing through functions or storing in class attributes.

## Impact

This limitation makes it difficult to:
- Create reusable StreamContext factory functions
- Encapsulate StreamContext in classes for better code organization
- Write modular, maintainable code with proper separation of concerns
