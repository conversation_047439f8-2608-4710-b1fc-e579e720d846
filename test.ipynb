from dataset.spot import SpotKlineDataset
from config import alpha101_config, alpha158_config, spot_kline_config
from factor.alpha101 import Alpha101SpotKline
from factor.alpha158 import Alpha158SpotKline


ds = SpotKlineDataset(spot_kline_config())

ds.read().get_data().collect()

alpha101 = Alpha101SpotKline(alpha101_config('2025-01-01'))

alpha101.init_stream(['alpha001'], 8)

alpha101.get_stream_context()

alpha101.calc().to_xarray()

alpha101.make_stream()

stream = alpha101.calc_stream()

alpha101.calc().from_kunquant()

alpha101.read().collect()

alpha101.get_data()

alpha101.data

alpha101.get()

alpha158 = Alpha158SpotKline(alpha158_config())

ds = alpha158.get()  # type: ignore

d

ds

df = ds.to_dataframe()

import polars as pl

(pl.lit('2021-01-01').dt.strftime() > pl.lit('2021-01-01').alias('datetime'))

import pandas as pd

pl.lit(pd.to_datetime('2021-01-01'))

from KunQuant.Driver import KunCompilerConfig
from KunQuant.jit import cfake
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined.Alpha101 import AllData, all_alpha
from KunQuant.runner import KunRunner as kr
import sys


def check_alpha101_stream():
    builder = Builder()
    cnt = 0
    with builder:
        all_data = AllData(
            low=Input("low"),
            high=Input("high"),
            close=Input("close"),
            open=Input("open"),
            amount=Input("amount"),
            volume=Input("volume"),
        )
        for f in all_alpha:
            out = f(all_data)
            Output(out, f.__name__)
            cnt += 1
    simd_len = 8
    f = Function(builder.ops)
    return (
        "alpha_101_stream",
        f,
        KunCompilerConfig(
            blocking_len=simd_len,
            partition_factor=8,
            output_layout="STREAM",
            options={"opt_reduce": False, "fast_log": True},
        ),
    )


cfake.compileit(
    [check_alpha101_stream()],
    "alpha101_stream",
    cfake.CppCompilerConfig(),
    tempdir=sys.argv[1],
    keep_files=True,
)

import numpy as np

np.ndarray((3,))

