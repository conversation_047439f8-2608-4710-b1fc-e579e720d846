import polars as pl
import pandas as pd
from prefect import task, flow
from pathlib import Path
import numpy as np
from loguru import logger

from base.data import DatasetPl
from base.config import DatasetConfig
from enums.data import BinanceCSVHeaders
from utils.file import file_date_filter, get_csv_files
from utils.preprocess import clean_data


class SpotKlineDataset(DatasetPl):
    def __init__(self, dataset_config: DatasetConfig):
        self.config = dataset_config

    @staticmethod
    def _spot_kline_to_df(csv_file: Path, before_2025: bool) -> pl.LazyFrame:
        df = pl.scan_csv(
            str(csv_file), has_header=False, new_columns=BinanceCSVHeaders.SPOT
        )

        if before_2025:
            df = df.with_columns(
                pl.from_epoch("Open time", time_unit="ms")
                .dt.cast_time_unit("us")
                .alias("Open time")
            )
        else:
            df = df.with_columns(
                pl.from_epoch("Open time", time_unit="us").alias("Open time")
            )
        df = df.with_columns(
            pl.lit(str(csv_file.name).split("-")[0]).alias("symbol"),
            pl.col("Open time").dt.month().alias("month"),
            pl.col("Open time").dt.year().alias("year"),
        )
        return df

    def from_csv(self):
        logger.info(f"Reading CSV files from {self.config.csv_dir_path}")

        csv_files = get_csv_files(self.config.csv_dir_path)
        csv_files = file_date_filter(
            csv_files, start_date=self.config.start_date, end_date=self.config.end_date
        )
        csv_before_2025 = file_date_filter(csv_files, end_date="2024-12-31")
        csv_after_2025 = file_date_filter(csv_files, start_date="2025-01-01")

        dfs: list[pl.LazyFrame] = []
        if csv_after_2025:
            dfs.extend(
                self._spot_kline_to_df(csv, before_2025=False) for csv in csv_after_2025
            )
        if csv_before_2025:
            dfs.extend(
                self._spot_kline_to_df(csv, before_2025=True) for csv in csv_before_2025
            )
        if not dfs:
            raise ValueError(
                f"在文件夹 {self.config.csv_dir_path} 中未发现符合要求的 CSV 文件"
            )

        res = pl.concat(dfs)
        res = res.sort(by=["Open time", "symbol"])
        self.data = res
        return self

    def to_kunquant(self) -> tuple[dict, list[str], pl.Series]:
        logger.info("Transforming data to KunQuant format...")

        df = self.data
        df = df.rename(
            mapping={
                "Quote asset volume": "amount",
                "Open time": "timestamp",
                "Open": "open",
                "High": "high",
                "Low": "low",
                "Close": "close",
                "Volume": "volume",
            }
        )
        df = df.drop(
            [
                "Close time",
                "Number of trades",
                "Taker buy base asset volume",
                "Taker buy quote asset volume",
                "Ignore",
            ]
        )
        df = clean_data(df)

        data = df.collect()
        timestamp = data["timestamp"].unique()
        columns = ["open", "high", "low", "close", "volume", "amount"]
        input_dict = {}
        symbols = []
        for value in columns:
            pivot_df = data.pivot(
                index="symbol",
                values=value,
                on="timestamp",
            )
            symbols = pivot_df["symbol"].to_list()
            pivot_df = pivot_df.select(pl.exclude("symbol"))
            df_numpy = pivot_df.to_numpy().astype(np.float32)

            # 对shape[0]不为8的倍数的补0至8的倍数
            if df_numpy.shape[0] % 8 != 0:
                zero_row = np.zeros(
                    (8 - df_numpy.shape[0] % 8, df_numpy.shape[1]), dtype=np.float32
                )
                df_numpy = np.vstack((df_numpy, zero_row))

            # [stocks, time] => [stocks//8, 8, time] => [stocks//8, time, 8]
            df_numpy = df_numpy.reshape(
                (df_numpy.shape[0] // 8, 8, df_numpy.shape[1])
            ).transpose((0, 2, 1))
            input_dict[value] = np.ascontiguousarray(df_numpy)

        return input_dict, symbols, timestamp
