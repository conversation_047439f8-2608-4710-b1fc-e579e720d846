from KunQuant.jit import cfake
from KunQuant.Driver import KunCompilerConfig
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined import Alpha101, Alpha158
from KunQuant.jit import cfake
from KunQuant.Driver import KunCompilerConfig
import KunQuant.runner.KunRunner as kr
from KunQuant.Stage import *
from KunQuant.ops import *
import numpy as np


# 1. 定义一个简单的因子计算
def create_simple_stream_factor():
    builder = Builder()
    with builder:
        inp = Input("close")
        # 简单的移动平均
        avg = WindowedAvg(inp, 5)
        Output(avg, "ma5")
    return Function(builder.ops)


# 2. 编译为流模式
def compile_stream_factor():
    f = create_simple_stream_factor()
    return cfake.compileit(
        [
            (
                "stream_factor",
                f,
                KunCompilerConfig(
                    input_layout="STREAM",
                    output_layout="STREAM",
                    options={"opt_reduce": False, "fast_log": True},
                ),
            )
        ],
        "stream_lib",
        cfake.CppCompilerConfig(),
    )


# 3. 流模式测试函数
def test_stream_mode():
    # 编译因子
    lib = compile_stream_factor()
    modu = lib.getModule("stream_factor")

    # 创建执行器
    executor = kr.createMultiThreadExecutor(64)

    # 测试参数
    num_stock = 8  # 必须是SIMD长度的倍数
    num_time = 50000

    # 准备输入数据
    input_data = {"close": np.random.rand(num_time, num_stock).astype("float32")}

    # 创建流上下文
    stream = kr.StreamContext(executor, modu, num_stock)

    # 获取缓冲区句柄
    close_handle = stream.queryBufferHandle("close")
    ma5_handle = stream.queryBufferHandle("ma5")

    # 准备输出数组
    outputs = np.empty((num_time, num_stock), dtype="float32")

    # 逐时间点推送数据并计算
    for t in range(num_time):
        # 推送当前时间点的数据
        stream.pushData(close_handle, input_data["close"][t])

        # 执行计算
        stream.run()

        # 获取结果
        outputs[t, :] = stream.getCurrentBuffer(ma5_handle)

    print(f"流模式计算完成，输出形状: {outputs.shape}")
    print(f"前5个时间点的结果:\n{outputs[:5]}")
    print(f"后5个时间点的结果:\n{outputs[-5:]}")

    return outputs


if __name__ == "__main__":
    test_stream_mode()
