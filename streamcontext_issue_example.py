#!/usr/bin/env python3
"""
Minimal reproducible example for StreamContext segmentation fault issue.

Issue: kr.StreamContext(executor, modu, 8) returns a stream_context that cannot be:
1. Assigned to variables through function returns
2. Stored as class attributes and accessed via class_name.stream.pushData()

Both scenarios result in segmentation fault (core dumped).
"""

import KunQuant.runner.KunRunner as kr
import numpy as np


def create_mock_data():
    """Create mock data for testing"""
    np.random.seed(42)
    return {
        "high": np.random.rand(10).astype("float32"),
        "low": np.random.rand(10).astype("float32"), 
        "close": np.random.rand(10).astype("float32"),
        "open": np.random.rand(10).astype("float32"),
        "volume": np.random.rand(10).astype("float32"),
        "amount": np.random.rand(10).astype("float32"),
    }


class StreamContextTest:
    """Test class to demonstrate StreamContext issues"""
    
    def __init__(self):
        self.stream = None
        self.buffer_name_to_id = {}
    
    def create_stream_as_attribute(self):
        """Scenario 1: Store StreamContext as class attribute - CAUSES SEGFAULT"""
        lib = kr.Library.load(
            "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
        )
        modu = lib.getModule("alpha101_stream")
        executor = kr.createSingleThreadExecutor()
        
        # This assignment causes segfault when accessing later
        self.stream = kr.StreamContext(executor, modu, 8)
        
        # Initialize buffer handles
        for name in ["high", "low", "close", "open", "volume", "amount"]:
            self.buffer_name_to_id[name] = self.stream.queryBufferHandle(name)
    
    def create_stream_via_function(self):
        """Scenario 2: Return StreamContext from function - CAUSES SEGFAULT"""
        def _create_stream():
            lib = kr.Library.load(
                "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
            )
            modu = lib.getModule("alpha101_stream")
            executor = kr.createSingleThreadExecutor()
            return kr.StreamContext(executor, modu, 8)
        
        # This assignment from function return causes segfault
        stream = _create_stream()
        return stream
    
    def test_attribute_access(self):
        """Test accessing StreamContext stored as attribute"""
        print("Testing StreamContext as class attribute...")
        self.create_stream_as_attribute()
        
        mock_data = create_mock_data()
        
        try:
            # This line causes segmentation fault
            self.stream.pushData(self.buffer_name_to_id["high"], mock_data["high"])
            self.stream.pushData(self.buffer_name_to_id["low"], mock_data["low"])
            self.stream.pushData(self.buffer_name_to_id["close"], mock_data["close"])
            self.stream.pushData(self.buffer_name_to_id["open"], mock_data["open"])
            self.stream.pushData(self.buffer_name_to_id["volume"], mock_data["volume"])
            self.stream.pushData(self.buffer_name_to_id["amount"], mock_data["amount"])
            
            self.stream.run()
            print("SUCCESS: Attribute access worked")
            
        except Exception as e:
            print(f"ERROR in attribute access: {e}")
    
    def test_function_return(self):
        """Test using StreamContext returned from function"""
        print("Testing StreamContext from function return...")
        
        try:
            stream = self.create_stream_via_function()
            
            # Initialize buffer handles
            buffer_name_to_id = {}
            for name in ["high", "low", "close", "open", "volume", "amount"]:
                buffer_name_to_id[name] = stream.queryBufferHandle(name)
            
            mock_data = create_mock_data()
            
            # This line causes segmentation fault
            stream.pushData(buffer_name_to_id["high"], mock_data["high"])
            stream.pushData(buffer_name_to_id["low"], mock_data["low"])
            stream.pushData(buffer_name_to_id["close"], mock_data["close"])
            stream.pushData(buffer_name_to_id["open"], mock_data["open"])
            stream.pushData(buffer_name_to_id["volume"], mock_data["volume"])
            stream.pushData(buffer_name_to_id["amount"], mock_data["amount"])
            
            stream.run()
            print("SUCCESS: Function return worked")
            
        except Exception as e:
            print(f"ERROR in function return: {e}")


def test_direct_creation():
    """Scenario 3: Direct creation (WORKING) - for comparison"""
    print("Testing direct StreamContext creation...")
    
    try:
        # Direct creation - this works fine
        lib = kr.Library.load(
            "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
        )
        modu = lib.getModule("alpha101_stream")
        executor = kr.createSingleThreadExecutor()
        stream = kr.StreamContext(executor, modu, 8)
        
        # Initialize buffer handles
        buffer_name_to_id = {}
        for name in ["high", "low", "close", "open", "volume", "amount"]:
            buffer_name_to_id[name] = stream.queryBufferHandle(name)
        
        mock_data = create_mock_data()
        
        # This works without segfault
        stream.pushData(buffer_name_to_id["high"], mock_data["high"])
        stream.pushData(buffer_name_to_id["low"], mock_data["low"])
        stream.pushData(buffer_name_to_id["close"], mock_data["close"])
        stream.pushData(buffer_name_to_id["open"], mock_data["open"])
        stream.pushData(buffer_name_to_id["volume"], mock_data["volume"])
        stream.pushData(buffer_name_to_id["amount"], mock_data["amount"])
        
        stream.run()
        print("SUCCESS: Direct creation worked")
        
    except Exception as e:
        print(f"ERROR in direct creation: {e}")


if __name__ == "__main__":
    print("=" * 60)
    print("StreamContext Segmentation Fault Reproduction")
    print("=" * 60)
    
    # Test direct creation (should work)
    test_direct_creation()
    print()
    
    # Test problematic scenarios
    test_instance = StreamContextTest()
    
    # Test 1: StreamContext as class attribute
    test_instance.test_attribute_access()
    print()
    
    # Test 2: StreamContext from function return
    test_instance.test_function_return()
    
    print("=" * 60)
    print("Expected: Direct creation works, others cause segfault")
    print("=" * 60)
