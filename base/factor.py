from abc import abstractmethod, ABC
import polars as pl
import pandas as pd
import xarray as xr
from loguru import logger

# from prefect import task, flow
from typing import Self
from pathlib import Path
import numpy as np

from base.config import FactorConfig
from enums.constant import Date


class FactorKunQuant(ABC):

    @property
    def data(self) -> xr.Dataset | pl.LazyFrame:
        return self._data

    def _pl_filter_by_date(self, data: pl.LazyFrame, col: str):
        return data.filter(
            pl.col(col).is_between(
                pl.lit(pd.to_datetime(self.config.start_date)),  # type: ignore
                pl.lit(pd.to_datetime(self.config.start_date)),  # type: ignore
            )
        )

    def _xr_filter_by_date(self, data: xr.Dataset, col: str):
        return data.sel({col: slice(self.config.start_date, self.config.end_date)})

    @data.setter
    def data(self, data: xr.Dataset | pl.LazyFrame):
        if isinstance(data, xr.Dataset):
            self._data = self._xr_filter_by_date(data, "timestamp")
            if self.config.symbol is not None:
                self._data = self._data.sel(symbol=self.config.symbol)
        elif isinstance(data, pl.LazyFrame):
            self._data = self._pl_filter_by_date(data, "timestamp")
            if self.config.symbol is not None:
                self._data = self._data.filter(
                    pl.col("symbol").is_in(self.config.symbol)
                )
        else:
            raise ValueError(f"backend: {self.config.backend} is not supported")

    @property
    def config(self) -> FactorConfig:
        return self._config

    @config.setter
    def config(self, config: FactorConfig):
        if config.start_date == "":
            config.start_date = Date.START_DATE
        if config.end_date == "":
            config.end_date = Date.END_DATE

        start_date = pd.to_datetime(config.start_date)  # type: ignore
        start_date = start_date - pd.DateOffset(days=config.window)
        config.dataset.config.start_date = start_date.strftime("%Y-%m-%d")
        config.dataset.config.end_date = config.end_date

        self._config = config

    @property
    def raw_factor_data(self) -> dict[str, np.ndarray]:
        return self._raw_factor_data

    @raw_factor_data.setter
    def raw_factor_data(self, raw_factor_data: dict[str, np.ndarray]):
        self._raw_factor_data = raw_factor_data

    @property
    def symbols(self) -> list[str]:
        return self._symbols

    @symbols.setter
    def symbols(self, symbols: list[str]):
        self._symbols = symbols

    @property
    def timestamps(self) -> pl.Series:
        return self._timestamps

    @timestamps.setter
    def timestamps(self, timestamps: pl.Series):
        self._timestamps = timestamps

    @property
    def stream_context(self):
        return self._stream_context

    @stream_context.setter
    def stream_context(self, stream_context):
        self._stream_context = stream_context

    def get_stream_context(self):
        try:
            return self.stream_context
        except AttributeError:
            return None

    def register_stream_context(self, stream_context):
        self.stream_context = stream_context

    def read_pl(self) -> Self:
        data = pl.scan_parquet(self.config.file_path)
        data = data.drop(["year", "month"])
        self.data = data
        return self

    def read_xr(self) -> Self:
        self.data = xr.open_dataset(self.config.file_path)
        return self

    def save(self) -> None:
        logger.info(f"Saving {self.config.file_path}")

        if isinstance(self.data, xr.Dataset):
            Path(self.config.file_path).parent.mkdir(parents=True, exist_ok=True)
            self.data.to_netcdf(self.config.file_path)
        elif isinstance(self.data, pl.LazyFrame):
            self.data.collect().write_parquet(
                self.config.file_path, partition_by=["symbol", "year", "month"]
            )
        else:
            raise ValueError(f"data type {type(self.data)} is not supported")

    def get_data(self) -> xr.Dataset | pl.LazyFrame:
        return self.data

    def get_raw_factor_data(self) -> dict[str, np.ndarray]:
        return self.raw_factor_data

    def to_xarray(self) -> Self:
        logger.info("Transforming data to xarray dataset")
        ds = xr.Dataset(
            {
                k: (["timestamp", "symbol"], v[:, : len(self.symbols)])
                for k, v in self.raw_factor_data.items()
            },
            coords={
                "timestamp": self.timestamps.to_numpy(),
                "symbol": self.symbols,
            },
        )
        self.data = ds
        return self

    def to_polars(self) -> Self:
        logger.info("Transforming data to polars lazyframe")

        dfs = []
        for k, v in self.raw_factor_data.items():
            res_df = pl.DataFrame(v)
            # 最后一列是填充0的列,去掉
            n = len(res_df.columns) - len(self.symbols)
            if n > 0:
                res_df = res_df.drop(res_df.columns[-n:])
            res_df.columns = self.symbols
            res_df = res_df.lazy().with_columns(timestamp=self.timestamps)
            res_df = res_df.unpivot(
                value_name=k, variable_name="symbol", index="timestamp"
            )
            dfs.append(res_df)

        data = pl.concat(dfs, how="align")
        data = data.with_columns(
            pl.col("timestamp").dt.year().alias("year"),
            pl.col("timestamp").dt.month().alias("month"),
        )
        self.data = data
        return self

    @abstractmethod
    def calc(self, force) -> Self: ...

    @abstractmethod
    def make(self): ...

    @abstractmethod
    def make_stream(self): ...
