from abc import ABC, abstractmethod
from pathlib import Path
import polars as pl
import pandas as pd
from loguru import logger

# from prefect import flow, task

from base.config import DatasetConfig
from enums.constant import Date


class DatasetPl(ABC):
    @property
    def data(self) -> pl.LazyFrame:
        return self._data

    def _filter_by_date(self, data: pl.LazyFrame, col: str) -> pl.LazyFrame:
        data = data.filter(
            pl.col(col).is_between(
                pl.lit(pd.to_datetime(self.config.start_date)),  # type: ignore
                pl.lit(pd.to_datetime(self.config.end_date)),  # type: ignore
            )
        )
        return data

    @data.setter
    def data(self, data: pl.LazyFrame):
        if self.config.data_type == "kline":
            self._data = self._filter_by_date(data, "Open time")
        else:
            raise ValueError("Invalid data type")

        if self.config.symbol is not None:
            self._data = self._data.filter(pl.col("symbol").is_in(self.config.symbol))

    @property
    def config(self) -> DatasetConfig:
        return self._config

    @config.setter
    def config(self, config: DatasetConfig):
        if config.start_date == "":
            config.start_date = Date.START_DATE
        if config.end_date == "":
            config.end_date = Date.END_DATE

        self._config = config

    def read(self):
        if not Path(self.config.pqt_file_path).exists():
            raise FileNotFoundError(f"{self.config.pqt_file_path} does not exist")
        logger.info(f"Reading {self.config.pqt_file_path}")

        self.data = pl.scan_parquet(self.config.pqt_file_path).drop(["year", "month"])
        return self

    def save(self):
        logger.info(f"Saving {self.config.pqt_file_path}")
        self.data.collect().write_parquet(
            self.config.pqt_file_path, partition_by=["symbol", "year", "month"]
        )

    def get_data(self) -> pl.LazyFrame:
        return self.data

    @abstractmethod
    def from_csv(self) -> "DatasetPl": ...

    @abstractmethod
    def to_kunquant(self) -> tuple[dict, list[str], pl.Series]: ...
