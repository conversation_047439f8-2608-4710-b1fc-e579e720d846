from dataset.spot import SpotKlineDataset
from config import alpha101_config, alpha158_config, spot_kline_config
from factor.alpha101 import Alpha101SpotKline
from factor.alpha158 import Alpha158SpotKline
from utils.preprocess import clean_data
import numpy as np

data = SpotKlineDataset(spot_kline_config("2025-01-01"))
df = data.read().get_data()
alpha101 = Alpha101SpotKline(alpha101_config())
df = df.rename(
    mapping={
        "Quote asset volume": "amount",
        "Open time": "timestamp",
        "Open": "open",
        "High": "high",
        "Low": "low",
        "Close": "close",
        "Volume": "volume",
    }
)
df = df.drop(
    [
        "Close time",
        "Number of trades",
        "Taker buy base asset volume",
        "Taker buy quote asset volume",
        "Ignore",
    ]
)
df = clean_data(df)
df = df.collect()

from KunQuant.jit import cfake
from KunQuant.Driver import KunCompilerConfig
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined import Alpha101, Alpha158
import KunQuant.runner.KunRunner as kr


class Test:
    def __init__(self) -> None:
        self.stream = None

    def get_stream(self):
        return self.stream

    def create_stream(self):
        lib = kr.Library.load(
            str(
                "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
            )
        )
        modu = lib.getModule("alpha101_stream")
        # executor = kr.createMultiThreadExecutor(32)
        executor = kr.createSingleThreadExecutor()
        stream = kr.StreamContext(executor, modu, 8)
        return stream


s = Test()

stream = s.create_stream()


# lib = kr.Library.load(
#     str(
#         "/home/<USER>/projects/crypto_quant/compiled_factor/alpha101_stream/alpha101_stream.so"
#     )
# )
# modu = lib.getModule("alpha101_stream")
# executor = kr.createMultiThreadExecutor(32)
# # executor = kr.createSingleThreadExecutor()
# stream = kr.StreamContext(executor, modu, 8)


buffer_name_to_id = dict()
for name in ["high", "low", "close", "open", "volume", "amount"]:
    buffer_name_to_id[name] = stream.queryBufferHandle(name)
for name in ["alpha002", "alpha001", "alpha003"]:
    buffer_name_to_id[name] = stream.queryBufferHandle(name)


import polars as pl

for _, d in df.group_by("timestamp"):
    symbols = []
    data = {}
    for c in ["open", "high", "low", "close", "volume", "amount"]:
        data[c] = d.select(c).to_numpy().astype("float32").squeeze()

    stream.pushData(buffer_name_to_id["high"], data["high"])
    stream.pushData(buffer_name_to_id["low"], data["low"])
    stream.pushData(buffer_name_to_id["close"], data["close"])
    stream.pushData(buffer_name_to_id["open"], data["open"])
    stream.pushData(buffer_name_to_id["volume"], data["volume"])
    stream.pushData(buffer_name_to_id["amount"], data["amount"])
    stream.run()

    print(stream.getCurrentBuffer(buffer_name_to_id["alpha003"])[:])
    # break
