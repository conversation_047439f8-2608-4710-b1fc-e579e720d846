import polars as pl
import pandas as pd
from prefect import task, flow
from prefect.futures import wait
from pathlib import Path
from KunQuant.jit import cfake
from copy import copy
from KunQuant.Driver import KunCompilerConfig
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined import Alpha101, Alpha158
import KunQuant.runner.KunRunner as kr
import numpy as np
from typing import Self
from loguru import logger

from base.factor import FactorKunQuant
from base.config import FactorConfig, DatasetConfig
from dataset.spot import SpotKlineDataset


class Alpha101SpotKline(FactorKunQuant):

    def __init__(self, factor_config: FactorConfig):
        self.config: FactorConfig = factor_config

    # @task(name="calc_alpha101_spot")
    def calc(self) -> Self:
        self.config.dataset.read()
        input_dict, symbols, timestamp = self.config.dataset.to_kunquant()
        # [stocks//8, time, 8]
        num_time = input_dict["open"].shape[1]
        so_file = Path(self.config.so_path)
        if not so_file.exists():
            self.make()

        lib = kr.Library.load(str(so_file))
        modu = lib.getModule("alpha101")

        logger.info("Calculating alpha101...")
        executor = kr.createMultiThreadExecutor(self.config.njobs)
        out: dict = kr.runGraph(executor, modu, input_dict, 0, num_time)
        self.raw_factor_data = out
        self.timestamps = timestamp
        self.symbols = symbols
        return self

    def init_stream(self, factor_names: list, num_symbols: int) -> dict:
        so_file = Path(self.config.so_stream_path)
        if not so_file.exists():
            self.make_stream()

        lib = kr.Library.load(str(so_file))
        modu = lib.getModule("alpha101_stream")
        executor = kr.createMultiThreadExecutor(self.config.njobs)
        stream = kr.StreamContext(executor, modu, num_symbols)

        buffer_name_to_id = {}
        for name in ["high", "low", "close", "open", "volume", "amount"]:
            buffer_name_to_id[name] = stream.queryBufferHandle(name)
        for name in ["alpha001"]:
            buffer_name_to_id[name] = stream.queryBufferHandle(name)

        self.stream_context = stream
        return buffer_name_to_id

    def calc_stream(
        self,
        data: dict,
        factor_names: list[str],
        num_symbols: int = 8,
    ):
        self.flag = False
        if not self.flag:
            buffer_name_to_id = self.init_stream(factor_names, num_symbols)
            self.flag = True
        self.stream_context.pushData(buffer_name_to_id["high"], data["high"])
        self.stream_context.pushData(buffer_name_to_id["low"], data["low"])
        self.stream_context.pushData(buffer_name_to_id["close"], data["close"])
        self.stream_context.pushData(buffer_name_to_id["open"], data["open"])
        self.stream_context.pushData(buffer_name_to_id["volume"], data["volume"])
        self.stream_context.pushData(buffer_name_to_id["amount"], data["amount"])
        # self.stream_context.run()
        # stream.pushData(buffer_name_to_id["high"], high)
        # stream.pushData(buffer_name_to_id["low"], low)
        # stream.pushData(buffer_name_to_id["close"], close)
        # stream.pushData(buffer_name_to_id["open"], open)
        # stream.pushData(buffer_name_to_id["volume"], volume)
        # stream.pushData(buffer_name_to_id["amount"], amount)
        # stream.run()

        out_dict = {}
        # for factor in ["alpha001"]:
        #     alpha = self.stream_context.getCurrentBuffer(buffer_name_to_id[factor])[:]
        #     out_dict[factor] = alpha
        self.raw_factor_data = out_dict

    # @task
    def make(self) -> None:
        builder = Builder()
        with builder:
            close = Input("close")
            low = Input("low")
            high = Input("high")
            vopen = Input("open")
            amount = Input("amount")
            vol = Input("volume")
            all_data = Alpha101.AllData(
                low=low, high=high, close=close, open=vopen, amount=amount, volume=vol
            )
            for alpha in Alpha101.all_alpha:
                Output(alpha(all_data), alpha.__name__)
        f = Function(builder.ops)
        cfake.compileit(
            [
                (
                    "alpha101",
                    f,
                    KunCompilerConfig(
                        blocking_len=8,
                        partition_factor=8,
                        input_layout="STs",
                        output_layout="TS",
                    ),
                )
            ],
            "alpha101",
            cfake.CppCompilerConfig(),
            tempdir=str(Path(self.config.so_path).parent.parent),
            keep_files=True,
            load=False,
        )

    def make_stream(self):
        builder = Builder()
        with builder:
            close = Input("close")
            low = Input("low")
            high = Input("high")
            vopen = Input("open")
            amount = Input("amount")
            vol = Input("volume")
            all_data = Alpha101.AllData(
                low=low, high=high, close=close, open=vopen, amount=amount, volume=vol
            )
            for alpha in Alpha101.all_alpha:
                Output(alpha(all_data), alpha.__name__)
        f = Function(builder.ops)
        cfake.compileit(
            [
                (
                    "alpha101_stream",
                    f,
                    KunCompilerConfig(
                        # blocking_len=8,
                        # partition_factor=8,
                        input_layout="STREAM",
                        output_layout="STREAM",
                        options={"opt_reduce": False, "fast_log": True},
                    ),
                )
            ],
            "alpha101_stream",
            cfake.CppCompilerConfig(),
            tempdir=str(Path(self.config.so_path).parent.parent),
            keep_files=True,
            load=False,
        )
