import polars as pl
import pandas as pd
from prefect import task, flow
from prefect.futures import wait
from pathlib import Path
from KunQuant.jit import cfake
from KunQuant.Driver import KunCompilerConfig
from KunQuant.Op import Builder, Input, Output
from KunQuant.Stage import Function
from KunQuant.predefined import Alpha101, Alpha158
import KunQuant.runner.KunRunner as kr
import numpy as np
from loguru import logger

from base.factor import FactorKunQuant
from dataset.spot import SpotKlineDataset
from base.config import FactorConfig, DatasetConfig


class Alpha158SpotKline(FactorKunQuant):

    def __init__(self, factor_config: FactorConfig):
        self.config: FactorConfig = factor_config

    # @task(name="calc_alpha158_spot")
    def calc(self):
        self.config.dataset.read()
        input_dict, symbols, timestamp = self.config.dataset.to_kunquant()
        # [stocks//8, time, 8]
        num_time = input_dict["open"].shape[1]
        so_file = Path(self.config.so_path)
        if not so_file.exists():
            self.make()

        lib = kr.Library.load(str(so_file))
        modu = lib.getModule("alpha158")

        logger.info("Calculating alpha158...")
        executor = kr.createMultiThreadExecutor(self.config.njobs)
        out = kr.runGraph(executor, modu, input_dict, 0, num_time)
        self.raw_factor_data = out
        self.timestamps = timestamp
        self.symbols = symbols
        return self

    # @task
    def make(self) -> None:
        builder = Builder()
        with builder:
            close = Input("close")
            low = Input("low")
            high = Input("high")
            vopen = Input("open")
            amount = Input("amount")
            vol = Input("volume")
            all_data = Alpha158.AllData(
                low=low, high=high, close=close, open=vopen, amount=amount, volume=vol
            )
            alpha158, names = all_data.build(
                {
                    "kbar": {},  # 是否使用K线特征
                    "price": {
                        "windows": [0, 1, 2, 3, 4],
                        "feature": [
                            ("OPEN", all_data.open),
                            ("HIGH", all_data.high),
                            ("LOW", all_data.low),
                            ("CLOSE", all_data.close),
                            ("VWAP", all_data.vwap),
                        ],
                    },
                    "volume": {
                        "windows": [0, 1, 2, 3, 4],
                    },
                    "rolling": {  # 是否使用滚动窗口特征
                        "windows": [5, 10, 20, 30, 60],  # 滚动窗口大小
                    },
                }
            )
            for v, k in zip(alpha158, names):
                Output(v, k)
        f = Function(builder.ops)
        cfake.compileit(
            [
                (
                    "alpha158",
                    f,
                    KunCompilerConfig(input_layout="STs", output_layout="TS"),
                )
            ],
            "alpha158",
            cfake.CppCompilerConfig(),
            tempdir=str(Path(self.config.so_path).parent.parent),
            keep_files=True,
            load=False,
        )
        cfake.compileit(
            [
                (
                    "alpha158_stream",
                    f,
                    KunCompilerConfig(
                        input_layout="STREAM",
                        output_layout="STREAM",
                        options={"opt_reduce": False, "fast_log": True},
                    ),
                )
            ],
            "alpha158_stream",
            cfake.CppCompilerConfig(),
            tempdir=str(Path(self.config.so_path).parent.parent),
            keep_files=True,
            load=False,
        )
