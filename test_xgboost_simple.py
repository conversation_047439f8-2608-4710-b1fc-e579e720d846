#!/usr/bin/env python3
"""
简化的XGBoost测试脚本
用于验证数据预处理和模型训练功能
"""

import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

import polars as pl
import xarray as xr
import numpy as np

from dataset.spot import SpotKlineDataset
from factor.alpha158 import Alpha158SpotKline
from utils.preprocess import DataPreprocessor
from models.xgboost_predictor import XGBoostPredictor
import config


def test_data_preprocessing():
    """测试数据预处理功能"""
    print("=" * 50)
    print("测试数据预处理功能")
    print("=" * 50)
    
    # 使用较短的时间范围进行测试
    start_date = "2022-01-01"
    end_date = "2024-01-31"
    
    try:
        # 加载因子数据
        print("1. 加载Alpha158因子数据...")
        alpha158_factor = Alpha158SpotKline(config.alpha158_config(start_date, end_date))
        factor_data = alpha158_factor.get()
        
        print(f"   因子数据维度: {factor_data.dims}")
        print(f"   因子数量: {len(factor_data.data_vars)}")
        
        # 测试数据预处理
        print("\n2. 测试数据预处理...")
        preprocessor = DataPreprocessor(factor_data)
        
        # 原始数据摘要
        print("   原始数据摘要:")
        preprocessor.summary()
        
        # 执行预处理
        print("\n   执行预处理流水线...")
        processed_data = (preprocessor
                         .handle_missing_values(method='forward_fill')
                         .remove_outliers(method='iqr', threshold=3.0)
                         .normalize_features(method='standard', per_symbol=True)
                         .get_processed_data())
        
        print("   预处理完成!")
        print(f"   处理后数据维度: {processed_data.dims}")
        
        return True, factor_data, processed_data
        
    except Exception as e:
        print(f"   数据预处理测试失败: {e}")
        return False, None, None


def test_label_creation():
    """测试标签创建功能"""
    print("\n" + "=" * 50)
    print("测试标签创建功能")
    print("=" * 50)
    
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    try:
        # 加载Spot数据
        print("1. 加载Spot数据...")
        spot_dataset = SpotKlineDataset(config.spot_kline_config(start_date, end_date))
        spot_data = spot_dataset.get()
        
        print(f"   Spot数据形状: {spot_data.collect().shape}")
        
        # 创建标签
        print("\n2. 创建涨跌标签...")
        labels = spot_data.with_columns(
            pl.when((pl.col('Close') > pl.col('Close').shift(1)).over('symbol'))
            .then(1)
            .otherwise(0)
            .alias('label')
            .fill_null(0)
        ).select(['symbol', 'Open time', 'label']).rename({'Open time': 'timestamp'}).collect()
        
        print(f"   标签数据形状: {labels.shape}")
        print("   标签分布:")
        label_counts = labels['label'].value_counts().sort('label')
        for row in label_counts.iter_rows():
            print(f"     标签 {row[0]}: {row[1]} 个样本")
        
        return True, labels
        
    except Exception as e:
        print(f"   标签创建测试失败: {e}")
        return False, None


def test_model_training(processed_data, labels):
    """测试模型训练功能"""
    print("\n" + "=" * 50)
    print("测试模型训练功能")
    print("=" * 50)
    
    try:
        # 创建预测器
        print("1. 创建XGBoost预测器...")
        predictor = XGBoostPredictor(
            use_gpu=False,  # 为了兼容性，先不使用GPU
            n_splits=2,     # 使用2折交叉验证以加快测试
            random_state=42
        )
        
        # 数据对齐
        print("\n2. 对齐数据...")
        X, y, timestamps, _ = predictor.align_data(processed_data, labels)
        feature_names = list(processed_data.data_vars)
        
        print(f"   对齐后特征矩阵形状: {X.shape}")
        print(f"   对齐后标签向量形状: {y.shape}")
        print(f"   特征数量: {len(feature_names)}")
        print(f"   标签分布: {np.bincount(y.astype(int))}")
        
        # 检查数据质量
        if X.shape[0] < 100:
            print("   警告: 样本数量太少，跳过模型训练")
            return True
        
        # 训练模型
        print("\n3. 训练模型...")
        training_results = predictor.train_with_cv(
            X=X,
            y=y,
            timestamps=timestamps,
            feature_names=feature_names
        )
        
        print("\n4. 训练结果:")
        for metric, scores in training_results['cv_scores'].items():
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            print(f"   {metric}: {mean_score:.4f} ± {std_score:.4f}")
        
        # 测试预测
        print("\n5. 测试预测...")
        test_size = min(100, len(X))
        X_test = X[:test_size]
        y_test = y[:test_size]
        
        y_pred_proba = predictor.predict(X_test, feature_names)
        y_pred = predictor.predict_binary(X_test, threshold=0.5, feature_names=feature_names)
        
        accuracy = np.mean(y_pred == y_test)
        print(f"   测试准确率: {accuracy:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   模型训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始XGBoost功能测试...")
    
    # 测试数据预处理
    preprocess_success, factor_data, processed_data = test_data_preprocessing()
    if not preprocess_success:
        print("数据预处理测试失败，退出")
        return
    
    # 测试标签创建
    label_success, labels = test_label_creation()
    if not label_success:
        print("标签创建测试失败，退出")
        return
    
    # 测试模型训练
    if processed_data is not None and labels is not None:
        model_success = test_model_training(processed_data, labels)
        if not model_success:
            print("模型训练测试失败")
            return
    
    print("\n" + "=" * 50)
    print("所有测试完成!")
    print("=" * 50)
    print("✓ 数据预处理功能正常")
    print("✓ 标签创建功能正常")
    print("✓ 模型训练功能正常")
    print("=" * 50)


if __name__ == "__main__":
    main()
